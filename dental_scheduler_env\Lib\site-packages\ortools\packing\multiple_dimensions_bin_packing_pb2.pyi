"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class MultipleDimensionsBinPackingShape(google.protobuf.message.Message):
    """A Multiple Dimension BinPacking problem.
    It supports the following file format:
     - 2bp:
       see http://or.dei.unibo.it/library/two-dimensional-bin-packing-problem

    The generated problems have the following characteristics:

    You have identical boxes with n dimensions. The size of one box is stored in
    the field box_shape().
    You need to fit all items into the minimum number of boxes. Each item has the
    same number of dimensions and one or more possible shapes (this usually means
    that you can rotate the item).
    Each item must not overlap (in n dimensions) with any other item.

    The shape of one item. each edge is parallel to one axis of a dimension.
    One shape cannot be rotated, the item itself will contain multiple rotated
    shapes.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DIMENSIONS_FIELD_NUMBER: builtins.int
    @property
    def dimensions(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        dimensions: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["dimensions", b"dimensions"]) -> None: ...

global___MultipleDimensionsBinPackingShape = MultipleDimensionsBinPackingShape

@typing.final
class MultipleDimensionsBinPackingItem(google.protobuf.message.Message):
    """Items with multiple shapes often means that they can be rotated."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SHAPES_FIELD_NUMBER: builtins.int
    VALUE_FIELD_NUMBER: builtins.int
    value: builtins.int
    """The value of the item (useful when solving the problem as a knapsack)."""
    @property
    def shapes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MultipleDimensionsBinPackingShape]:
        """All available shapes of the item."""

    def __init__(
        self,
        *,
        shapes: collections.abc.Iterable[global___MultipleDimensionsBinPackingShape] | None = ...,
        value: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["shapes", b"shapes", "value", b"value"]) -> None: ...

global___MultipleDimensionsBinPackingItem = MultipleDimensionsBinPackingItem

@typing.final
class MultipleDimensionsBinPackingProblem(google.protobuf.message.Message):
    """The full problem: the enclosing box and the list of items."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BOX_SHAPE_FIELD_NUMBER: builtins.int
    ITEMS_FIELD_NUMBER: builtins.int
    @property
    def box_shape(self) -> global___MultipleDimensionsBinPackingShape:
        """The enclosing shape."""

    @property
    def items(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MultipleDimensionsBinPackingItem]:
        """All available items of the problem."""

    def __init__(
        self,
        *,
        box_shape: global___MultipleDimensionsBinPackingShape | None = ...,
        items: collections.abc.Iterable[global___MultipleDimensionsBinPackingItem] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["box_shape", b"box_shape"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["box_shape", b"box_shape", "items", b"items"]) -> None: ...

global___MultipleDimensionsBinPackingProblem = MultipleDimensionsBinPackingProblem
