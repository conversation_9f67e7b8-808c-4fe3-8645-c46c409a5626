from _typeshed import Incomplete

class _SwigNonDynamicMeta(type):
    __setattr__: Incomplete

class DefaultPhaseParameters:
    thisown: Incomplete
    CHOOSE_MAX_SUM_IMPACT: Incomplete
    CHOOSE_MAX_AVERAGE_IMPACT: Incomplete
    CHOOSE_MAX_VALUE_IMPACT: Incomplete
    SELECT_MIN_IMPACT: Incomplete
    SELECT_MAX_IMPACT: Incomplete
    NONE: Incomplete
    NORMAL: Incomplete
    VERBOSE: Incomplete
    var_selection_schema: Incomplete
    value_selection_schema: Incomplete
    initialization_splits: Incomplete
    run_all_heuristics: Incomplete
    heuristic_period: Incomplete
    heuristic_num_failures_limit: Incomplete
    persistent_impact: Incomplete
    random_seed: Incomplete
    display_level: Incomplete
    decision_builder: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete

class Solver:
    thisown: Incomplete
    INT_VAR_DEFAULT: Incomplete
    INT_VAR_SIMPLE: Incomplete
    CHOOSE_FIRST_UNBOUND: Incomplete
    CHOOSE_RANDOM: Incomplete
    CHOOSE_MIN_SIZE_LOWEST_MIN: Incomplete
    CHOOSE_MIN_SIZE_HIGHEST_MIN: Incomplete
    CHOOSE_MIN_SIZE_LOWEST_MAX: Incomplete
    CHOOSE_MIN_SIZE_HIGHEST_MAX: Incomplete
    CHOOSE_LOWEST_MIN: Incomplete
    CHOOSE_HIGHEST_MAX: Incomplete
    CHOOSE_MIN_SIZE: Incomplete
    CHOOSE_MAX_SIZE: Incomplete
    CHOOSE_MAX_REGRET_ON_MIN: Incomplete
    CHOOSE_PATH: Incomplete
    INT_VALUE_DEFAULT: Incomplete
    INT_VALUE_SIMPLE: Incomplete
    ASSIGN_MIN_VALUE: Incomplete
    ASSIGN_MAX_VALUE: Incomplete
    ASSIGN_RANDOM_VALUE: Incomplete
    ASSIGN_CENTER_VALUE: Incomplete
    SPLIT_LOWER_HALF: Incomplete
    SPLIT_UPPER_HALF: Incomplete
    SEQUENCE_DEFAULT: Incomplete
    SEQUENCE_SIMPLE: Incomplete
    CHOOSE_MIN_SLACK_RANK_FORWARD: Incomplete
    CHOOSE_RANDOM_RANK_FORWARD: Incomplete
    INTERVAL_DEFAULT: Incomplete
    INTERVAL_SIMPLE: Incomplete
    INTERVAL_SET_TIMES_FORWARD: Incomplete
    INTERVAL_SET_TIMES_BACKWARD: Incomplete
    TWOOPT: Incomplete
    OROPT: Incomplete
    RELOCATE: Incomplete
    EXCHANGE: Incomplete
    CROSS: Incomplete
    MAKEACTIVE: Incomplete
    MAKEINACTIVE: Incomplete
    MAKECHAININACTIVE: Incomplete
    SWAPACTIVE: Incomplete
    EXTENDEDSWAPACTIVE: Incomplete
    PATHLNS: Incomplete
    FULLPATHLNS: Incomplete
    UNACTIVELNS: Incomplete
    INCREMENT: Incomplete
    DECREMENT: Incomplete
    SIMPLELNS: Incomplete
    GE: Incomplete
    LE: Incomplete
    EQ: Incomplete
    DELAYED_PRIORITY: Incomplete
    VAR_PRIORITY: Incomplete
    NORMAL_PRIORITY: Incomplete
    def __init__(self, *args) -> None: ...
    __swig_destroy__: Incomplete
    def Parameters(self): ...
    @staticmethod
    def DefaultSolverParameters(): ...
    def AddConstraint(self, c): ...
    def Solve(self, *args): ...
    def NewSearch(self, *args): ...
    def NextSolution(self): ...
    def RestartSearch(self): ...
    def EndSearch(self): ...
    def SolveAndCommit(self, *args): ...
    def CheckAssignment(self, solution): ...
    def CheckConstraint(self, ct): ...
    def Fail(self): ...
    @staticmethod
    def MemoryUsage(): ...
    def WallTime(self): ...
    def Branches(self): ...
    def Solutions(self): ...
    def Failures(self): ...
    def AcceptedNeighbors(self): ...
    def Stamp(self): ...
    def FailStamp(self): ...
    def IntVar(self, *args): ...
    def BoolVar(self, *args): ...
    def IntConst(self, *args): ...
    def Sum(self, vars): ...
    def ScalProd(self, *args): ...
    def MonotonicElement(self, values, increasing, index): ...
    def Element(self, *args): ...
    def IndexExpression(self, vars, value): ...
    def Min(self, *args): ...
    def Max(self, *args): ...
    def ConvexPiecewiseExpr(self, expr, early_cost, early_date, late_date, late_cost): ...
    def SemiContinuousExpr(self, expr, fixed_charge, step): ...
    def ConditionalExpression(self, condition, expr, unperformed_value): ...
    def TrueConstraint(self): ...
    def FalseConstraint(self, *args): ...
    def IsEqualCstCt(self, var, value, boolvar): ...
    def IsEqualCstVar(self, var, value): ...
    def IsEqualCt(self, v1, v2, b): ...
    def IsEqualVar(self, v1, v2): ...
    def IsDifferentCstCt(self, var, value, boolvar): ...
    def IsDifferentCstVar(self, var, value): ...
    def IsDifferentVar(self, v1, v2): ...
    def IsDifferentCt(self, v1, v2, b): ...
    def IsLessOrEqualCstCt(self, var, value, boolvar): ...
    def IsLessOrEqualCstVar(self, var, value): ...
    def IsLessOrEqualVar(self, left, right): ...
    def IsLessOrEqualCt(self, left, right, b): ...
    def IsGreaterOrEqualCstCt(self, var, value, boolvar): ...
    def IsGreaterOrEqualCstVar(self, var, value): ...
    def IsGreaterOrEqualVar(self, left, right): ...
    def IsGreaterOrEqualCt(self, left, right, b): ...
    def IsGreaterCstCt(self, v, c, b): ...
    def IsGreaterCstVar(self, var, value): ...
    def IsGreaterVar(self, left, right): ...
    def IsGreaterCt(self, left, right, b): ...
    def IsLessCstCt(self, v, c, b): ...
    def IsLessCstVar(self, var, value): ...
    def IsLessVar(self, left, right): ...
    def IsLessCt(self, left, right, b): ...
    def SumLessOrEqual(self, vars, cst): ...
    def SumGreaterOrEqual(self, vars, cst): ...
    def SumEquality(self, *args): ...
    def ScalProdEquality(self, *args): ...
    def ScalProdGreaterOrEqual(self, *args): ...
    def ScalProdLessOrEqual(self, *args): ...
    def MinEquality(self, vars, min_var): ...
    def MaxEquality(self, vars, max_var): ...
    def ElementEquality(self, *args): ...
    def AbsEquality(self, var, abs_var): ...
    def IndexOfConstraint(self, vars, index, target): ...
    def ConstraintInitialPropagateCallback(self, ct): ...
    def DelayedConstraintInitialPropagateCallback(self, ct): ...
    def ClosureDemon(self, closure): ...
    def BetweenCt(self, expr, l, u): ...
    def IsBetweenCt(self, expr, l, u, b): ...
    def IsBetweenVar(self, v, l, u): ...
    def MemberCt(self, *args): ...
    def NotMemberCt(self, *args): ...
    def IsMemberCt(self, *args): ...
    def IsMemberVar(self, *args): ...
    def Count(self, *args): ...
    def Distribute(self, *args): ...
    def Deviation(self, vars, deviation_var, total_sum): ...
    def AllDifferent(self, *args): ...
    def AllDifferentExcept(self, vars, escape_value): ...
    def SortingConstraint(self, vars, sorted): ...
    def LexicalLess(self, left, right): ...
    def LexicalLessOrEqual(self, left, right): ...
    def InversePermutationConstraint(self, left, right): ...
    def NullIntersect(self, first_vars, second_vars): ...
    def NullIntersectExcept(self, first_vars, second_vars, escape_value): ...
    def Circuit(self, nexts): ...
    def SubCircuit(self, nexts): ...
    def DelayedPathCumul(self, nexts, active, cumuls, transits): ...
    def PathCumul(self, *args): ...
    def AllowedAssignments(self, *args): ...
    def TransitionConstraint(self, *args): ...
    def NonOverlappingBoxesConstraint(self, *args): ...
    def Pack(self, vars, number_of_bins): ...
    def FixedDurationIntervalVar(self, *args): ...
    def FixedInterval(self, start, duration, name): ...
    def IntervalVar(self, start_min, start_max, duration_min, duration_max, end_min, end_max, optional, name): ...
    def MirrorInterval(self, interval_var): ...
    def FixedDurationStartSyncedOnStartIntervalVar(self, interval_var, duration, offset): ...
    def FixedDurationStartSyncedOnEndIntervalVar(self, interval_var, duration, offset): ...
    def FixedDurationEndSyncedOnStartIntervalVar(self, interval_var, duration, offset): ...
    def FixedDurationEndSyncedOnEndIntervalVar(self, interval_var, duration, offset): ...
    def IntervalRelaxedMin(self, interval_var): ...
    def IntervalRelaxedMax(self, interval_var): ...
    def TemporalDisjunction(self, *args): ...
    def DisjunctiveConstraint(self, intervals, name): ...
    def Cumulative(self, *args): ...
    def Cover(self, vars, target_var): ...
    def Assignment(self, *args): ...
    def FirstSolutionCollector(self, *args): ...
    def LastSolutionCollector(self, *args): ...
    def BestValueSolutionCollector(self, *args): ...
    def AllSolutionCollector(self, *args): ...
    def Minimize(self, v, step): ...
    def Maximize(self, v, step): ...
    def Optimize(self, maximize, v, step): ...
    def WeightedMinimize(self, *args): ...
    def WeightedMaximize(self, *args): ...
    def WeightedOptimize(self, *args): ...
    def TabuSearch(self, maximize, objective, step, vars, keep_tenure, forbid_tenure, tabu_factor): ...
    def SimulatedAnnealing(self, maximize, v, step, initial_temperature): ...
    def LubyRestart(self, scale_factor): ...
    def ConstantRestart(self, frequency): ...
    def TimeLimit(self, *args): ...
    def BranchesLimit(self, branches): ...
    def FailuresLimit(self, failures): ...
    def SolutionsLimit(self, solutions): ...
    def Limit(self, *args): ...
    def CustomLimit(self, limiter): ...
    def SearchLog(self, *args): ...
    def SearchTrace(self, prefix): ...
    def PrintModelVisitor(self): ...
    def StatisticsModelVisitor(self): ...
    def AssignVariableValue(self, var, val): ...
    def VariableLessOrEqualValue(self, var, value): ...
    def VariableGreaterOrEqualValue(self, var, value): ...
    def SplitVariableDomain(self, var, val, start_with_lower_half): ...
    def AssignVariableValueOrFail(self, var, value): ...
    def AssignVariablesValues(self, vars, values): ...
    def FailDecision(self): ...
    def Decision(self, apply, refute): ...
    def Compose(self, dbs): ...
    def Try(self, dbs): ...
    def DefaultPhase(self, *args): ...
    def ScheduleOrPostpone(self, var, est, marker): ...
    def ScheduleOrExpedite(self, var, est, marker): ...
    def RankFirstInterval(self, sequence, index): ...
    def RankLastInterval(self, sequence, index): ...
    def Phase(self, *args): ...
    def DecisionBuilderFromAssignment(self, assignment, db, vars): ...
    def ConstraintAdder(self, ct): ...
    def SolveOnce(self, db, monitors): ...
    def NestedOptimize(self, *args): ...
    def RestoreAssignment(self, assignment): ...
    def StoreAssignment(self, assignment): ...
    def Operator(self, *args): ...
    def RandomLnsOperator(self, *args): ...
    def MoveTowardTargetOperator(self, *args): ...
    def ConcatenateOperators(self, *args): ...
    def RandomConcatenateOperators(self, *args): ...
    def NeighborhoodLimit(self, op, limit): ...
    def LocalSearchPhase(self, *args): ...
    def LocalSearchPhaseParameters(self, *args): ...
    def TopProgressPercent(self): ...
    def SearchDepth(self): ...
    def SearchLeftDepth(self): ...
    def SolveDepth(self): ...
    def Rand64(self, size): ...
    def Rand32(self, size): ...
    def ReSeed(self, seed): ...
    def LocalSearchProfile(self): ...
    def Constraints(self): ...
    def Accept(self, visitor): ...
    def FinishCurrentSearch(self): ...
    def RestartCurrentSearch(self): ...
    def ShouldFail(self): ...
    def Add(self, ct) -> None: ...
    def TreeNoCycle(self, nexts, active, callback: int = 0): ...
    def SearchLogWithCallback(self, period, callback): ...
    def ElementFunction(self, values, index): ...
    def VarEvalValStrPhase(self, vars, var_evaluator, val_str): ...
    def VarStrValEvalPhase(self, vars, var_str, val_eval): ...
    def VarEvalValEvalPhase(self, vars, var_eval, val_eval): ...
    def VarStrValEvalTieBreakPhase(self, vars, var_str, val_eval, tie_breaker): ...
    def VarEvalValEvalTieBreakPhase(self, vars, var_eval, val_eval, tie_breaker): ...
    def EvalEvalStrPhase(self, vars, evaluator, str): ...
    def EvalEvalStrTieBreakPhase(self, vars, evaluator, tie_breaker, str): ...
    def GuidedLocalSearch(self, maximize, objective, objective_function, step, vars, penalty_factor): ...
    def SumObjectiveFilter(self, vars, values, filter_enum): ...

class BaseObject:
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete
    def DebugString(self): ...
    def __disown__(self): ...

class PropagationBaseObject(BaseObject):
    thisown: Incomplete
    def __init__(self, s) -> None: ...
    __swig_destroy__: Incomplete
    def DebugString(self): ...
    def solver(self): ...
    def Name(self): ...
    def __disown__(self): ...

class Decision(BaseObject):
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete
    def ApplyWrapper(self, s): ...
    def RefuteWrapper(self, s): ...
    def DebugString(self): ...
    def __disown__(self): ...

class DecisionBuilder(BaseObject):
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete
    def NextWrapper(self, s): ...
    def DebugString(self): ...
    def __disown__(self): ...

class Demon(BaseObject):
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete
    def RunWrapper(self, s): ...
    def Priority(self): ...
    def DebugString(self): ...
    def Inhibit(self, s): ...
    def Desinhibit(self, s): ...
    def __disown__(self): ...

class Constraint(PropagationBaseObject):
    thisown: Incomplete
    def __init__(self, solver) -> None: ...
    __swig_destroy__: Incomplete
    def Post(self): ...
    def InitialPropagateWrapper(self): ...
    def DebugString(self): ...
    def Var(self): ...
    def __add__(self, *args): ...
    def __radd__(self, v): ...
    def __sub__(self, *args): ...
    def __rsub__(self, v): ...
    def __mul__(self, *args): ...
    def __rmul__(self, v): ...
    def __floordiv__(self, v): ...
    def __neg__(self): ...
    def __abs__(self): ...
    def Square(self): ...
    def __eq__(self, *args): ...
    def __ne__(self, *args): ...
    def __ge__(self, *args): ...
    def __gt__(self, *args): ...
    def __le__(self, *args): ...
    def __lt__(self, *args): ...
    def MapTo(self, vars): ...
    def IndexOf(self, *args): ...
    def __disown__(self): ...

class SearchMonitor(BaseObject):
    thisown: Incomplete
    kNoProgress: Incomplete
    def __init__(self, s) -> None: ...
    __swig_destroy__: Incomplete
    def EnterSearch(self): ...
    def RestartSearch(self): ...
    def ExitSearch(self): ...
    def BeginNextDecision(self, b): ...
    def EndNextDecision(self, b, d): ...
    def ApplyDecision(self, d): ...
    def RefuteDecision(self, d): ...
    def AfterDecision(self, d, apply): ...
    def BeginFail(self): ...
    def EndFail(self): ...
    def BeginInitialPropagation(self): ...
    def EndInitialPropagation(self): ...
    def AcceptSolution(self): ...
    def AtSolution(self): ...
    def NoMoreSolutions(self): ...
    def LocalOptimum(self): ...
    def AcceptDelta(self, delta, deltadelta): ...
    def AcceptNeighbor(self): ...
    def ProgressPercent(self): ...
    def solver(self): ...
    def __disown__(self): ...

class IntExpr(PropagationBaseObject):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Min(self): ...
    def SetMin(self, m): ...
    def Max(self): ...
    def SetMax(self, m): ...
    def SetRange(self, l, u): ...
    def SetValue(self, v): ...
    def Bound(self): ...
    def IsVar(self): ...
    def Var(self): ...
    def VarWithName(self, name): ...
    def WhenRange(self, *args): ...
    def __add__(self, *args): ...
    def __radd__(self, v): ...
    def __sub__(self, *args): ...
    def __rsub__(self, v): ...
    def __mul__(self, *args): ...
    def __rmul__(self, v): ...
    def __floordiv__(self, *args): ...
    def __mod__(self, *args): ...
    def __neg__(self): ...
    def __abs__(self): ...
    def Square(self): ...
    def __eq__(self, *args): ...
    def __ne__(self, *args): ...
    def __ge__(self, *args): ...
    def __gt__(self, *args): ...
    def __le__(self, *args): ...
    def __lt__(self, *args): ...
    def MapTo(self, vars): ...
    def IndexOf(self, *args): ...
    def IsMember(self, values): ...
    def Member(self, values): ...
    def NotMember(self, starts, ends): ...

class IntVarIterator(BaseObject):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Init(self): ...
    def Ok(self): ...
    def Value(self): ...
    def Next(self): ...
    def DebugString(self): ...
    def __iter__(self): ...
    def next(self): ...
    def __next__(self): ...

class IntVar(IntExpr):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def IsVar(self): ...
    def Var(self): ...
    def Value(self): ...
    def RemoveValue(self, v): ...
    def RemoveInterval(self, l, u): ...
    def RemoveValues(self, values): ...
    def SetValues(self, values): ...
    def WhenBound(self, *args): ...
    def WhenDomain(self, *args): ...
    def Size(self): ...
    def Contains(self, v): ...
    def HoleIteratorAux(self, reversible): ...
    def DomainIteratorAux(self, reversible): ...
    def OldMin(self): ...
    def OldMax(self): ...
    def DomainIterator(self): ...
    def HoleIterator(self): ...

class SolutionCollector(SearchMonitor):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def DebugString(self): ...
    def Add(self, *args): ...
    def AddObjective(self, objective): ...
    def EnterSearch(self): ...
    def SolutionCount(self): ...
    def Solution(self, n): ...
    def WallTime(self, n): ...
    def Branches(self, n): ...
    def Failures(self, n): ...
    def ObjectiveValue(self, n): ...
    def Value(self, n, var): ...
    def StartValue(self, n, var): ...
    def EndValue(self, n, var): ...
    def DurationValue(self, n, var): ...
    def PerformedValue(self, n, var): ...
    def ForwardSequence(self, n, var): ...
    def BackwardSequence(self, n, var): ...
    def Unperformed(self, n, var): ...

class OptimizeVar:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Best(self): ...
    def BeginNextDecision(self, db): ...
    def RefuteDecision(self, d): ...
    def AtSolution(self): ...
    def AcceptSolution(self): ...
    def DebugString(self): ...
    __swig_destroy__: Incomplete

class SearchLimit(SearchMonitor):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    __swig_destroy__: Incomplete
    def Crossed(self): ...
    def Check(self): ...
    def Init(self): ...
    def EnterSearch(self): ...
    def BeginNextDecision(self, b): ...
    def RefuteDecision(self, d): ...
    def DebugString(self): ...

class IntervalVar(PropagationBaseObject):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def StartMin(self): ...
    def StartMax(self): ...
    def SetStartMin(self, m): ...
    def SetStartMax(self, m): ...
    def SetStartRange(self, mi, ma): ...
    def OldStartMin(self): ...
    def OldStartMax(self): ...
    def WhenStartRange(self, *args): ...
    def WhenStartBound(self, *args): ...
    def DurationMin(self): ...
    def DurationMax(self): ...
    def SetDurationMin(self, m): ...
    def SetDurationMax(self, m): ...
    def SetDurationRange(self, mi, ma): ...
    def OldDurationMin(self): ...
    def OldDurationMax(self): ...
    def WhenDurationRange(self, *args): ...
    def WhenDurationBound(self, *args): ...
    def EndMin(self): ...
    def EndMax(self): ...
    def SetEndMin(self, m): ...
    def SetEndMax(self, m): ...
    def SetEndRange(self, mi, ma): ...
    def OldEndMin(self): ...
    def OldEndMax(self): ...
    def WhenEndRange(self, *args): ...
    def WhenEndBound(self, *args): ...
    def MustBePerformed(self): ...
    def MayBePerformed(self): ...
    def CannotBePerformed(self): ...
    def IsPerformedBound(self): ...
    def SetPerformed(self, val): ...
    def WasPerformedBound(self): ...
    def WhenPerformedBound(self, *args): ...
    def WhenAnything(self, *args): ...
    def StartExpr(self): ...
    def DurationExpr(self): ...
    def EndExpr(self): ...
    def PerformedExpr(self): ...
    def SafeStartExpr(self, unperformed_value): ...
    def SafeDurationExpr(self, unperformed_value): ...
    def SafeEndExpr(self, unperformed_value): ...
    def EndsAfterEnd(self, other): ...
    def EndsAfterEndWithDelay(self, other, delay): ...
    def EndsAfterStart(self, other): ...
    def EndsAfterStartWithDelay(self, other, delay): ...
    def EndsAtEnd(self, other): ...
    def EndsAtEndWithDelay(self, other, delay): ...
    def EndsAtStart(self, other): ...
    def EndsAtStartWithDelay(self, other, delay): ...
    def StartsAfterEnd(self, other): ...
    def StartsAfterEndWithDelay(self, other, delay): ...
    def StartsAfterStart(self, other): ...
    def StartsAfterStartWithDelay(self, other, delay): ...
    def StartsAtEnd(self, other): ...
    def StartsAtEndWithDelay(self, other, delay): ...
    def StartsAtStart(self, other): ...
    def StartsAtStartWithDelay(self, other, delay): ...
    def StaysInSync(self, other): ...
    def StaysInSyncWithDelay(self, other, delay): ...
    def EndsAfter(self, date): ...
    def EndsAt(self, date): ...
    def EndsBefore(self, date): ...
    def StartsAfter(self, date): ...
    def StartsAt(self, date): ...
    def StartsBefore(self, date): ...
    def CrossesDate(self, date): ...
    def AvoidsDate(self, date): ...

class SequenceVar(PropagationBaseObject):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def DebugString(self): ...
    def RankFirst(self, index): ...
    def RankNotFirst(self, index): ...
    def RankLast(self, index): ...
    def RankNotLast(self, index): ...
    def Interval(self, index): ...
    def Next(self, index): ...
    def Size(self): ...

class AssignmentElement:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Activate(self): ...
    def Deactivate(self): ...
    def Activated(self): ...
    __swig_destroy__: Incomplete

class IntVarElement(AssignmentElement):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Var(self): ...
    def Min(self): ...
    def SetMin(self, m): ...
    def Max(self): ...
    def SetMax(self, m): ...
    def Value(self): ...
    def Bound(self): ...
    def SetRange(self, l, u): ...
    def SetValue(self, v): ...
    def __eq__(self, element): ...
    def __ne__(self, element): ...
    __swig_destroy__: Incomplete

class IntervalVarElement(AssignmentElement):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Var(self): ...
    def StartMin(self): ...
    def StartMax(self): ...
    def StartValue(self): ...
    def DurationMin(self): ...
    def DurationMax(self): ...
    def DurationValue(self): ...
    def EndMin(self): ...
    def EndMax(self): ...
    def EndValue(self): ...
    def PerformedMin(self): ...
    def PerformedMax(self): ...
    def PerformedValue(self): ...
    def SetStartMin(self, m): ...
    def SetStartMax(self, m): ...
    def SetStartRange(self, mi, ma): ...
    def SetStartValue(self, v): ...
    def SetDurationMin(self, m): ...
    def SetDurationMax(self, m): ...
    def SetDurationRange(self, mi, ma): ...
    def SetDurationValue(self, v): ...
    def SetEndMin(self, m): ...
    def SetEndMax(self, m): ...
    def SetEndRange(self, mi, ma): ...
    def SetEndValue(self, v): ...
    def SetPerformedMin(self, m): ...
    def SetPerformedMax(self, m): ...
    def SetPerformedRange(self, mi, ma): ...
    def SetPerformedValue(self, v): ...
    def __eq__(self, element): ...
    def __ne__(self, element): ...
    __swig_destroy__: Incomplete

class SequenceVarElement(AssignmentElement):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Var(self): ...
    def ForwardSequence(self): ...
    def BackwardSequence(self): ...
    def Unperformed(self): ...
    def SetSequence(self, forward_sequence, backward_sequence, unperformed): ...
    def SetForwardSequence(self, forward_sequence): ...
    def SetBackwardSequence(self, backward_sequence): ...
    def SetUnperformed(self, unperformed): ...
    def __eq__(self, element): ...
    def __ne__(self, element): ...
    __swig_destroy__: Incomplete

class Assignment(PropagationBaseObject):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Clear(self): ...
    def Empty(self): ...
    def Size(self): ...
    def NumIntVars(self): ...
    def NumIntervalVars(self): ...
    def NumSequenceVars(self): ...
    def Store(self): ...
    def Restore(self): ...
    def Load(self, *args): ...
    def Save(self, *args): ...
    def AddObjective(self, v): ...
    def Objective(self): ...
    def HasObjective(self): ...
    def ObjectiveMin(self): ...
    def ObjectiveMax(self): ...
    def ObjectiveValue(self): ...
    def ObjectiveBound(self): ...
    def SetObjectiveMin(self, m): ...
    def SetObjectiveMax(self, m): ...
    def SetObjectiveValue(self, value): ...
    def SetObjectiveRange(self, l, u): ...
    def Min(self, var): ...
    def Max(self, var): ...
    def Value(self, var): ...
    def Bound(self, var): ...
    def SetMin(self, var, m): ...
    def SetMax(self, var, m): ...
    def SetRange(self, var, l, u): ...
    def SetValue(self, var, value): ...
    def StartMin(self, var): ...
    def StartMax(self, var): ...
    def StartValue(self, var): ...
    def DurationMin(self, var): ...
    def DurationMax(self, var): ...
    def DurationValue(self, var): ...
    def EndMin(self, var): ...
    def EndMax(self, var): ...
    def EndValue(self, var): ...
    def PerformedMin(self, var): ...
    def PerformedMax(self, var): ...
    def PerformedValue(self, var): ...
    def SetStartMin(self, var, m): ...
    def SetStartMax(self, var, m): ...
    def SetStartRange(self, var, mi, ma): ...
    def SetStartValue(self, var, value): ...
    def SetDurationMin(self, var, m): ...
    def SetDurationMax(self, var, m): ...
    def SetDurationRange(self, var, mi, ma): ...
    def SetDurationValue(self, var, value): ...
    def SetEndMin(self, var, m): ...
    def SetEndMax(self, var, m): ...
    def SetEndRange(self, var, mi, ma): ...
    def SetEndValue(self, var, value): ...
    def SetPerformedMin(self, var, m): ...
    def SetPerformedMax(self, var, m): ...
    def SetPerformedRange(self, var, mi, ma): ...
    def SetPerformedValue(self, var, value): ...
    def Add(self, *args): ...
    def ForwardSequence(self, var): ...
    def BackwardSequence(self, var): ...
    def Unperformed(self, var): ...
    def SetSequence(self, var, forward_sequence, backward_sequence, unperformed): ...
    def SetForwardSequence(self, var, forward_sequence): ...
    def SetBackwardSequence(self, var, backward_sequence): ...
    def SetUnperformed(self, var, unperformed): ...
    def Activate(self, *args): ...
    def Deactivate(self, *args): ...
    def Activated(self, *args): ...
    def DebugString(self): ...
    def IntVarContainer(self): ...
    def MutableIntVarContainer(self): ...
    def IntervalVarContainer(self): ...
    def MutableIntervalVarContainer(self): ...
    def SequenceVarContainer(self): ...
    def MutableSequenceVarContainer(self): ...
    def __eq__(self, assignment): ...
    def __ne__(self, assignment): ...

def __lshift__(*args): ...

class Pack(Constraint):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def AddWeightedSumLessOrEqualConstantDimension(self, *args): ...
    def AddWeightedSumEqualVarDimension(self, *args): ...
    def AddSumVariableWeightsLessOrEqualConstantDimension(self, usage, capacity): ...
    def AddWeightedSumOfAssignedDimension(self, weights, cost_var): ...
    def AddCountUsedBinDimension(self, count_var): ...
    def AddCountAssignedItemsDimension(self, count_var): ...
    def Post(self): ...
    def InitialPropagateWrapper(self): ...
    def DebugString(self): ...

class DisjunctiveConstraint(Constraint):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def SequenceVar(self): ...
    def SetTransitionTime(self, transition_time): ...
    def TransitionTime(self, before_index, after_index): ...

class RevInteger:
    thisown: Incomplete
    def __init__(self, val) -> None: ...
    def Value(self): ...
    def SetValue(self, s, val): ...
    __swig_destroy__: Incomplete

class NumericalRevInteger(RevInteger):
    thisown: Incomplete
    def __init__(self, val) -> None: ...
    def Add(self, s, to_add): ...
    def Incr(self, s): ...
    def Decr(self, s): ...
    __swig_destroy__: Incomplete

class RevBool:
    thisown: Incomplete
    def __init__(self, val) -> None: ...
    def Value(self): ...
    def SetValue(self, s, val): ...
    __swig_destroy__: Incomplete

class IntVarContainer:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Contains(self, var): ...
    def Element(self, index): ...
    def Size(self): ...
    def Store(self): ...
    def Restore(self): ...
    def __eq__(self, container): ...
    def __ne__(self, container): ...
    __swig_destroy__: Incomplete

class IntervalVarContainer:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Contains(self, var): ...
    def Element(self, index): ...
    def Size(self): ...
    def Store(self): ...
    def Restore(self): ...
    def __eq__(self, container): ...
    def __ne__(self, container): ...
    __swig_destroy__: Incomplete

class SequenceVarContainer:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Contains(self, var): ...
    def Element(self, index): ...
    def Size(self): ...
    def Store(self): ...
    def Restore(self): ...
    def __eq__(self, container): ...
    def __ne__(self, container): ...
    __swig_destroy__: Incomplete

class LocalSearchOperator(BaseObject):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def NextNeighbor(self, delta, deltadelta): ...
    def Start(self, assignment): ...
    def __disown__(self): ...

class IntVarLocalSearchOperator(LocalSearchOperator):
    thisown: Incomplete
    def __init__(self, vars, keep_inverse_values: bool = False) -> None: ...
    __swig_destroy__: Incomplete
    def Start(self, assignment): ...
    def IsIncremental(self): ...
    def Size(self): ...
    def Value(self, index): ...
    def Var(self, index): ...
    def OldValue(self, index): ...
    def PrevValue(self, index): ...
    def SetValue(self, index, value): ...
    def Activated(self, index): ...
    def Activate(self, index): ...
    def Deactivate(self, index): ...
    def AddVars(self, vars): ...
    def OnStart(self): ...
    def NextNeighbor(self, delta, deltadelta): ...
    def OneNeighbor(self): ...
    def __disown__(self): ...

class BaseLns(IntVarLocalSearchOperator):
    thisown: Incomplete
    def __init__(self, vars) -> None: ...
    __swig_destroy__: Incomplete
    def InitFragments(self): ...
    def NextFragment(self): ...
    def AppendToFragment(self, index): ...
    def FragmentSize(self): ...
    def __getitem__(self, index): ...
    def __len__(self) -> int: ...
    def __disown__(self): ...

class ChangeValue(IntVarLocalSearchOperator):
    thisown: Incomplete
    def __init__(self, vars) -> None: ...
    __swig_destroy__: Incomplete
    def ModifyValue(self, index, value): ...
    def OneNeighbor(self): ...
    def __disown__(self): ...

class LocalSearchFilter(BaseObject):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Accept(self, delta, deltadelta, objective_min, objective_max): ...
    def IsIncremental(self): ...
    def Synchronize(self, assignment, delta): ...
    __swig_destroy__: Incomplete

class LocalSearchFilterManager(BaseObject):
    thisown: Incomplete
    def DebugString(self): ...
    def __init__(self, *args) -> None: ...
    def Accept(self, monitor, delta, deltadelta, objective_min, objective_max): ...
    def Synchronize(self, assignment, delta): ...
    __swig_destroy__: Incomplete

class IntVarLocalSearchFilter(LocalSearchFilter):
    thisown: Incomplete
    def __init__(self, vars) -> None: ...
    __swig_destroy__: Incomplete
    def Synchronize(self, assignment, delta): ...
    def Size(self): ...
    def Value(self, index): ...
    def IndexFromVar(self, var): ...
    def __disown__(self): ...

class BooleanVar(IntVar):
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Min(self): ...
    def SetMin(self, m): ...
    def Max(self): ...
    def SetMax(self, m): ...
    def SetRange(self, mi, ma): ...
    def Bound(self): ...
    def Value(self): ...
    def RemoveValue(self, v): ...
    def RemoveInterval(self, l, u): ...
    def WhenBound(self, d): ...
    def WhenRange(self, d): ...
    def WhenDomain(self, d): ...
    def Size(self): ...
    def Contains(self, v): ...
    def HoleIteratorAux(self, reversible): ...
    def DomainIteratorAux(self, reversible): ...
    def DebugString(self): ...

class PyDecision(Decision):
    def ApplyWrapper(self, solver) -> None: ...
    def RefuteWrapper(self, solver) -> None: ...
    def DebugString(self): ...

class PyDecisionBuilder(DecisionBuilder):
    def NextWrapper(self, solver): ...
    def DebugString(self): ...

class PyDemon(Demon):
    def RunWrapper(self, solver) -> None: ...
    def DebugString(self): ...

class PyConstraintDemon(PyDemon):
    def __init__(self, ct, method, delayed, *args) -> None: ...
    def Run(self, solver) -> None: ...
    def Priority(self): ...
    def DebugString(self): ...

class PyConstraint(Constraint):
    def __init__(self, solver) -> None: ...
    def Demon(self, method, *args): ...
    def DelayedDemon(self, method, *args): ...
    def InitialPropagateDemon(self): ...
    def DelayedInitialPropagateDemon(self): ...
    def InitialPropagateWrapper(self) -> None: ...
    def DebugString(self): ...

class RoutingIndexManager:
    thisown: Incomplete
    def __init__(self, *args) -> None: ...
    def GetNumberOfNodes(self): ...
    def GetNumberOfVehicles(self): ...
    def GetNumberOfIndices(self): ...
    def GetStartIndex(self, vehicle): ...
    def GetEndIndex(self, vehicle): ...
    def NodeToIndex(self, node): ...
    def IndexToNode(self, index): ...
    __swig_destroy__: Incomplete

def DefaultRoutingModelParameters(): ...
def DefaultRoutingSearchParameters(): ...
def FindErrorInRoutingSearchParameters(search_parameters): ...

BOOL_UNSPECIFIED: Incomplete
BOOL_FALSE: Incomplete
BOOL_TRUE: Incomplete

class FirstSolutionStrategy:
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete

class LocalSearchMetaheuristic:
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete

class RoutingSearchStatus:
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete

class PathsMetadata:
    thisown: Incomplete
    def __init__(self, manager) -> None: ...
    def IsStart(self, node): ...
    def IsEnd(self, node): ...
    def GetPath(self, start_or_end_node): ...
    def NumPaths(self): ...
    def Paths(self): ...
    def Starts(self): ...
    def Start(self, path): ...
    def End(self, path): ...
    def Ends(self): ...
    __swig_destroy__: Incomplete

class RoutingModel:
    thisown: Incomplete
    PICKUP_AND_DELIVERY_NO_ORDER: Incomplete
    PICKUP_AND_DELIVERY_LIFO: Incomplete
    PICKUP_AND_DELIVERY_FIFO: Incomplete
    def __init__(self, *args) -> None: ...
    __swig_destroy__: Incomplete
    kTransitEvaluatorSignUnknown: Incomplete
    kTransitEvaluatorSignPositiveOrZero: Incomplete
    kTransitEvaluatorSignNegativeOrZero: Incomplete
    def RegisterUnaryTransitVector(self, values): ...
    def RegisterUnaryTransitCallback(self, *args): ...
    def RegisterTransitMatrix(self, values): ...
    def RegisterTransitCallback(self, *args): ...
    def RegisterCumulDependentTransitCallback(self, callback): ...
    def TransitCallback(self, callback_index): ...
    def UnaryTransitCallbackOrNull(self, callback_index): ...
    def CumulDependentTransitCallback(self, callback_index): ...
    def AddDimension(self, evaluator_index, slack_max, capacity, fix_start_cumul_to_zero, name): ...
    def AddDimensionWithVehicleTransits(self, evaluator_indices, slack_max, capacity, fix_start_cumul_to_zero, name): ...
    def AddDimensionWithVehicleCapacity(self, evaluator_index, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name): ...
    def AddDimensionWithVehicleTransitAndCapacity(self, evaluator_indices, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name): ...
    def AddDimensionWithCumulDependentVehicleTransitAndCapacity(self, fixed_evaluator_indices, cumul_dependent_evaluator_indices, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name): ...
    def AddConstantDimensionWithSlack(self, value, capacity, slack_max, fix_start_cumul_to_zero, name): ...
    def AddConstantDimension(self, value, capacity, fix_start_cumul_to_zero, name): ...
    def AddVectorDimension(self, values, capacity, fix_start_cumul_to_zero, name): ...
    def AddMatrixDimension(self, values, capacity, fix_start_cumul_to_zero, name): ...
    def GetAllDimensionNames(self): ...
    def GetDimensions(self): ...
    def GetDimensionsWithSoftOrSpanCosts(self): ...
    def GetUnaryDimensions(self): ...
    def GetDimensionsWithGlobalCumulOptimizers(self): ...
    def GetDimensionsWithLocalCumulOptimizers(self): ...
    def HasGlobalCumulOptimizer(self, dimension): ...
    def HasLocalCumulOptimizer(self, dimension): ...
    def GetMutableGlobalCumulLPOptimizer(self, dimension): ...
    def GetMutableGlobalCumulMPOptimizer(self, dimension): ...
    def GetMutableLocalCumulLPOptimizer(self, dimension): ...
    def GetMutableLocalCumulMPOptimizer(self, dimension): ...
    def HasDimension(self, dimension_name): ...
    def GetDimensionOrDie(self, dimension_name): ...
    def GetMutableDimension(self, dimension_name): ...
    def SetPrimaryConstrainedDimension(self, dimension_name): ...
    def GetPrimaryConstrainedDimension(self): ...
    def GetResourceGroup(self, rg_index): ...
    def GetDimensionResourceGroupIndices(self, dimension): ...
    def GetDimensionResourceGroupIndex(self, dimension): ...
    PENALIZE_ONCE: Incomplete
    PENALIZE_PER_INACTIVE: Incomplete
    def AddDisjunction(self, *args): ...
    def GetDisjunctionIndices(self, index): ...
    def GetDisjunctionPenalty(self, index): ...
    def GetDisjunctionMaxCardinality(self, index): ...
    def GetDisjunctionPenaltyCostBehavior(self, index): ...
    def GetNumberOfDisjunctions(self): ...
    def HasMandatoryDisjunctions(self): ...
    def HasMaxCardinalityConstrainedDisjunctions(self): ...
    def GetPerfectBinaryDisjunctions(self): ...
    def IgnoreDisjunctionsAlreadyForcedToZero(self): ...
    def AddSoftSameVehicleConstraint(self, indices, cost): ...
    def SetAllowedVehiclesForIndex(self, vehicles, index): ...
    def IsVehicleAllowedForIndex(self, vehicle, index): ...
    def AddPickupAndDelivery(self, pickup, delivery): ...
    def AddPickupAndDeliverySets(self, pickup_disjunction, delivery_disjunction): ...
    def GetPickupPosition(self, node_index): ...
    def GetDeliveryPosition(self, node_index): ...
    def IsPickup(self, node_index): ...
    def IsDelivery(self, node_index): ...
    def SetPickupAndDeliveryPolicyOfAllVehicles(self, policy): ...
    def SetPickupAndDeliveryPolicyOfVehicle(self, policy, vehicle): ...
    def GetPickupAndDeliveryPolicyOfVehicle(self, vehicle): ...
    def GetNumOfSingletonNodes(self): ...
    def GetFirstMatchingPickupDeliverySibling(self, node, is_match): ...
    TYPE_ADDED_TO_VEHICLE: Incomplete
    ADDED_TYPE_REMOVED_FROM_VEHICLE: Incomplete
    TYPE_ON_VEHICLE_UP_TO_VISIT: Incomplete
    TYPE_SIMULTANEOUSLY_ADDED_AND_REMOVED: Incomplete
    def SetVisitType(self, index, type, type_policy): ...
    def GetVisitType(self, index): ...
    def GetSingleNodesOfType(self, type): ...
    def GetPairIndicesOfType(self, type): ...
    def GetVisitTypePolicy(self, index): ...
    def GetNumberOfVisitTypes(self): ...
    def AddHardTypeIncompatibility(self, type1, type2): ...
    def AddTemporalTypeIncompatibility(self, type1, type2): ...
    def GetHardTypeIncompatibilitiesOfType(self, type): ...
    def GetTemporalTypeIncompatibilitiesOfType(self, type): ...
    def HasHardTypeIncompatibilities(self): ...
    def HasTemporalTypeIncompatibilities(self): ...
    def AddSameVehicleRequiredTypeAlternatives(self, dependent_type, required_type_alternatives): ...
    def AddRequiredTypeAlternativesWhenAddingType(self, dependent_type, required_type_alternatives): ...
    def AddRequiredTypeAlternativesWhenRemovingType(self, dependent_type, required_type_alternatives): ...
    def GetSameVehicleRequiredTypeAlternativesOfType(self, type): ...
    def GetRequiredTypeAlternativesWhenAddingType(self, type): ...
    def GetRequiredTypeAlternativesWhenRemovingType(self, type): ...
    def HasSameVehicleTypeRequirements(self): ...
    def HasTemporalTypeRequirements(self): ...
    def HasTypeRegulations(self): ...
    def UnperformedPenalty(self, var_index): ...
    def UnperformedPenaltyOrValue(self, default_value, var_index): ...
    def GetDepot(self): ...
    def SetMaximumNumberOfActiveVehicles(self, max_active_vehicles): ...
    def GetMaximumNumberOfActiveVehicles(self): ...
    def SetArcCostEvaluatorOfAllVehicles(self, evaluator_index): ...
    def SetArcCostEvaluatorOfVehicle(self, evaluator_index, vehicle): ...
    def SetFixedCostOfAllVehicles(self, cost): ...
    def SetFixedCostOfVehicle(self, cost, vehicle): ...
    def GetFixedCostOfVehicle(self, vehicle): ...
    def SetPathEnergyCostOfVehicle(self, force, distance, cost_per_unit, vehicle): ...
    def SetPathEnergyCostsOfVehicle(self, force, distance, threshold, cost_per_unit_below_threshold, cost_per_unit_above_threshold, vehicle): ...
    def SetAmortizedCostFactorsOfAllVehicles(self, linear_cost_factor, quadratic_cost_factor): ...
    def SetAmortizedCostFactorsOfVehicle(self, linear_cost_factor, quadratic_cost_factor, vehicle): ...
    def GetAmortizedLinearCostFactorOfVehicles(self): ...
    def GetAmortizedQuadraticCostFactorOfVehicles(self): ...
    def AddRouteConstraint(self, route_evaluator, costs_are_homogeneous_across_vehicles: bool = False): ...
    def GetRouteCost(self, route): ...
    def SetVehicleUsedWhenEmpty(self, is_used, vehicle): ...
    def IsVehicleUsedWhenEmpty(self, vehicle): ...
    def SetFirstSolutionEvaluator(self, evaluator): ...
    def SetFirstSolutionHint(self, hint): ...
    def GetFirstSolutionHint(self): ...
    def AddLocalSearchOperator(self, ls_operator): ...
    def AddSearchMonitor(self, monitor): ...
    def AddEnterSearchCallback(self, callback): ...
    def AddAtSolutionCallback(self, callback, track_unchecked_neighbors: bool = False): ...
    def AddRestoreDimensionValuesResetCallback(self, callback): ...
    def AddVariableMinimizedByFinalizer(self, var): ...
    def AddVariableMaximizedByFinalizer(self, var): ...
    def AddWeightedVariableMinimizedByFinalizer(self, var, cost): ...
    def AddWeightedVariableMaximizedByFinalizer(self, var, cost): ...
    def AddVariableTargetToFinalizer(self, var, target): ...
    def AddWeightedVariableTargetToFinalizer(self, var, target, cost): ...
    def CloseModel(self): ...
    def CloseModelWithParameters(self, search_parameters): ...
    def Solve(self, assignment: Incomplete | None = None): ...
    def SolveWithParameters(self, search_parameters, solutions: Incomplete | None = None): ...
    def SolveFromAssignmentWithParameters(self, assignment, search_parameters, solutions: Incomplete | None = None): ...
    def FastSolveFromAssignmentWithParameters(self, assignment, search_parameters, check_solution_in_cp, touched: Incomplete | None = None): ...
    def SolveFromAssignmentsWithParameters(self, assignments, search_parameters, solutions: Incomplete | None = None): ...
    def SolveWithIteratedLocalSearch(self, search_parameters): ...
    def SetAssignmentFromOtherModelAssignment(self, target_assignment, source_model, source_assignment): ...
    def ComputeLowerBound(self): ...
    def objective_lower_bound(self): ...
    def status(self): ...
    def enable_deep_serialization(self): ...
    def ApplyLocks(self, locks): ...
    def ApplyLocksToAllVehicles(self, locks, close_routes): ...
    def PreAssignment(self): ...
    def MutablePreAssignment(self): ...
    def WriteAssignment(self, file_name): ...
    def ReadAssignment(self, file_name): ...
    def RestoreAssignment(self, solution): ...
    def ReadAssignmentFromRoutes(self, routes, ignore_inactive_indices): ...
    def RoutesToAssignment(self, routes, ignore_inactive_indices, close_routes, assignment): ...
    def AssignmentToRoutes(self, assignment, routes): ...
    def CompactAssignment(self, assignment): ...
    def CompactAndCheckAssignment(self, assignment): ...
    def AddToAssignment(self, var): ...
    def AddIntervalToAssignment(self, interval): ...
    def PackCumulsOfOptimizerDimensionsFromAssignment(self, original_assignment, duration_limit, time_limit_was_reached: Incomplete | None = None): ...
    def GetOrCreateNodeNeighborsByCostClass(self, *args): ...
    def AddLocalSearchFilter(self, filter): ...
    def Start(self, vehicle): ...
    def End(self, vehicle): ...
    def IsStart(self, index): ...
    def IsEnd(self, index): ...
    def VehicleIndex(self, index): ...
    def Next(self, assignment, index): ...
    def IsVehicleUsed(self, assignment, vehicle): ...
    def NextVar(self, index): ...
    def ActiveVar(self, index): ...
    def ActiveVehicleVar(self, vehicle): ...
    def VehicleRouteConsideredVar(self, vehicle): ...
    def VehicleVar(self, index): ...
    def ResourceVar(self, vehicle, resource_group): ...
    def CostVar(self): ...
    def GetArcCostForVehicle(self, from_index, to_index, vehicle): ...
    def CostsAreHomogeneousAcrossVehicles(self): ...
    def GetHomogeneousCost(self, from_index, to_index): ...
    def GetArcCostForFirstSolution(self, from_index, to_index): ...
    def GetArcCostForClass(self, from_index, to_index, cost_class_index): ...
    def GetCostClassIndexOfVehicle(self, vehicle): ...
    def HasVehicleWithCostClassIndex(self, cost_class_index): ...
    def GetCostClassesCount(self): ...
    def GetNonZeroCostClassesCount(self): ...
    def GetVehicleClassIndexOfVehicle(self, vehicle): ...
    def GetVehicleOfClass(self, vehicle_class): ...
    def GetVehicleClassesCount(self): ...
    def GetSameVehicleIndicesOfIndex(self, node): ...
    def GetSameActivityIndicesOfIndex(self, node): ...
    def GetSameActivityGroupOfIndex(self, node): ...
    def GetSameActivityGroupsCount(self): ...
    def GetSameActivityIndicesOfGroup(self, group): ...
    def GetVehicleTypeContainer(self): ...
    def ArcIsMoreConstrainedThanArc(self, _from, to1, to2): ...
    def DebugOutputAssignment(self, solution_assignment, dimension_to_print): ...
    def CheckIfAssignmentIsFeasible(self, assignment, call_at_solution_monitors): ...
    def solver(self): ...
    def CheckLimit(self, *args): ...
    def RemainingTime(self): ...
    def UpdateTimeLimit(self, time_limit): ...
    def TimeBuffer(self): ...
    def GetMutableCPSatInterrupt(self): ...
    def GetMutableCPInterrupt(self): ...
    def CancelSearch(self): ...
    def nodes(self): ...
    def vehicles(self): ...
    def Size(self): ...
    def GetNumberOfDecisionsInFirstSolution(self, search_parameters): ...
    def GetNumberOfRejectsInFirstSolution(self, search_parameters): ...
    def GetAutomaticFirstSolutionStrategy(self): ...
    def IsMatchingModel(self): ...
    def AreRoutesInterdependent(self, parameters): ...
    def MakeGuidedSlackFinalizer(self, dimension, initializer): ...
    def MakeSelfDependentDimensionFinalizer(self, dimension): ...
    def GetPathsMetadata(self): ...
    def GetVehiclesOfSameClass(self, start_end_index): ...
    def GetSameVehicleClassArcs(self, from_index, to_index): ...

cvar: Incomplete

class RoutingModelVisitor(BaseObject):
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete

class GlobalVehicleBreaksConstraint(Constraint):
    thisown: Incomplete
    def __init__(self, dimension) -> None: ...
    def DebugString(self): ...
    def Post(self): ...
    def InitialPropagateWrapper(self): ...
    __swig_destroy__: Incomplete

class TypeRegulationsChecker:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    __swig_destroy__: Incomplete
    def CheckVehicle(self, vehicle, next_accessor): ...

class TypeIncompatibilityChecker(TypeRegulationsChecker):
    thisown: Incomplete
    def __init__(self, model, check_hard_incompatibilities) -> None: ...
    __swig_destroy__: Incomplete

class TypeRequirementChecker(TypeRegulationsChecker):
    thisown: Incomplete
    def __init__(self, model) -> None: ...
    __swig_destroy__: Incomplete

class TypeRegulationsConstraint(Constraint):
    thisown: Incomplete
    def __init__(self, model) -> None: ...
    def Post(self): ...
    def InitialPropagateWrapper(self): ...
    __swig_destroy__: Incomplete

class BoundCost:
    thisown: Incomplete
    bound: Incomplete
    cost: Incomplete
    def __init__(self, *args) -> None: ...
    __swig_destroy__: Incomplete

class SimpleBoundCosts:
    thisown: Incomplete
    def __init__(self, num_bounds, default_bound_cost) -> None: ...
    def bound_cost(self, element): ...
    def size(self): ...
    __swig_destroy__: Incomplete

class RoutingDimension:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    __swig_destroy__: Incomplete
    def model(self): ...
    def GetTransitValue(self, from_index, to_index, vehicle): ...
    def GetTransitValueFromClass(self, from_index, to_index, vehicle_class): ...
    def CumulVar(self, index): ...
    def TransitVar(self, index): ...
    def FixedTransitVar(self, index): ...
    def SlackVar(self, index): ...
    def SetCumulVarRange(self, index, min, max): ...
    def GetCumulVarMin(self, index): ...
    def GetCumulVarMax(self, index): ...
    def SetSpanUpperBoundForVehicle(self, upper_bound, vehicle): ...
    def SetSpanCostCoefficientForVehicle(self, coefficient, vehicle): ...
    def SetSpanCostCoefficientForAllVehicles(self, coefficient): ...
    def SetSlackCostCoefficientForVehicle(self, coefficient, vehicle): ...
    def SetSlackCostCoefficientForAllVehicles(self, coefficient): ...
    def SetGlobalSpanCostCoefficient(self, coefficient): ...
    def SetCumulVarSoftUpperBound(self, index, upper_bound, coefficient): ...
    def HasCumulVarSoftUpperBound(self, index): ...
    def GetCumulVarSoftUpperBound(self, index): ...
    def GetCumulVarSoftUpperBoundCoefficient(self, index): ...
    def SetCumulVarSoftLowerBound(self, index, lower_bound, coefficient): ...
    def HasCumulVarSoftLowerBound(self, index): ...
    def GetCumulVarSoftLowerBound(self, index): ...
    def GetCumulVarSoftLowerBoundCoefficient(self, index): ...
    def SetBreakIntervalsOfVehicle(self, breaks, vehicle, node_visit_transits): ...
    def SetBreakDistanceDurationOfVehicle(self, distance, duration, vehicle): ...
    def InitializeBreaks(self): ...
    def HasBreakConstraints(self): ...
    def GetPreTravelEvaluatorOfVehicle(self, vehicle): ...
    def GetPostTravelEvaluatorOfVehicle(self, vehicle): ...
    def base_dimension(self): ...
    def ShortestTransitionSlack(self, node): ...
    def name(self): ...
    def SetPickupToDeliveryLimitFunctionForPair(self, limit_function, pair_index): ...
    def HasPickupToDeliveryLimits(self): ...
    def AddNodePrecedence(self, first_node, second_node, offset): ...
    def GetSpanUpperBoundForVehicle(self, vehicle): ...
    def GetSpanCostCoefficientForVehicle(self, vehicle): ...
    def GetSlackCostCoefficientForVehicle(self, vehicle): ...
    def global_span_cost_coefficient(self): ...
    def GetGlobalOptimizerOffset(self): ...
    def GetLocalOptimizerOffsetForVehicle(self, vehicle): ...
    def SetSoftSpanUpperBoundForVehicle(self, bound_cost, vehicle): ...
    def HasSoftSpanUpperBounds(self): ...
    def GetSoftSpanUpperBoundForVehicle(self, vehicle): ...
    def SetQuadraticCostSoftSpanUpperBoundForVehicle(self, bound_cost, vehicle): ...
    def HasQuadraticCostSoftSpanUpperBounds(self): ...
    def GetQuadraticCostSoftSpanUpperBoundForVehicle(self, vehicle): ...

def SolveModelWithSat(model, search_parameters, initial_solution, solution): ...
