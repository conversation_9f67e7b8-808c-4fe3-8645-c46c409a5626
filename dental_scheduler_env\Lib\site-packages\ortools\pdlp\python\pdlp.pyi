import numpy
import scipy.sparse
from _typeshed import Incomplete

class PrimalAndDualSolution:
    dual_solution: numpy.ndarray[numpy.float64[m, 1]]
    primal_solution: numpy.ndarray[numpy.float64[m, 1]]
    def __init__(self) -> None: ...

class QuadraticProgram:
    constraint_lower_bounds: numpy.ndarray[numpy.float64[m, 1]]
    constraint_matrix: scipy.sparse.csc_matrix[numpy.float64]
    constraint_names: list[str] | None
    constraint_upper_bounds: numpy.ndarray[numpy.float64[m, 1]]
    objective_offset: float
    objective_scaling_factor: float
    objective_vector: numpy.ndarray[numpy.float64[m, 1]]
    problem_name: str | None
    variable_lower_bounds: numpy.ndarray[numpy.float64[m, 1]]
    variable_names: list[str] | None
    variable_upper_bounds: numpy.ndarray[numpy.float64[m, 1]]
    def __init__(self) -> None: ...
    def apply_objective_scaling_and_offset(self, arg0: float) -> float: ...
    def clear_objective_matrix(self) -> None: ...
    def resize_and_initialize(self, arg0: int, arg1: int) -> None: ...
    def set_objective_matrix_diagonal(self, arg0: numpy.ndarray[numpy.float64[m, 1]]) -> None: ...
    @property
    def objective_matrix(self) -> numpy.ndarray[numpy.float64[m, n]] | None: ...

class SolverResult:
    dual_solution: numpy.ndarray[numpy.float64[m, 1]]
    primal_solution: numpy.ndarray[numpy.float64[m, 1]]
    reduced_costs: numpy.ndarray[numpy.float64[m, 1]]
    solve_log: Incomplete
    def __init__(self) -> None: ...

def is_linear_program(arg0: QuadraticProgram) -> bool: ...
def primal_dual_hybrid_gradient(qp: QuadraticProgram, params, initial_solution: PrimalAndDualSolution | None = ...) -> SolverResult: ...
def qp_from_mpmodel_proto(proto_str, relax_integer_variables: bool, include_names: bool = ...) -> QuadraticProgram: ...
def qp_to_mpmodel_proto(*args, **kwargs): ...
def read_quadratic_program_or_die(filename: str, include_names: bool = ...) -> QuadraticProgram: ...
def validate_quadratic_program_dimensions(arg0: QuadraticProgram) -> None: ...
