# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/solvers/osqp.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/solvers/osqp.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#ortools/math_opt/solvers/osqp.proto\x12\x1coperations_research.math_opt\"\x89\x07\n\x11OsqpSettingsProto\x12\x10\n\x03rho\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x12\n\x05sigma\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x14\n\x07scaling\x18\x03 \x01(\x03H\x02\x88\x01\x01\x12\x19\n\x0c\x61\x64\x61ptive_rho\x18\x04 \x01(\x08H\x03\x88\x01\x01\x12\"\n\x15\x61\x64\x61ptive_rho_interval\x18\x05 \x01(\x03H\x04\x88\x01\x01\x12#\n\x16\x61\x64\x61ptive_rho_tolerance\x18\x06 \x01(\x01H\x05\x88\x01\x01\x12\"\n\x15\x61\x64\x61ptive_rho_fraction\x18\x07 \x01(\x01H\x06\x88\x01\x01\x12\x15\n\x08max_iter\x18\x08 \x01(\x03H\x07\x88\x01\x01\x12\x14\n\x07\x65ps_abs\x18\t \x01(\x01H\x08\x88\x01\x01\x12\x14\n\x07\x65ps_rel\x18\n \x01(\x01H\t\x88\x01\x01\x12\x19\n\x0c\x65ps_prim_inf\x18\x0b \x01(\x01H\n\x88\x01\x01\x12\x19\n\x0c\x65ps_dual_inf\x18\x0c \x01(\x01H\x0b\x88\x01\x01\x12\x12\n\x05\x61lpha\x18\r \x01(\x01H\x0c\x88\x01\x01\x12\x12\n\x05\x64\x65lta\x18\x0e \x01(\x01H\r\x88\x01\x01\x12\x13\n\x06polish\x18\x0f \x01(\x08H\x0e\x88\x01\x01\x12\x1f\n\x12polish_refine_iter\x18\x10 \x01(\x03H\x0f\x88\x01\x01\x12\x14\n\x07verbose\x18\x11 \x01(\x08H\x10\x88\x01\x01\x12\x1f\n\x12scaled_termination\x18\x12 \x01(\x08H\x11\x88\x01\x01\x12\x1e\n\x11\x63heck_termination\x18\x13 \x01(\x03H\x12\x88\x01\x01\x12\x17\n\nwarm_start\x18\x14 \x01(\x08H\x13\x88\x01\x01\x12\x17\n\ntime_limit\x18\x15 \x01(\x01H\x14\x88\x01\x01\x42\x06\n\x04_rhoB\x08\n\x06_sigmaB\n\n\x08_scalingB\x0f\n\r_adaptive_rhoB\x18\n\x16_adaptive_rho_intervalB\x19\n\x17_adaptive_rho_toleranceB\x18\n\x16_adaptive_rho_fractionB\x0b\n\t_max_iterB\n\n\x08_eps_absB\n\n\x08_eps_relB\x0f\n\r_eps_prim_infB\x0f\n\r_eps_dual_infB\x08\n\x06_alphaB\x08\n\x06_deltaB\t\n\x07_polishB\x15\n\x13_polish_refine_iterB\n\n\x08_verboseB\x15\n\x13_scaled_terminationB\x14\n\x12_check_terminationB\r\n\x0b_warm_startB\r\n\x0b_time_limit\"3\n\nOsqpOutput\x12%\n\x1dinitialized_underlying_solver\x18\x01 \x01(\x08\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.solvers.osqp_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_OSQPSETTINGSPROTO']._serialized_start=70
  _globals['_OSQPSETTINGSPROTO']._serialized_end=975
  _globals['_OSQPOUTPUT']._serialized_start=977
  _globals['_OSQPOUTPUT']._serialized_end=1028
# @@protoc_insertion_point(module_scope)
