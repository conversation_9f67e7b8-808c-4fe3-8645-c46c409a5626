"""Team-based job scheduling with employee hour constraints and Excel job loading."""
import collections
from ortools.sat.python import cp_model
from datetime import datetime, timedelta
from job_loader import load_jobs_from_excel, validate_jobs_data
import sys
import os

def main(excel_file="Job_Upload_Template.xlsx") -> None:
    """Team-based job scheduling problem with Excel job loading."""
    
    # Team and employee data
    teams = {
        'red': ['<PERSON>', '<PERSON>'],
        'blue': ['<PERSON>', '<PERSON>', '<PERSON>'], 
        'green': ['<PERSON>', '<PERSON><PERSON>']
    }
    
    # Create employee to ID mapping
    all_employees = []
    employee_to_id = {}
    employee_to_team = {}
    
    for team_name, employees in teams.items():
        for employee in employees:
            if employee not in employee_to_id:
                employee_id = len(all_employees)
                all_employees.append(employee)
                employee_to_id[employee] = employee_id
                employee_to_team[employee] = [team_name]
            else:
                employee_to_team[employee].append(team_name)
    
    # Load jobs from Excel file
    print(f"Loading jobs from {excel_file}...")
    if os.path.exists(excel_file):
        jobs_data = load_jobs_from_excel(excel_file)
        if not jobs_data:
            print("No jobs loaded from Excel. Using default jobs.")
            jobs_data = get_default_jobs()
    else:
        print(f"Excel file {excel_file} not found. Using default jobs.")
        jobs_data = get_default_jobs()
    
    # Validate jobs
    if not validate_jobs_data(jobs_data):
        print("Job validation failed. Exiting.")
        return
    
    print(f"Loaded {len(jobs_data)} jobs for scheduling")
    
    team_names = ['red', 'blue', 'green']
    
    # Time constraints
    work_start_hour = 9  # 9 AM
    work_end_hour = 17   # 5 PM
    daily_work_hours = work_end_hour - work_start_hour  # 8 hours
    
    # Calculate planning horizon in hours (5 days * 8 hours/day to be safe)
    planning_days = 5
    horizon = planning_days * daily_work_hours
    
    # Create the model.
    model = cp_model.CpModel()

    # Named tuple to store information about created variables.
    task_type = collections.namedtuple("task_type", "start end interval employee")

    # Creates job intervals and employee assignment variables
    all_tasks = {}
    job_completion_vars = []
    
    today = datetime.now().date()
    
    for job_id, job_data in enumerate(jobs_data):
        job_tasks = job_data['tasks']
        completion_date = job_data['completion_date']
        
        # Calculate completion deadline in hours from start
        completion_deadline_days = (completion_date - today).days
        completion_deadline_hours = min(completion_deadline_days * daily_work_hours, horizon)
        
        for task_id, task in enumerate(job_tasks):
            team_id, duration = task
            suffix = f"_{job_id}_{task_id}"
            
            # Create time variables with working hour constraints
            # Tasks can only start at valid times (within working hours and allowing completion)
            valid_starts = []
            for day in range(planning_days):
                for hour in range(daily_work_hours - duration + 1):
                    valid_starts.append(day * daily_work_hours + hour)
            
            if valid_starts:
                start_var = model.new_int_var_from_domain(
                    cp_model.Domain.from_values(valid_starts), "start" + suffix
                )
            else:
                start_var = model.new_int_var(0, 0, "start" + suffix)  # No valid start times
            
            end_var = model.new_int_var(duration, horizon, "end" + suffix)
            model.add(end_var == start_var + duration)
            
            interval_var = model.new_interval_var(
                start_var, duration, end_var, "interval" + suffix
            )
            
            # Create employee assignment variable for this team
            team_name = team_names[team_id]
            available_employees = [employee_to_id[emp] for emp in teams[team_name]]
            employee_var = model.new_int_var_from_domain(
                cp_model.Domain.from_values(available_employees), 
                "employee" + suffix
            )
            
            all_tasks[job_id, task_id] = task_type(
                start=start_var, end=end_var, interval=interval_var, employee=employee_var
            )
        
        # Job completion constraint: all tasks must finish before deadline
        if job_tasks:
            last_task_end = all_tasks[job_id, len(job_tasks) - 1].end
            model.add(last_task_end <= completion_deadline_hours)
            job_completion_vars.append(last_task_end)
    
    # No overlap constraint for each employee
    for emp_id in range(len(all_employees)):
        emp_intervals = []
        for job_id, job_data in enumerate(jobs_data):
            job_tasks = job_data['tasks']
            for task_id, task in enumerate(job_tasks):
                team_id, duration = task
                team_name = team_names[team_id]
                
                # Check if this employee can work on this team's tasks
                if all_employees[emp_id] in teams[team_name]:
                    # Create optional interval for this employee
                    is_assigned = model.new_bool_var(f"emp_{emp_id}_assigned_{job_id}_{task_id}")
                    model.add(all_tasks[job_id, task_id].employee == emp_id).only_enforce_if(is_assigned)
                    model.add(all_tasks[job_id, task_id].employee != emp_id).only_enforce_if(is_assigned.Not())
                    
                    optional_interval = model.new_optional_interval_var(
                        all_tasks[job_id, task_id].start,
                        duration,
                        all_tasks[job_id, task_id].end,
                        is_assigned,
                        f"optional_{emp_id}_{job_id}_{task_id}"
                    )
                    emp_intervals.append(optional_interval)
        
        # No overlap for this employee
        if emp_intervals:
            model.add_no_overlap(emp_intervals)

    # Simplified approach: just ensure no employee works more than total available hours
    for emp_id in range(len(all_employees)):
        total_work_vars = []
        
        for job_id, job_data in enumerate(jobs_data):
            job_tasks = job_data['tasks']
            for task_id, task in enumerate(job_tasks):
                team_id, duration = task
                team_name = team_names[team_id]
                
                # Check if this employee can work on this team's tasks
                if all_employees[emp_id] in teams[team_name]:
                    is_assigned = model.new_bool_var(f"emp_{emp_id}_work_{job_id}_{task_id}")
                    model.add(all_tasks[job_id, task_id].employee == emp_id).only_enforce_if(is_assigned)
                    model.add(all_tasks[job_id, task_id].employee != emp_id).only_enforce_if(is_assigned.Not())
                    
                    work_var = model.new_int_var(0, duration, f"work_{emp_id}_{job_id}_{task_id}")
                    model.add(work_var == duration).only_enforce_if(is_assigned)
                    model.add(work_var == 0).only_enforce_if(is_assigned.Not())
                    
                    total_work_vars.append(work_var)
        
        # Constraint: total work for this employee <= available hours over all days
        if total_work_vars:
            max_hours = planning_days * daily_work_hours
            model.add(sum(total_work_vars) <= max_hours)

    # Precedences inside a job.
    for job_id, job_data in enumerate(jobs_data):
        job_tasks = job_data['tasks']
        for task_id in range(len(job_tasks) - 1):
            model.add(
                all_tasks[job_id, task_id + 1].start >= all_tasks[job_id, task_id].end
            )

    # Multi-objective optimization
    # Primary: Minimize makespan (shortest completion time)
    makespan_var = model.new_int_var(0, horizon, "makespan")
    if job_completion_vars:
        model.add_max_equality(makespan_var, job_completion_vars)
    
    # Minimize makespan
    model.minimize(makespan_var)

    # Creates the solver and solve.
    solver = cp_model.CpSolver()
    status = solver.solve(model)

    if status == cp_model.OPTIMAL or status == cp_model.FEASIBLE:
        print("Solution:")
        print(f"Optimal Schedule Length: {solver.value(makespan_var)} hours")
        print()
        
        # Create schedule by day and employee
        schedule_by_day = collections.defaultdict(lambda: collections.defaultdict(list))
        
        for job_id, job_data in enumerate(jobs_data):
            job_tasks = job_data['tasks']
            for task_id, task in enumerate(job_tasks):
                employee_id = solver.value(all_tasks[job_id, task_id].employee)
                start_time = solver.value(all_tasks[job_id, task_id].start)
                duration = task[1]
                
                # Convert to day and hour
                day = start_time // daily_work_hours
                start_hour = work_start_hour + (start_time % daily_work_hours)
                end_hour = start_hour + duration
                
                schedule_by_day[day][employee_id].append({
                    'job_id': job_data['job_id'],
                    'job_name': job_data['job_name'],
                    'task': task_id,
                    'team': team_names[task[0]],
                    'start_hour': start_hour,
                    'end_hour': end_hour,
                    'duration': duration
                })
        
        # Print schedule by day
        for day in range(planning_days):
            if day in schedule_by_day:
                schedule_date = today + timedelta(days=day)
                print(f"Day {day + 1} ({schedule_date.strftime('%Y-%m-%d')}):")
                print("-" * 80)
                
                for emp_id in range(len(all_employees)):
                    if emp_id in schedule_by_day[day]:
                        employee_name = all_employees[emp_id]
                        teams_list = ", ".join(employee_to_team[employee_name])
                        print(f"  {employee_name} ({teams_list}):")
                        
                        # Sort tasks by start time
                        tasks = sorted(schedule_by_day[day][emp_id], key=lambda x: x['start_hour'])
                        daily_total = sum(task['duration'] for task in tasks)
                        
                        for task in tasks:
                            print(f"    {task['start_hour']:02d}:00-{task['end_hour']:02d}:00 "
                                  f"{task['job_id']} ({task['job_name']}) Task{task['task']} "
                                  f"({task['team']} team) [{task['duration']}h]")
                        
                        print(f"    Daily total: {daily_total} hours")
                        print()
                print()
        
        # Print job completion status
        print("Job Completion Status:")
        print("-" * 60)
        for job_id, job_data in enumerate(jobs_data):
            job_tasks = job_data['tasks']
            if job_tasks:
                completion_time = solver.value(all_tasks[job_id, len(job_tasks) - 1].end)
                completion_day = completion_time // daily_work_hours
                completion_date = today + timedelta(days=completion_day)
                deadline = job_data['completion_date']
                
                status_text = "✓ On time" if completion_date <= deadline else "⚠ Late"
                print(f"  {job_data['job_id']}: {job_data['job_name']}")
                print(f"    Completes: {completion_date.strftime('%Y-%m-%d')} "
                      f"(Due: {deadline.strftime('%Y-%m-%d')}) {status_text}")
        print()
    else:
        print("No solution found.")

    # Statistics.
    print("\nStatistics")
    print(f"  - conflicts: {solver.num_conflicts}")
    print(f"  - branches : {solver.num_branches}")
    print(f"  - wall time: {solver.wall_time}s")

def get_default_jobs():
    """Return default jobs if Excel file is not available"""
    today = datetime.now().date()
    
    return [
        {
            'job_id': 'DEFAULT001',
            'job_name': 'Sample Project A',
            'description': 'Default sample project',
            'priority': 'High',
            'tasks': [(0, 2), (1, 3), (2, 2)],
            'received_date': today,
            'completion_date': today + timedelta(days=2)
        },
        {
            'job_id': 'DEFAULT002',
            'job_name': 'Sample Project B',
            'description': 'Default sample project',
            'priority': 'Medium',
            'tasks': [(1, 4), (2, 2), (0, 3)],
            'received_date': today,
            'completion_date': today + timedelta(days=3)
        }
    ]

if __name__ == "__main__":
    # Check if Excel file is provided as command line argument
    excel_file = "Job_Upload_Template.xlsx"
    if len(sys.argv) > 1:
        excel_file = sys.argv[1]
    
    main(excel_file)
