"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

The solution to an optimization model.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import ortools.math_opt.sparse_containers_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _SolutionStatusProto:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SolutionStatusProtoEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SolutionStatusProto.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SOLUTION_STATUS_UNSPECIFIED: _SolutionStatusProto.ValueType  # 0
    """Guard value representing no status."""
    SOLUTION_STATUS_UNDETERMINED: _SolutionStatusProto.ValueType  # 1
    """Solver does not claim a feasibility status."""
    SOLUTION_STATUS_FEASIBLE: _SolutionStatusProto.ValueType  # 2
    """Solver claims the solution is feasible."""
    SOLUTION_STATUS_INFEASIBLE: _SolutionStatusProto.ValueType  # 3
    """Solver claims the solution is infeasible."""

class SolutionStatusProto(_SolutionStatusProto, metaclass=_SolutionStatusProtoEnumTypeWrapper):
    """Feasibility of a primal or dual solution as claimed by the solver."""

SOLUTION_STATUS_UNSPECIFIED: SolutionStatusProto.ValueType  # 0
"""Guard value representing no status."""
SOLUTION_STATUS_UNDETERMINED: SolutionStatusProto.ValueType  # 1
"""Solver does not claim a feasibility status."""
SOLUTION_STATUS_FEASIBLE: SolutionStatusProto.ValueType  # 2
"""Solver claims the solution is feasible."""
SOLUTION_STATUS_INFEASIBLE: SolutionStatusProto.ValueType  # 3
"""Solver claims the solution is infeasible."""
global___SolutionStatusProto = SolutionStatusProto

class _BasisStatusProto:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _BasisStatusProtoEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_BasisStatusProto.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    BASIS_STATUS_UNSPECIFIED: _BasisStatusProto.ValueType  # 0
    """Guard value representing no status."""
    BASIS_STATUS_FREE: _BasisStatusProto.ValueType  # 1
    """The variable/constraint is free (it has no finite bounds)."""
    BASIS_STATUS_AT_LOWER_BOUND: _BasisStatusProto.ValueType  # 2
    """The variable/constraint is at its lower bound (which must be finite)."""
    BASIS_STATUS_AT_UPPER_BOUND: _BasisStatusProto.ValueType  # 3
    """The variable/constraint is at its upper bound (which must be finite)."""
    BASIS_STATUS_FIXED_VALUE: _BasisStatusProto.ValueType  # 4
    """The variable/constraint has identical finite lower and upper bounds."""
    BASIS_STATUS_BASIC: _BasisStatusProto.ValueType  # 5
    """The variable/constraint is basic."""

class BasisStatusProto(_BasisStatusProto, metaclass=_BasisStatusProtoEnumTypeWrapper):
    """Status of a variable/constraint in a LP basis."""

BASIS_STATUS_UNSPECIFIED: BasisStatusProto.ValueType  # 0
"""Guard value representing no status."""
BASIS_STATUS_FREE: BasisStatusProto.ValueType  # 1
"""The variable/constraint is free (it has no finite bounds)."""
BASIS_STATUS_AT_LOWER_BOUND: BasisStatusProto.ValueType  # 2
"""The variable/constraint is at its lower bound (which must be finite)."""
BASIS_STATUS_AT_UPPER_BOUND: BasisStatusProto.ValueType  # 3
"""The variable/constraint is at its upper bound (which must be finite)."""
BASIS_STATUS_FIXED_VALUE: BasisStatusProto.ValueType  # 4
"""The variable/constraint has identical finite lower and upper bounds."""
BASIS_STATUS_BASIC: BasisStatusProto.ValueType  # 5
"""The variable/constraint is basic."""
global___BasisStatusProto = BasisStatusProto

@typing.final
class PrimalSolutionProto(google.protobuf.message.Message):
    """A solution to an optimization problem.

    E.g. consider a simple linear program:
      min c * x
      s.t. A * x >= b
      x >= 0.
    A primal solution is assignment values to x. It is feasible if it satisfies
    A * x >= b and x >= 0 from above. In the message PrimalSolutionProto below,
    variable_values is x and objective_value is c * x.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class AuxiliaryObjectiveValuesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        value: builtins.float
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: builtins.float = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    VARIABLE_VALUES_FIELD_NUMBER: builtins.int
    OBJECTIVE_VALUE_FIELD_NUMBER: builtins.int
    AUXILIARY_OBJECTIVE_VALUES_FIELD_NUMBER: builtins.int
    FEASIBILITY_STATUS_FIELD_NUMBER: builtins.int
    objective_value: builtins.float
    """Objective value as computed by the underlying solver. Cannot be infinite or
    NaN.
    """
    feasibility_status: global___SolutionStatusProto.ValueType
    """Feasibility status of the solution according to the underlying solver."""
    @property
    def variable_values(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Requirements:
         * variable_values.ids are elements of VariablesProto.ids.
         * variable_values.values must all be finite.
        """

    @property
    def auxiliary_objective_values(self) -> google.protobuf.internal.containers.ScalarMap[builtins.int, builtins.float]:
        """Auxiliary objective values as computed by the underlying solver. Keys must
        be valid auxiliary objective IDs. Values cannot be infinite or NaN.
        """

    def __init__(
        self,
        *,
        variable_values: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        objective_value: builtins.float = ...,
        auxiliary_objective_values: collections.abc.Mapping[builtins.int, builtins.float] | None = ...,
        feasibility_status: global___SolutionStatusProto.ValueType = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["variable_values", b"variable_values"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["auxiliary_objective_values", b"auxiliary_objective_values", "feasibility_status", b"feasibility_status", "objective_value", b"objective_value", "variable_values", b"variable_values"]) -> None: ...

global___PrimalSolutionProto = PrimalSolutionProto

@typing.final
class PrimalRayProto(google.protobuf.message.Message):
    """A direction of unbounded improvement to an optimization problem;
    equivalently, a certificate of infeasibility for the dual of the
    optimization problem.

    E.g. consider a simple linear program:
      min c * x
      s.t. A * x >= b
      x >= 0
    A primal ray is an x that satisfies:
      c * x < 0
      A * x >= 0
      x >= 0
    Observe that given a feasible solution, any positive multiple of the primal
    ray plus that solution is still feasible, and gives a better objective
    value. A primal ray also proves the dual optimization problem infeasible.

    In the message PrimalRay below, variable_values is x.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARIABLE_VALUES_FIELD_NUMBER: builtins.int
    @property
    def variable_values(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Requirements:
         * variable_values.ids are elements of VariablesProto.ids.
         * variable_values.values must all be finite.
        """

    def __init__(
        self,
        *,
        variable_values: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["variable_values", b"variable_values"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["variable_values", b"variable_values"]) -> None: ...

global___PrimalRayProto = PrimalRayProto

@typing.final
class DualSolutionProto(google.protobuf.message.Message):
    """A solution to the dual of an optimization problem.

    E.g. consider the primal dual pair linear program pair:
      (Primal)             (Dual)
      min c * x            max b * y
      s.t. A * x >= b      s.t. y * A + r = c
      x >= 0               y, r >= 0.
    The dual solution is the pair (y, r). It is feasible if it satisfies the
    constraints from (Dual) above.

    In the message below, y is dual_values, r is reduced_costs, and
    b * y is objective value.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DUAL_VALUES_FIELD_NUMBER: builtins.int
    QUADRATIC_DUAL_VALUES_FIELD_NUMBER: builtins.int
    REDUCED_COSTS_FIELD_NUMBER: builtins.int
    OBJECTIVE_VALUE_FIELD_NUMBER: builtins.int
    FEASIBILITY_STATUS_FIELD_NUMBER: builtins.int
    objective_value: builtins.float
    """TODO(b/195295177): consider making this non-optional
    Objective value as computed by the underlying solver.
    """
    feasibility_status: global___SolutionStatusProto.ValueType
    """Feasibility status of the solution according to the underlying solver."""
    @property
    def dual_values(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Requirements:
         * dual_values.ids are elements of LinearConstraints.ids.
         * dual_values.values must all be finite.
        """

    @property
    def quadratic_dual_values(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Requirements:
         * quadratic_dual_values.ids are keys of ModelProto.quadratic_constraints.
         * quadratic_dual_values.values must all be finite.
        Note: Some solvers only return quadratic constraint duals when a
        solver-specific parameter is set
        (see go/mathopt-qcqp-dual#solver-specific).
        """

    @property
    def reduced_costs(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Requirements:
         * reduced_costs.ids are elements of VariablesProto.ids.
         * reduced_costs.values must all be finite.
        """

    def __init__(
        self,
        *,
        dual_values: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        quadratic_dual_values: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        reduced_costs: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        objective_value: builtins.float | None = ...,
        feasibility_status: global___SolutionStatusProto.ValueType = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_objective_value", b"_objective_value", "dual_values", b"dual_values", "objective_value", b"objective_value", "quadratic_dual_values", b"quadratic_dual_values", "reduced_costs", b"reduced_costs"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_objective_value", b"_objective_value", "dual_values", b"dual_values", "feasibility_status", b"feasibility_status", "objective_value", b"objective_value", "quadratic_dual_values", b"quadratic_dual_values", "reduced_costs", b"reduced_costs"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_objective_value", b"_objective_value"]) -> typing.Literal["objective_value"] | None: ...

global___DualSolutionProto = DualSolutionProto

@typing.final
class DualRayProto(google.protobuf.message.Message):
    """A direction of unbounded improvement to the dual of an optimization,
    problem; equivalently, a certificate of primal infeasibility.

    E.g. consider the primal dual pair linear program pair:
       (Primal)              (Dual)
       min c * x             max b * y
       s.t. A * x >= b       s.t. y * A + r = c
       x >= 0                y, r >= 0.
    The dual ray is the pair (y, r) satisfying:
      b * y > 0
      y * A + r = 0
      y, r >= 0
    Observe that adding a positive multiple of (y, r) to dual feasible solution
    maintains dual feasibility and improves the objective (proving the dual is
    unbounded). The dual ray also proves the primal problem is infeasible.

    In the message DualRay below, y is dual_values and r is reduced_costs.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DUAL_VALUES_FIELD_NUMBER: builtins.int
    REDUCED_COSTS_FIELD_NUMBER: builtins.int
    @property
    def dual_values(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Requirements:
         * dual_values.ids are elements of LinearConstraints.ids.
         * dual_values.values must all be finite.
        """

    @property
    def reduced_costs(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Requirements:
         * reduced_costs.ids are elements of VariablesProto.ids.
         * reduced_costs.values must all be finite.
        """

    def __init__(
        self,
        *,
        dual_values: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        reduced_costs: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["dual_values", b"dual_values", "reduced_costs", b"reduced_costs"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["dual_values", b"dual_values", "reduced_costs", b"reduced_costs"]) -> None: ...

global___DualRayProto = DualRayProto

@typing.final
class SparseBasisStatusVector(google.protobuf.message.Message):
    """A sparse representation of a vector of basis statuses."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    IDS_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    @property
    def ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Must be sorted (in increasing ordering) with all elements distinct."""

    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[global___BasisStatusProto.ValueType]:
        """Must have equal length to ids."""

    def __init__(
        self,
        *,
        ids: collections.abc.Iterable[builtins.int] | None = ...,
        values: collections.abc.Iterable[global___BasisStatusProto.ValueType] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["ids", b"ids", "values", b"values"]) -> None: ...

global___SparseBasisStatusVector = SparseBasisStatusVector

@typing.final
class BasisProto(google.protobuf.message.Message):
    """A combinatorial characterization for a solution to a linear program.

    The simplex method for solving linear programs always returns a "basic
    feasible solution" which can be described combinatorially by a Basis. A basis
    assigns a BasisStatusProto for every variable and linear constraint.

    E.g. consider a standard form LP:
      min c * x
      s.t. A * x = b
      x >= 0
    that has more variables than constraints and with full row rank A.

    Let n be the number of variables and m the number of linear constraints. A
    valid basis for this problem can be constructed as follows:
     * All constraints will have basis status FIXED.
     * Pick m variables such that the columns of A are linearly independent and
       assign the status BASIC.
     * Assign the status AT_LOWER for the remaining n - m variables.

    The basic solution for this basis is the unique solution of A * x = b that
    has all variables with status AT_LOWER fixed to their lower bounds (all
    zero). The resulting solution is called a basic feasible solution if it also
    satisfies x >= 0.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CONSTRAINT_STATUS_FIELD_NUMBER: builtins.int
    VARIABLE_STATUS_FIELD_NUMBER: builtins.int
    BASIC_DUAL_FEASIBILITY_FIELD_NUMBER: builtins.int
    basic_dual_feasibility: global___SolutionStatusProto.ValueType
    """This is an advanced feature used by MathOpt to characterize feasibility of
    suboptimal LP solutions (optimal solutions will always have status
    SOLUTION_STATUS_FEASIBLE).

    For single-sided LPs it should be equal to the feasibility status of the
    associated dual solution. For two-sided LPs it may be different in some
    edge cases (e.g. incomplete solves with primal simplex).

    If you are providing a starting basis via
    ModelSolveParametersProto.initial_basis, this value is ignored. It is only
    relevant for the basis returned by SolutionProto.basis.
    """
    @property
    def constraint_status(self) -> global___SparseBasisStatusVector:
        """Constraint basis status.

        Requirements:
         * constraint_status.ids is equal to LinearConstraints.ids.
        """

    @property
    def variable_status(self) -> global___SparseBasisStatusVector:
        """Variable basis status.

        Requirements:
         * constraint_status.ids is equal to VariablesProto.ids.
        """

    def __init__(
        self,
        *,
        constraint_status: global___SparseBasisStatusVector | None = ...,
        variable_status: global___SparseBasisStatusVector | None = ...,
        basic_dual_feasibility: global___SolutionStatusProto.ValueType = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["constraint_status", b"constraint_status", "variable_status", b"variable_status"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["basic_dual_feasibility", b"basic_dual_feasibility", "constraint_status", b"constraint_status", "variable_status", b"variable_status"]) -> None: ...

global___BasisProto = BasisProto

@typing.final
class SolutionProto(google.protobuf.message.Message):
    """What is included in a solution depends on the kind of problem and solver.
    The current common patterns are
      1. MIP solvers return only a primal solution.
      2. Simplex LP solvers often return a basis and the primal and dual
         solutions associated to this basis.
      3. Other continuous solvers often return a primal and dual solution
         solution that are connected in a solver-dependent form.

    Requirements:
     * at least one field must be set; a solution can't be empty.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PRIMAL_SOLUTION_FIELD_NUMBER: builtins.int
    DUAL_SOLUTION_FIELD_NUMBER: builtins.int
    BASIS_FIELD_NUMBER: builtins.int
    @property
    def primal_solution(self) -> global___PrimalSolutionProto: ...
    @property
    def dual_solution(self) -> global___DualSolutionProto: ...
    @property
    def basis(self) -> global___BasisProto: ...
    def __init__(
        self,
        *,
        primal_solution: global___PrimalSolutionProto | None = ...,
        dual_solution: global___DualSolutionProto | None = ...,
        basis: global___BasisProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_basis", b"_basis", "_dual_solution", b"_dual_solution", "_primal_solution", b"_primal_solution", "basis", b"basis", "dual_solution", b"dual_solution", "primal_solution", b"primal_solution"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_basis", b"_basis", "_dual_solution", b"_dual_solution", "_primal_solution", b"_primal_solution", "basis", b"basis", "dual_solution", b"dual_solution", "primal_solution", b"primal_solution"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_basis", b"_basis"]) -> typing.Literal["basis"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_dual_solution", b"_dual_solution"]) -> typing.Literal["dual_solution"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_primal_solution", b"_primal_solution"]) -> typing.Literal["primal_solution"] | None: ...

global___SolutionProto = SolutionProto
