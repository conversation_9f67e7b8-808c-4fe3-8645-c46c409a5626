"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Configures the behavior of a MathOpt solver.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.duration_pb2
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import ortools.glop.parameters_pb2
import ortools.gscip.gscip_pb2
import ortools.math_opt.solvers.glpk_pb2
import ortools.math_opt.solvers.gurobi_pb2
import ortools.math_opt.solvers.highs_pb2
import ortools.math_opt.solvers.osqp_pb2
import ortools.pdlp.solvers_pb2
import ortools.sat.sat_parameters_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _SolverTypeProto:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SolverTypeProtoEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SolverTypeProto.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SOLVER_TYPE_UNSPECIFIED: _SolverTypeProto.ValueType  # 0
    SOLVER_TYPE_GSCIP: _SolverTypeProto.ValueType  # 1
    """Solving Constraint Integer Programs (SCIP) solver (third party).

    Supports LP, MIP, and nonconvex integer quadratic problems. No dual data
    for LPs is returned though. Prefer GLOP for LPs.
    """
    SOLVER_TYPE_GUROBI: _SolverTypeProto.ValueType  # 2
    """Gurobi solver (third party).

    Supports LP, MIP, and nonconvex integer quadratic problems. Generally the
    fastest option, but has special licensing.
    """
    SOLVER_TYPE_GLOP: _SolverTypeProto.ValueType  # 3
    """Google's Glop solver.

    Supports LP with primal and dual simplex methods.
    """
    SOLVER_TYPE_CP_SAT: _SolverTypeProto.ValueType  # 4
    """Google's CP-SAT solver.

    Supports problems where all variables are integer and bounded (or implied
    to be after presolve). Experimental support to rescale and discretize
    problems with continuous variables.
    """
    SOLVER_TYPE_PDLP: _SolverTypeProto.ValueType  # 5
    """Google's PDLP solver.

    Supports LP and convex diagonal quadratic objectives. Uses first order
    methods rather than simplex. Can solve very large problems.
    """
    SOLVER_TYPE_GLPK: _SolverTypeProto.ValueType  # 6
    """GNU Linear Programming Kit (GLPK) (third party).

    Supports MIP and LP.

    Thread-safety: GLPK use thread-local storage for memory allocations. As a
    consequence Solver instances must be destroyed on the same thread as they
    are created or GLPK will crash. It seems OK to call Solver::Solve() from
    another thread than the one used to create the Solver but it is not
    documented by GLPK and should be avoided.

    When solving a LP with the presolver, a solution (and the unbound rays) are
    only returned if an optimal solution has been found. Else nothing is
    returned. See glpk-5.0/doc/glpk.pdf page #40 available from glpk-5.0.tar.gz
    for details.
    """
    SOLVER_TYPE_OSQP: _SolverTypeProto.ValueType  # 7
    """The Operator Splitting Quadratic Program (OSQP) solver (third party).

    Supports continuous problems with linear constraints and linear or convex
    quadratic objectives. Uses a first-order method.
    """
    SOLVER_TYPE_ECOS: _SolverTypeProto.ValueType  # 8
    """The Embedded Conic Solver (ECOS) (third party).

    Supports LP and SOCP problems. Uses interior point methods (barrier).
    """
    SOLVER_TYPE_SCS: _SolverTypeProto.ValueType  # 9
    """The Splitting Conic Solver (SCS) (third party).

    Supports LP and SOCP problems. Uses a first-order method.
    """
    SOLVER_TYPE_HIGHS: _SolverTypeProto.ValueType  # 10
    """The HiGHS Solver (third party).

    Supports LP and MIP problems (convex QPs are unimplemented).
    """
    SOLVER_TYPE_SANTORINI: _SolverTypeProto.ValueType  # 11
    """MathOpt's reference implementation of a MIP solver.

    Slow/not recommended for production. Not an LP solver (no dual information
    returned).
    """
    SOLVER_TYPE_XPRESS: _SolverTypeProto.ValueType  # 13
    """Fico XPRESS solver (third party).

    Supports LP, MIP, and nonconvex integer quadratic problems.
    A fast option, but has special licensing.
    """

class SolverTypeProto(_SolverTypeProto, metaclass=_SolverTypeProtoEnumTypeWrapper):
    """The solvers supported by MathOpt."""

SOLVER_TYPE_UNSPECIFIED: SolverTypeProto.ValueType  # 0
SOLVER_TYPE_GSCIP: SolverTypeProto.ValueType  # 1
"""Solving Constraint Integer Programs (SCIP) solver (third party).

Supports LP, MIP, and nonconvex integer quadratic problems. No dual data
for LPs is returned though. Prefer GLOP for LPs.
"""
SOLVER_TYPE_GUROBI: SolverTypeProto.ValueType  # 2
"""Gurobi solver (third party).

Supports LP, MIP, and nonconvex integer quadratic problems. Generally the
fastest option, but has special licensing.
"""
SOLVER_TYPE_GLOP: SolverTypeProto.ValueType  # 3
"""Google's Glop solver.

Supports LP with primal and dual simplex methods.
"""
SOLVER_TYPE_CP_SAT: SolverTypeProto.ValueType  # 4
"""Google's CP-SAT solver.

Supports problems where all variables are integer and bounded (or implied
to be after presolve). Experimental support to rescale and discretize
problems with continuous variables.
"""
SOLVER_TYPE_PDLP: SolverTypeProto.ValueType  # 5
"""Google's PDLP solver.

Supports LP and convex diagonal quadratic objectives. Uses first order
methods rather than simplex. Can solve very large problems.
"""
SOLVER_TYPE_GLPK: SolverTypeProto.ValueType  # 6
"""GNU Linear Programming Kit (GLPK) (third party).

Supports MIP and LP.

Thread-safety: GLPK use thread-local storage for memory allocations. As a
consequence Solver instances must be destroyed on the same thread as they
are created or GLPK will crash. It seems OK to call Solver::Solve() from
another thread than the one used to create the Solver but it is not
documented by GLPK and should be avoided.

When solving a LP with the presolver, a solution (and the unbound rays) are
only returned if an optimal solution has been found. Else nothing is
returned. See glpk-5.0/doc/glpk.pdf page #40 available from glpk-5.0.tar.gz
for details.
"""
SOLVER_TYPE_OSQP: SolverTypeProto.ValueType  # 7
"""The Operator Splitting Quadratic Program (OSQP) solver (third party).

Supports continuous problems with linear constraints and linear or convex
quadratic objectives. Uses a first-order method.
"""
SOLVER_TYPE_ECOS: SolverTypeProto.ValueType  # 8
"""The Embedded Conic Solver (ECOS) (third party).

Supports LP and SOCP problems. Uses interior point methods (barrier).
"""
SOLVER_TYPE_SCS: SolverTypeProto.ValueType  # 9
"""The Splitting Conic Solver (SCS) (third party).

Supports LP and SOCP problems. Uses a first-order method.
"""
SOLVER_TYPE_HIGHS: SolverTypeProto.ValueType  # 10
"""The HiGHS Solver (third party).

Supports LP and MIP problems (convex QPs are unimplemented).
"""
SOLVER_TYPE_SANTORINI: SolverTypeProto.ValueType  # 11
"""MathOpt's reference implementation of a MIP solver.

Slow/not recommended for production. Not an LP solver (no dual information
returned).
"""
SOLVER_TYPE_XPRESS: SolverTypeProto.ValueType  # 13
"""Fico XPRESS solver (third party).

Supports LP, MIP, and nonconvex integer quadratic problems.
A fast option, but has special licensing.
"""
global___SolverTypeProto = SolverTypeProto

class _LPAlgorithmProto:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _LPAlgorithmProtoEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_LPAlgorithmProto.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    LP_ALGORITHM_UNSPECIFIED: _LPAlgorithmProto.ValueType  # 0
    LP_ALGORITHM_PRIMAL_SIMPLEX: _LPAlgorithmProto.ValueType  # 1
    """The (primal) simplex method. Typically can provide primal and dual
    solutions, primal/dual rays on primal/dual unbounded problems, and a basis.
    """
    LP_ALGORITHM_DUAL_SIMPLEX: _LPAlgorithmProto.ValueType  # 2
    """The dual simplex method. Typically can provide primal and dual
    solutions, primal/dual rays on primal/dual unbounded problems, and a basis.
    """
    LP_ALGORITHM_BARRIER: _LPAlgorithmProto.ValueType  # 3
    """The barrier method, also commonly called an interior point method (IPM).
    Can typically give both primal and dual solutions. Some implementations can
    also produce rays on unbounded/infeasible problems. A basis is not given
    unless the underlying solver does "crossover" and finishes with simplex.
    """
    LP_ALGORITHM_FIRST_ORDER: _LPAlgorithmProto.ValueType  # 4
    """An algorithm based around a first-order method. These will typically
    produce both primal and dual solutions, and potentially also certificates
    of primal and/or dual infeasibility. First-order methods typically will
    provide solutions with lower accuracy, so users should take care to set
    solution quality parameters (e.g., tolerances) and to validate solutions.
    """

class LPAlgorithmProto(_LPAlgorithmProto, metaclass=_LPAlgorithmProtoEnumTypeWrapper):
    """Selects an algorithm for solving linear programs."""

LP_ALGORITHM_UNSPECIFIED: LPAlgorithmProto.ValueType  # 0
LP_ALGORITHM_PRIMAL_SIMPLEX: LPAlgorithmProto.ValueType  # 1
"""The (primal) simplex method. Typically can provide primal and dual
solutions, primal/dual rays on primal/dual unbounded problems, and a basis.
"""
LP_ALGORITHM_DUAL_SIMPLEX: LPAlgorithmProto.ValueType  # 2
"""The dual simplex method. Typically can provide primal and dual
solutions, primal/dual rays on primal/dual unbounded problems, and a basis.
"""
LP_ALGORITHM_BARRIER: LPAlgorithmProto.ValueType  # 3
"""The barrier method, also commonly called an interior point method (IPM).
Can typically give both primal and dual solutions. Some implementations can
also produce rays on unbounded/infeasible problems. A basis is not given
unless the underlying solver does "crossover" and finishes with simplex.
"""
LP_ALGORITHM_FIRST_ORDER: LPAlgorithmProto.ValueType  # 4
"""An algorithm based around a first-order method. These will typically
produce both primal and dual solutions, and potentially also certificates
of primal and/or dual infeasibility. First-order methods typically will
provide solutions with lower accuracy, so users should take care to set
solution quality parameters (e.g., tolerances) and to validate solutions.
"""
global___LPAlgorithmProto = LPAlgorithmProto

class _EmphasisProto:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _EmphasisProtoEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_EmphasisProto.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    EMPHASIS_UNSPECIFIED: _EmphasisProto.ValueType  # 0
    EMPHASIS_OFF: _EmphasisProto.ValueType  # 1
    EMPHASIS_LOW: _EmphasisProto.ValueType  # 2
    EMPHASIS_MEDIUM: _EmphasisProto.ValueType  # 3
    EMPHASIS_HIGH: _EmphasisProto.ValueType  # 4
    EMPHASIS_VERY_HIGH: _EmphasisProto.ValueType  # 5

class EmphasisProto(_EmphasisProto, metaclass=_EmphasisProtoEnumTypeWrapper):
    """Effort level applied to an optional task while solving (see
    SolveParametersProto for use).

    Emphasis is used to configure a solver feature as follows:
     * If a solver doesn't support the feature, only UNSPECIFIED will always be
       valid, any other setting will typically an invalid argument error (some
       solvers may also accept OFF).
     * If the solver supports the feature:
       - When set to UNSPECIFIED, the underlying default is used.
       - When the feature cannot be turned off, OFF will return an error.
       - If the feature is enabled by default, the solver default is typically
         mapped to MEDIUM.
       - If the feature is supported, LOW, MEDIUM, HIGH, and VERY HIGH will never
         give an error, and will map onto their best match.
    """

EMPHASIS_UNSPECIFIED: EmphasisProto.ValueType  # 0
EMPHASIS_OFF: EmphasisProto.ValueType  # 1
EMPHASIS_LOW: EmphasisProto.ValueType  # 2
EMPHASIS_MEDIUM: EmphasisProto.ValueType  # 3
EMPHASIS_HIGH: EmphasisProto.ValueType  # 4
EMPHASIS_VERY_HIGH: EmphasisProto.ValueType  # 5
global___EmphasisProto = EmphasisProto

@typing.final
class StrictnessProto(google.protobuf.message.Message):
    """Configures if potentially bad solver input is a warning or an error.

    TODO(b/196132970): implement this feature.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BAD_PARAMETER_FIELD_NUMBER: builtins.int
    bad_parameter: builtins.bool
    def __init__(
        self,
        *,
        bad_parameter: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["bad_parameter", b"bad_parameter"]) -> None: ...

global___StrictnessProto = StrictnessProto

@typing.final
class SolverInitializerProto(google.protobuf.message.Message):
    """This message contains solver specific data that are used when the solver is
    instantiated.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    GUROBI_FIELD_NUMBER: builtins.int
    @property
    def gurobi(self) -> ortools.math_opt.solvers.gurobi_pb2.GurobiInitializerProto: ...
    def __init__(
        self,
        *,
        gurobi: ortools.math_opt.solvers.gurobi_pb2.GurobiInitializerProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["gurobi", b"gurobi"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["gurobi", b"gurobi"]) -> None: ...

global___SolverInitializerProto = SolverInitializerProto

@typing.final
class SolveParametersProto(google.protobuf.message.Message):
    """Parameters to control a single solve.

    Contains both parameters common to all solvers e.g. time_limit, and
    parameters for a specific solver, e.g. gscip. If a value is set in both
    common and solver specific field, the solver specific setting is used.

    The common parameters that are optional and unset or an enum with value
    unspecified indicate that the solver default is used.

    Solver specific parameters for solvers other than the one in use are ignored.

    Parameters that depends on the model (e.g. branching priority is set for
    each variable) are passed in ModelSolveParametersProto.
    ////////////////////////////////////////////////////////////////////////////
    Parameters common to all solvers.
    ////////////////////////////////////////////////////////////////////////////
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TIME_LIMIT_FIELD_NUMBER: builtins.int
    ITERATION_LIMIT_FIELD_NUMBER: builtins.int
    NODE_LIMIT_FIELD_NUMBER: builtins.int
    CUTOFF_LIMIT_FIELD_NUMBER: builtins.int
    OBJECTIVE_LIMIT_FIELD_NUMBER: builtins.int
    BEST_BOUND_LIMIT_FIELD_NUMBER: builtins.int
    SOLUTION_LIMIT_FIELD_NUMBER: builtins.int
    ENABLE_OUTPUT_FIELD_NUMBER: builtins.int
    THREADS_FIELD_NUMBER: builtins.int
    RANDOM_SEED_FIELD_NUMBER: builtins.int
    ABSOLUTE_GAP_TOLERANCE_FIELD_NUMBER: builtins.int
    RELATIVE_GAP_TOLERANCE_FIELD_NUMBER: builtins.int
    SOLUTION_POOL_SIZE_FIELD_NUMBER: builtins.int
    LP_ALGORITHM_FIELD_NUMBER: builtins.int
    PRESOLVE_FIELD_NUMBER: builtins.int
    CUTS_FIELD_NUMBER: builtins.int
    HEURISTICS_FIELD_NUMBER: builtins.int
    SCALING_FIELD_NUMBER: builtins.int
    GSCIP_FIELD_NUMBER: builtins.int
    GUROBI_FIELD_NUMBER: builtins.int
    GLOP_FIELD_NUMBER: builtins.int
    CP_SAT_FIELD_NUMBER: builtins.int
    PDLP_FIELD_NUMBER: builtins.int
    OSQP_FIELD_NUMBER: builtins.int
    GLPK_FIELD_NUMBER: builtins.int
    HIGHS_FIELD_NUMBER: builtins.int
    iteration_limit: builtins.int
    """Limit on the iterations of the underlying algorithm (e.g. simplex pivots).
    The specific behavior is dependent on the solver and algorithm used, but
    often can give a deterministic solve limit (further configuration may be
    needed, e.g. one thread).

    Typically supported by LP, QP, and MIP solvers, but for MIP solvers see
    also node_limit.
    """
    node_limit: builtins.int
    """Limit on the number of subproblems solved in enumerative search (e.g.
    branch and bound). For many solvers this can be used to deterministically
    limit computation (further configuration may be needed, e.g. one thread).

    Typically for MIP solvers, see also iteration_limit.
    """
    cutoff_limit: builtins.float
    """The solver stops early if it can prove there are no primal solutions at
    least as good as cutoff.

    On an early stop, the solver returns termination reason NO_SOLUTION_FOUND
    and with limit CUTOFF and is not required to give any extra solution
    information. Has no effect on the return value if there is no early stop.

    It is recommended that you use a tolerance if you want solutions with
    objective exactly equal to cutoff to be returned.

    See the user guide for more details and a comparison with best_bound_limit.
    """
    objective_limit: builtins.float
    """The solver stops early as soon as it finds a solution at least this good,
    with termination reason FEASIBLE and limit OBJECTIVE.
    """
    best_bound_limit: builtins.float
    """The solver stops early as soon as it proves the best bound is at least this
    good, with termination reason FEASIBLE or NO_SOLUTION_FOUND and limit
    OBJECTIVE.

    See the user guide for more details and a comparison with cutoff_limit.
    """
    solution_limit: builtins.int
    """The solver stops early after finding this many feasible solutions, with
    termination reason FEASIBLE and limit SOLUTION. Must be greater than zero
    if set. It is often used get the solver to stop on the first feasible
    solution found. Note that there is no guarantee on the objective value for
    any of the returned solutions.

    Solvers will typically not return more solutions than the solution limit,
    but this is not enforced by MathOpt, see also b/214041169.

    Currently supported for Gurobi and SCIP, and for CP-SAT only with value 1.
    """
    enable_output: builtins.bool
    """Enables printing the solver implementation traces. The location of those
    traces depend on the solver. For SCIP and Gurobi this will be the standard
    output streams. For Glop and CP-SAT this will LOG(INFO).

    Note that if the solver supports message callback and the user registers a
    callback for it, then this parameter value is ignored and no traces are
    printed.
    """
    threads: builtins.int
    """If set, it must be >= 1."""
    random_seed: builtins.int
    """Seed for the pseudo-random number generator in the underlying
    solver. Note that all solvers use pseudo-random numbers to select things
    such as perturbation in the LP algorithm, for tie-break-up rules, and for
    heuristic fixings. Varying this can have a noticeable impact on solver
    behavior.

    Although all solvers have a concept of seeds, note that valid values
    depend on the actual solver.
    - Gurobi: [0:GRB_MAXINT] (which as of Gurobi 9.0 is 2x10^9).
    - GSCIP:  [0:2147483647] (which is MAX_INT or kint32max or 2^31-1).
    - GLOP:   [0:2147483647] (same as above)
    In all cases, the solver will receive a value equal to:
    MAX(0, MIN(MAX_VALID_VALUE_FOR_SOLVER, random_seed)).
    """
    absolute_gap_tolerance: builtins.float
    """An absolute optimality tolerance (primarily) for MIP solvers.

    The absolute GAP is the absolute value of the difference between:
      * the objective value of the best feasible solution found,
      * the dual bound produced by the search.
    The solver can stop once the absolute GAP is at most absolute_gap_tolerance
    (when set), and return TERMINATION_REASON_OPTIMAL.

    Must be >= 0 if set.

    See also relative_gap_tolerance.
    """
    relative_gap_tolerance: builtins.float
    """A relative optimality tolerance (primarily) for MIP solvers.

    The relative GAP is a normalized version of the absolute GAP (defined on
    absolute_gap_tolerance), where the normalization is solver-dependent, e.g.
    the absolute GAP divided by the objective value of the best feasible
    solution found.

    The solver can stop once the relative GAP is at most relative_gap_tolerance
    (when set), and return TERMINATION_REASON_OPTIMAL.

    Must be >= 0 if set.

    See also absolute_gap_tolerance.
    """
    solution_pool_size: builtins.int
    """Maintain up to `solution_pool_size` solutions while searching. The solution
    pool generally has two functions:
     (1) For solvers that can return more than one solution, this limits how
         many solutions will be returned.
     (2) Some solvers may run heuristics using solutions from the solution
         pool, so changing this value may affect the algorithm's path.
    To force the solver to fill the solution pool, e.g. with the n best
    solutions, requires further, solver specific configuration.
    """
    lp_algorithm: global___LPAlgorithmProto.ValueType
    """The algorithm for solving a linear program. If LP_ALGORITHM_UNSPECIFIED,
    use the solver default algorithm.

    For problems that are not linear programs but where linear programming is
    a subroutine, solvers may use this value. E.g. MIP solvers will typically
    use this for the root LP solve only (and use dual simplex otherwise).
    """
    presolve: global___EmphasisProto.ValueType
    """Effort on simplifying the problem before starting the main algorithm, or
    the solver default effort level if EMPHASIS_UNSPECIFIED.
    """
    cuts: global___EmphasisProto.ValueType
    """Effort on getting a stronger LP relaxation (MIP only), or the solver
    default effort level if EMPHASIS_UNSPECIFIED.

    NOTE: disabling cuts may prevent callbacks from having a chance to add cuts
    at MIP_NODE, this behavior is solver specific.
    """
    heuristics: global___EmphasisProto.ValueType
    """Effort in finding feasible solutions beyond those encountered in the
    complete search procedure (MIP only), or the solver default effort level if
    EMPHASIS_UNSPECIFIED.
    """
    scaling: global___EmphasisProto.ValueType
    """Effort in rescaling the problem to improve numerical stability, or the
    solver default effort level if EMPHASIS_UNSPECIFIED.
    """
    @property
    def time_limit(self) -> google.protobuf.duration_pb2.Duration:
        """Maximum time a solver should spend on the problem (or infinite if not set).

        This value is not a hard limit, solve time may slightly exceed this value.
        This parameter is always passed to the underlying solver, the solver
        default is not used.
        """

    @property
    def gscip(self) -> ortools.gscip.gscip_pb2.GScipParameters:
        """////////////////////////////////////////////////////////////////////////////
        Solver specific parameters
        ////////////////////////////////////////////////////////////////////////////
        """

    @property
    def gurobi(self) -> ortools.math_opt.solvers.gurobi_pb2.GurobiParametersProto: ...
    @property
    def glop(self) -> ortools.glop.parameters_pb2.GlopParameters: ...
    @property
    def cp_sat(self) -> ortools.sat.sat_parameters_pb2.SatParameters: ...
    @property
    def pdlp(self) -> ortools.pdlp.solvers_pb2.PrimalDualHybridGradientParams: ...
    @property
    def osqp(self) -> ortools.math_opt.solvers.osqp_pb2.OsqpSettingsProto:
        """Users should prefer the generic MathOpt parameters over OSQP-level
        parameters, when available:
          * Prefer SolveParametersProto.enable_output to OsqpSettingsProto.verbose.
          * Prefer SolveParametersProto.time_limit to OsqpSettingsProto.time_limit.
          * Prefer SolveParametersProto.iteration_limit to
            OsqpSettingsProto.iteration_limit.
          * If a less granular configuration is acceptable, prefer
            SolveParametersProto.scaling to OsqpSettingsProto.
        """

    @property
    def glpk(self) -> ortools.math_opt.solvers.glpk_pb2.GlpkParametersProto: ...
    @property
    def highs(self) -> ortools.math_opt.solvers.highs_pb2.HighsOptionsProto: ...
    def __init__(
        self,
        *,
        time_limit: google.protobuf.duration_pb2.Duration | None = ...,
        iteration_limit: builtins.int | None = ...,
        node_limit: builtins.int | None = ...,
        cutoff_limit: builtins.float | None = ...,
        objective_limit: builtins.float | None = ...,
        best_bound_limit: builtins.float | None = ...,
        solution_limit: builtins.int | None = ...,
        enable_output: builtins.bool = ...,
        threads: builtins.int | None = ...,
        random_seed: builtins.int | None = ...,
        absolute_gap_tolerance: builtins.float | None = ...,
        relative_gap_tolerance: builtins.float | None = ...,
        solution_pool_size: builtins.int | None = ...,
        lp_algorithm: global___LPAlgorithmProto.ValueType = ...,
        presolve: global___EmphasisProto.ValueType = ...,
        cuts: global___EmphasisProto.ValueType = ...,
        heuristics: global___EmphasisProto.ValueType = ...,
        scaling: global___EmphasisProto.ValueType = ...,
        gscip: ortools.gscip.gscip_pb2.GScipParameters | None = ...,
        gurobi: ortools.math_opt.solvers.gurobi_pb2.GurobiParametersProto | None = ...,
        glop: ortools.glop.parameters_pb2.GlopParameters | None = ...,
        cp_sat: ortools.sat.sat_parameters_pb2.SatParameters | None = ...,
        pdlp: ortools.pdlp.solvers_pb2.PrimalDualHybridGradientParams | None = ...,
        osqp: ortools.math_opt.solvers.osqp_pb2.OsqpSettingsProto | None = ...,
        glpk: ortools.math_opt.solvers.glpk_pb2.GlpkParametersProto | None = ...,
        highs: ortools.math_opt.solvers.highs_pb2.HighsOptionsProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_absolute_gap_tolerance", b"_absolute_gap_tolerance", "_best_bound_limit", b"_best_bound_limit", "_cutoff_limit", b"_cutoff_limit", "_iteration_limit", b"_iteration_limit", "_node_limit", b"_node_limit", "_objective_limit", b"_objective_limit", "_random_seed", b"_random_seed", "_relative_gap_tolerance", b"_relative_gap_tolerance", "_solution_limit", b"_solution_limit", "_solution_pool_size", b"_solution_pool_size", "_threads", b"_threads", "absolute_gap_tolerance", b"absolute_gap_tolerance", "best_bound_limit", b"best_bound_limit", "cp_sat", b"cp_sat", "cutoff_limit", b"cutoff_limit", "glop", b"glop", "glpk", b"glpk", "gscip", b"gscip", "gurobi", b"gurobi", "highs", b"highs", "iteration_limit", b"iteration_limit", "node_limit", b"node_limit", "objective_limit", b"objective_limit", "osqp", b"osqp", "pdlp", b"pdlp", "random_seed", b"random_seed", "relative_gap_tolerance", b"relative_gap_tolerance", "solution_limit", b"solution_limit", "solution_pool_size", b"solution_pool_size", "threads", b"threads", "time_limit", b"time_limit"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_absolute_gap_tolerance", b"_absolute_gap_tolerance", "_best_bound_limit", b"_best_bound_limit", "_cutoff_limit", b"_cutoff_limit", "_iteration_limit", b"_iteration_limit", "_node_limit", b"_node_limit", "_objective_limit", b"_objective_limit", "_random_seed", b"_random_seed", "_relative_gap_tolerance", b"_relative_gap_tolerance", "_solution_limit", b"_solution_limit", "_solution_pool_size", b"_solution_pool_size", "_threads", b"_threads", "absolute_gap_tolerance", b"absolute_gap_tolerance", "best_bound_limit", b"best_bound_limit", "cp_sat", b"cp_sat", "cutoff_limit", b"cutoff_limit", "cuts", b"cuts", "enable_output", b"enable_output", "glop", b"glop", "glpk", b"glpk", "gscip", b"gscip", "gurobi", b"gurobi", "heuristics", b"heuristics", "highs", b"highs", "iteration_limit", b"iteration_limit", "lp_algorithm", b"lp_algorithm", "node_limit", b"node_limit", "objective_limit", b"objective_limit", "osqp", b"osqp", "pdlp", b"pdlp", "presolve", b"presolve", "random_seed", b"random_seed", "relative_gap_tolerance", b"relative_gap_tolerance", "scaling", b"scaling", "solution_limit", b"solution_limit", "solution_pool_size", b"solution_pool_size", "threads", b"threads", "time_limit", b"time_limit"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_absolute_gap_tolerance", b"_absolute_gap_tolerance"]) -> typing.Literal["absolute_gap_tolerance"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_best_bound_limit", b"_best_bound_limit"]) -> typing.Literal["best_bound_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_cutoff_limit", b"_cutoff_limit"]) -> typing.Literal["cutoff_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_iteration_limit", b"_iteration_limit"]) -> typing.Literal["iteration_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_node_limit", b"_node_limit"]) -> typing.Literal["node_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_objective_limit", b"_objective_limit"]) -> typing.Literal["objective_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_random_seed", b"_random_seed"]) -> typing.Literal["random_seed"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_relative_gap_tolerance", b"_relative_gap_tolerance"]) -> typing.Literal["relative_gap_tolerance"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_solution_limit", b"_solution_limit"]) -> typing.Literal["solution_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_solution_pool_size", b"_solution_pool_size"]) -> typing.Literal["solution_pool_size"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_threads", b"_threads"]) -> typing.Literal["threads"] | None: ...

global___SolveParametersProto = SolveParametersProto
