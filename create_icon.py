"""
Create a simple icon for the application
This creates a basic icon - you can replace with a professional one later
"""
try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    def create_dental_icon():
        # Create a 256x256 image with transparent background
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Draw a tooth-like shape (simplified)
        # Main tooth body (rounded rectangle)
        margin = 20
        tooth_color = (255, 255, 255, 255)  # White
        outline_color = (100, 100, 100, 255)  # Gray
        
        # Tooth crown (top part)
        crown_rect = [margin, margin, size-margin, size//2]
        draw.rounded_rectangle(crown_rect, radius=30, fill=tooth_color, outline=outline_color, width=4)
        
        # Tooth roots (bottom part)
        root_width = (size - 2*margin) // 3
        root_height = size//3
        
        # Left root
        left_root = [margin + 10, size//2 - 10, margin + root_width, size - margin]
        draw.rounded_rectangle(left_root, radius=15, fill=tooth_color, outline=outline_color, width=3)
        
        # Right root  
        right_root = [size - margin - root_width, size//2 - 10, size - margin - 10, size - margin]
        draw.rounded_rectangle(right_root, radius=15, fill=tooth_color, outline=outline_color, width=3)
        
        # Add a small gear/schedule symbol
        gear_center = (size - 60, 60)
        gear_radius = 25
        gear_color = (70, 130, 180, 255)  # Steel blue
        
        # Draw gear (simplified as circle with spokes)
        draw.ellipse([gear_center[0] - gear_radius, gear_center[1] - gear_radius,
                     gear_center[0] + gear_radius, gear_center[1] + gear_radius],
                    fill=gear_color, outline=(50, 100, 150, 255), width=2)
        
        # Add gear teeth (small rectangles around the circle)
        for angle in range(0, 360, 45):
            import math
            x = gear_center[0] + (gear_radius + 8) * math.cos(math.radians(angle))
            y = gear_center[1] + (gear_radius + 8) * math.sin(math.radians(angle))
            draw.rectangle([x-3, y-3, x+3, y+3], fill=gear_color)
        
        # Save as ICO file (Windows icon format)
        # Create multiple sizes for the icon
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        icon_images = []
        
        for icon_size in sizes:
            resized = img.resize(icon_size, Image.Resampling.LANCZOS)
            icon_images.append(resized)
        
        # Save as .ico file
        icon_images[0].save('icon.ico', format='ICO', sizes=[(img.width, img.height) for img in icon_images])
        print("✓ Icon created: icon.ico")
        
        # Also save as PNG for other uses
        img.save('icon.png', format='PNG')
        print("✓ Icon created: icon.png")
        
        return True

except ImportError:
    print("⚠ PIL (Pillow) not available, creating placeholder icon...")
    # Create a simple text-based icon file
    with open('icon_placeholder.txt', 'w') as f:
        f.write("Icon placeholder - replace with professional dental icon")

    def create_dental_icon():
        return False

if __name__ == "__main__":
    create_dental_icon()
