# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/packing/vector_bin_packing.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/packing/vector_bin_packing.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(ortools/packing/vector_bin_packing.proto\x12\x1foperations_research.packing.vbp\"\xa5\x01\n\x04Item\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x16\n\x0eresource_usage\x18\x02 \x03(\x03\x12\x12\n\nnum_copies\x18\x03 \x01(\x05\x12\x1b\n\x13num_optional_copies\x18\x05 \x01(\x05\x12$\n\x1cmax_number_of_copies_per_bin\x18\x04 \x01(\x05\x12 \n\x18penalty_per_missing_copy\x18\x06 \x01(\x01\"\xcc\x01\n\x17VectorBinPackingProblem\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x19\n\x11resource_capacity\x18\x02 \x03(\x03\x12\x15\n\rresource_name\x18\x03 \x03(\t\x12\x33\n\x04item\x18\x04 \x03(\x0b\x32%.operations_research.packing.vbp.Item\x12\x10\n\x08max_bins\x18\x05 \x01(\x05\x12\x19\n\x0c\x63ost_per_bin\x18\x06 \x01(\x01H\x00\x88\x01\x01\x42\x0f\n\r_cost_per_bin\"M\n VectorBinPackingOneBinInSolution\x12\x14\n\x0citem_indices\x18\x01 \x03(\x05\x12\x13\n\x0bitem_copies\x18\x02 \x03(\x05\"\xa8\x02\n\x18VectorBinPackingSolution\x12\x13\n\x0bsolver_info\x18\x01 \x01(\t\x12O\n\x04\x62ins\x18\x02 \x03(\x0b\x32\x41.operations_research.packing.vbp.VectorBinPackingOneBinInSolution\x12L\n\x06status\x18\x03 \x01(\x0e\x32<.operations_research.packing.vbp.VectorBinPackingSolveStatus\x12\x17\n\x0fobjective_value\x18\x04 \x01(\x01\x12\x1d\n\x15solve_time_in_seconds\x18\x05 \x01(\x01\x12 \n\x18\x61rc_flow_time_in_seconds\x18\x06 \x01(\x01*y\n\x1bVectorBinPackingSolveStatus\x12/\n+VECTOR_BIN_PACKING_SOLVE_STATUS_UNSPECIFIED\x10\x00\x12\x0b\n\x07OPTIMAL\x10\x01\x12\x0c\n\x08\x46\x45\x41SIBLE\x10\x02\x12\x0e\n\nINFEASIBLE\x10\x03\x42?\n\x1e\x63om.google.ortools.packing.vbpP\x01\xaa\x02\x1aGoogle.OrTools.Packing.Vbpb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.packing.vector_bin_packing_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\036com.google.ortools.packing.vbpP\001\252\002\032Google.OrTools.Packing.Vbp'
  _globals['_VECTORBINPACKINGSOLVESTATUS']._serialized_start=830
  _globals['_VECTORBINPACKINGSOLVESTATUS']._serialized_end=951
  _globals['_ITEM']._serialized_start=78
  _globals['_ITEM']._serialized_end=243
  _globals['_VECTORBINPACKINGPROBLEM']._serialized_start=246
  _globals['_VECTORBINPACKINGPROBLEM']._serialized_end=450
  _globals['_VECTORBINPACKINGONEBININSOLUTION']._serialized_start=452
  _globals['_VECTORBINPACKINGONEBININSOLUTION']._serialized_end=529
  _globals['_VECTORBINPACKINGSOLUTION']._serialized_start=532
  _globals['_VECTORBINPACKINGSOLUTION']._serialized_end=828
# @@protoc_insertion_point(module_scope)
