# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/service/v1/mathopt/model_parameters.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/service/v1/mathopt/model_parameters.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.service.v1.mathopt import solution_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_solution__pb2
from ortools.service.v1.mathopt import sparse_containers_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_sparse__containers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1ortools/service/v1/mathopt/model_parameters.proto\x12&operations_research.service.v1.mathopt\x1a)ortools/service/v1/mathopt/solution.proto\x1a\x32ortools/service/v1/mathopt/sparse_containers.proto\"\xc3\x01\n\x11SolutionHintProto\x12X\n\x0fvariable_values\x18\x01 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\x12T\n\x0b\x64ual_values\x18\x02 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\"\xb4\x04\n\x19ModelSolveParametersProto\x12_\n\x16variable_values_filter\x18\x01 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseVectorFilterProto\x12[\n\x12\x64ual_values_filter\x18\x02 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseVectorFilterProto\x12]\n\x14reduced_costs_filter\x18\x03 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseVectorFilterProto\x12I\n\rinitial_basis\x18\x04 \x01(\x0b\x32\x32.operations_research.service.v1.mathopt.BasisProto\x12Q\n\x0esolution_hints\x18\x05 \x03(\x0b\x32\x39.operations_research.service.v1.mathopt.SolutionHintProto\x12\\\n\x14\x62ranching_priorities\x18\x06 \x01(\x0b\x32>.operations_research.service.v1.mathopt.SparseInt32VectorProtoBB\n%com.google.ortools.service.v1.mathoptP\x01\xaa\x02\x16Google.OrTools.Serviceb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.service.v1.mathopt.model_parameters_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.google.ortools.service.v1.mathoptP\001\252\002\026Google.OrTools.Service'
  _globals['_SOLUTIONHINTPROTO']._serialized_start=189
  _globals['_SOLUTIONHINTPROTO']._serialized_end=384
  _globals['_MODELSOLVEPARAMETERSPROTO']._serialized_start=387
  _globals['_MODELSOLVEPARAMETERSPROTO']._serialized_end=951
# @@protoc_insertion_point(module_scope)
