# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/util/optional_boolean.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/util/optional_boolean.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#ortools/util/optional_boolean.proto\x12\x13operations_research*F\n\x0fOptionalBoolean\x12\x14\n\x10\x42OOL_UNSPECIFIED\x10\x00\x12\x0e\n\nBOOL_FALSE\x10\x02\x12\r\n\tBOOL_TRUE\x10\x03\x42\x31\n\x17\x63om.google.ortools.utilP\x01\xaa\x02\x13Google.OrTools.Utilb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.util.optional_boolean_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\027com.google.ortools.utilP\001\252\002\023Google.OrTools.Util'
  _globals['_OPTIONALBOOLEAN']._serialized_start=60
  _globals['_OPTIONALBOOLEAN']._serialized_end=130
# @@protoc_insertion_point(module_scope)
