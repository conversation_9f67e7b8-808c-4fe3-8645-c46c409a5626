# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/service/v1/optimization.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/service/v1/optimization.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.service.v1.mathopt import model_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_model__pb2
from ortools.service.v1.mathopt import model_parameters_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_model__parameters__pb2
from ortools.service.v1.mathopt import parameters_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_parameters__pb2
from ortools.service.v1.mathopt import result_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_result__pb2
from ortools.service.v1.mathopt import solver_resources_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_solver__resources__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n%ortools/service/v1/optimization.proto\x12\x1eoperations_research.service.v1\x1a&ortools/service/v1/mathopt/model.proto\x1a\x31ortools/service/v1/mathopt/model_parameters.proto\x1a+ortools/service/v1/mathopt/parameters.proto\x1a\'ortools/service/v1/mathopt/result.proto\x1a\x31ortools/service/v1/mathopt/solver_resources.proto\"\xab\x03\n\x18SolveMathOptModelRequest\x12L\n\x0bsolver_type\x18\x01 \x01(\x0e\x32\x37.operations_research.service.v1.mathopt.SolverTypeProto\x12\x41\n\x05model\x18\x02 \x01(\x0b\x32\x32.operations_research.service.v1.mathopt.ModelProto\x12O\n\tresources\x18\x06 \x01(\x0b\x32<.operations_research.service.v1.mathopt.SolverResourcesProto\x12P\n\nparameters\x18\x04 \x01(\x0b\x32<.operations_research.service.v1.mathopt.SolveParametersProto\x12[\n\x10model_parameters\x18\x05 \x01(\x0b\x32\x41.operations_research.service.v1.mathopt.ModelSolveParametersProto\"w\n\x19SolveMathOptModelResponse\x12H\n\x06result\x18\x01 \x01(\x0b\x32\x38.operations_research.service.v1.mathopt.SolveResultProto\x12\x10\n\x08messages\x18\x02 \x03(\t2\x9b\x01\n\x0cOptimization\x12\x8a\x01\n\x11SolveMathOptModel\x12\x38.operations_research.service.v1.SolveMathOptModelRequest\x1a\x39.operations_research.service.v1.SolveMathOptModelResponse\"\x00\x42:\n\x1d\x63om.google.ortools.service.v1P\x01\xaa\x02\x16Google.OrTools.Serviceb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.service.v1.optimization_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.google.ortools.service.v1P\001\252\002\026Google.OrTools.Service'
  _globals['_SOLVEMATHOPTMODELREQUEST']._serialized_start=302
  _globals['_SOLVEMATHOPTMODELREQUEST']._serialized_end=729
  _globals['_SOLVEMATHOPTMODELRESPONSE']._serialized_start=731
  _globals['_SOLVEMATHOPTMODELRESPONSE']._serialized_end=850
  _globals['_OPTIMIZATION']._serialized_start=853
  _globals['_OPTIMIZATION']._serialized_end=1008
# @@protoc_insertion_point(module_scope)
