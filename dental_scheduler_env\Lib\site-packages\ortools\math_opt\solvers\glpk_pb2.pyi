"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Proto messages specific to GLPK.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class GlpkParametersProto(google.protobuf.message.Message):
    """GLPK specific parameters for solving.

    Fields are optional to enable to capture user intention; if they set
    explicitly a value to then no generic solve parameters will overwrite this
    parameter. User specified solver specific parameters have priority on generic
    parameters.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COMPUTE_UNBOUND_RAYS_IF_POSSIBLE_FIELD_NUMBER: builtins.int
    compute_unbound_rays_if_possible: builtins.bool
    """Compute the primal or dual unbound ray when the variable (structural or
    auxiliary) causing the unboundness is identified (see glp_get_unbnd_ray()).

    The unset value is equivalent to false.

    Rays are only available when solving linear programs, they are not
    available for MIPs. On top of that they are only available when using a
    simplex algorithm with the presolve disabled.

    A primal ray can only be built if the chosen LP algorithm is
    LP_ALGORITHM_PRIMAL_SIMPLEX. Same for a dual ray and
    LP_ALGORITHM_DUAL_SIMPLEX.

    The computation involves the basis factorization to be available which may
    lead to extra computations/errors.
    """
    def __init__(
        self,
        *,
        compute_unbound_rays_if_possible: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_compute_unbound_rays_if_possible", b"_compute_unbound_rays_if_possible", "compute_unbound_rays_if_possible", b"compute_unbound_rays_if_possible"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_compute_unbound_rays_if_possible", b"_compute_unbound_rays_if_possible", "compute_unbound_rays_if_possible", b"compute_unbound_rays_if_possible"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_compute_unbound_rays_if_possible", b"_compute_unbound_rays_if_possible"]) -> typing.Literal["compute_unbound_rays_if_possible"] | None: ...

global___GlpkParametersProto = GlpkParametersProto
