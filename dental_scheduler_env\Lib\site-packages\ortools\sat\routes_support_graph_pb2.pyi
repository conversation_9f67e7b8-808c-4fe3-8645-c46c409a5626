"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class ArcLpValue(google.protobuf.message.Message):
    """An arc of a routes constraint, with its LP value."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TAIL_FIELD_NUMBER: builtins.int
    HEAD_FIELD_NUMBER: builtins.int
    LP_VALUE_FIELD_NUMBER: builtins.int
    tail: builtins.int
    head: builtins.int
    lp_value: builtins.float
    def __init__(
        self,
        *,
        tail: builtins.int | None = ...,
        head: builtins.int | None = ...,
        lp_value: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["head", b"head", "lp_value", b"lp_value", "tail", b"tail"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["head", b"head", "lp_value", b"lp_value", "tail", b"tail"]) -> None: ...

global___ArcLpValue = ArcLpValue

@typing.final
class RoutesSupportGraphProto(google.protobuf.message.Message):
    """The arcs of a routes constraint which have non-zero LP values, in the LP
    relaxation of the problem.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ARC_LP_VALUES_FIELD_NUMBER: builtins.int
    @property
    def arc_lp_values(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ArcLpValue]: ...
    def __init__(
        self,
        *,
        arc_lp_values: collections.abc.Iterable[global___ArcLpValue] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["arc_lp_values", b"arc_lp_values"]) -> None: ...

global___RoutesSupportGraphProto = RoutesSupportGraphProto
