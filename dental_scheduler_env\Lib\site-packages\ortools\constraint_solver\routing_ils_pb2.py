# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/constraint_solver/routing_ils.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/constraint_solver/routing_ils.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.constraint_solver import routing_enums_pb2 as ortools_dot_constraint__solver_dot_routing__enums__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+ortools/constraint_solver/routing_ils.proto\x12\x13operations_research\x1a-ortools/constraint_solver/routing_enums.proto\"X\n SpatiallyCloseRoutesRuinStrategy\x12\x1e\n\x11num_ruined_routes\x18\x03 \x01(\rH\x00\x88\x01\x01\x42\x14\n\x12_num_ruined_routes\"P\n\x16RandomWalkRuinStrategy\x12\x1f\n\x12num_removed_visits\x18\x07 \x01(\rH\x00\x88\x01\x01\x42\x15\n\x13_num_removed_visits\"\xc6\x01\n\x10SISRRuinStrategy\x12&\n\x19max_removed_sequence_size\x18\x01 \x01(\rH\x00\x88\x01\x01\x12#\n\x16\x61vg_num_removed_visits\x18\x02 \x01(\rH\x01\x88\x01\x01\x12\x1a\n\rbypass_factor\x18\x03 \x01(\x01H\x02\x88\x01\x01\x42\x1c\n\x1a_max_removed_sequence_sizeB\x19\n\x17_avg_num_removed_visitsB\x10\n\x0e_bypass_factor\"\xee\x01\n\x0cRuinStrategy\x12W\n\x16spatially_close_routes\x18\x01 \x01(\x0b\x32\x35.operations_research.SpatiallyCloseRoutesRuinStrategyH\x00\x12\x42\n\x0brandom_walk\x18\x02 \x01(\x0b\x32+.operations_research.RandomWalkRuinStrategyH\x00\x12\x35\n\x04sisr\x18\x03 \x01(\x0b\x32%.operations_research.SISRRuinStrategyH\x00\x42\n\n\x08strategy\"s\n\x17RuinCompositionStrategy\"X\n\x05Value\x12\t\n\x05UNSET\x10\x00\x12\x18\n\x14RUN_ALL_SEQUENTIALLY\x10\x01\x12\x14\n\x10RUN_ALL_RANDOMLY\x10\x02\x12\x14\n\x10RUN_ONE_RANDOMLY\x10\x03\"\xe6\x03\n\x16RuinRecreateParameters\x12:\n\x0fruin_strategies\x18\x01 \x03(\x0b\x32!.operations_research.RuinStrategy\x12U\n\x19ruin_composition_strategy\x18\x02 \x01(\x0e\x32\x32.operations_research.RuinCompositionStrategy.Value\x12K\n\x11recreate_strategy\x18\x03 \x01(\x0e\x32\x30.operations_research.FirstSolutionStrategy.Value\x12,\n\x1froute_selection_neighbors_ratio\x18\x04 \x01(\x01H\x00\x88\x01\x01\x12*\n\x1droute_selection_min_neighbors\x18\x05 \x01(\rH\x01\x88\x01\x01\x12*\n\x1droute_selection_max_neighbors\x18\x06 \x01(\rH\x02\x88\x01\x01\x42\"\n _route_selection_neighbors_ratioB \n\x1e_route_selection_min_neighborsB \n\x1e_route_selection_max_neighbors\"A\n\x14PerturbationStrategy\")\n\x05Value\x12\t\n\x05UNSET\x10\x00\x12\x15\n\x11RUIN_AND_RECREATE\x10\x01\"J\n\x17\x43oolingScheduleStrategy\"/\n\x05Value\x12\t\n\x05UNSET\x10\x00\x12\x0f\n\x0b\x45XPONENTIAL\x10\x01\x12\n\n\x06LINEAR\x10\x02\"\xa5\x02\n\x1cSimulatedAnnealingParameters\x12U\n\x19\x63ooling_schedule_strategy\x18\x01 \x01(\x0e\x32\x32.operations_research.CoolingScheduleStrategy.Value\x12 \n\x13initial_temperature\x18\x02 \x01(\x01H\x00\x88\x01\x01\x12\x1e\n\x11\x66inal_temperature\x18\x03 \x01(\x01H\x01\x88\x01\x01\x12#\n\x16\x61utomatic_temperatures\x18\x04 \x01(\x08H\x02\x88\x01\x01\x42\x16\n\x14_initial_temperatureB\x14\n\x12_final_temperatureB\x19\n\x17_automatic_temperatures\"U\n\x12\x41\x63\x63\x65ptanceStrategy\"?\n\x05Value\x12\t\n\x05UNSET\x10\x00\x12\x12\n\x0eGREEDY_DESCENT\x10\x01\x12\x17\n\x13SIMULATED_ANNEALING\x10\x02\"\xad\x03\n\x1dIteratedLocalSearchParameters\x12N\n\x15perturbation_strategy\x18\x01 \x01(\x0e\x32/.operations_research.PerturbationStrategy.Value\x12M\n\x18ruin_recreate_parameters\x18\x02 \x01(\x0b\x32+.operations_research.RuinRecreateParameters\x12\'\n\x1aimprove_perturbed_solution\x18\x03 \x01(\x08H\x00\x88\x01\x01\x12J\n\x13\x61\x63\x63\x65ptance_strategy\x18\x04 \x01(\x0e\x32-.operations_research.AcceptanceStrategy.Value\x12Y\n\x1esimulated_annealing_parameters\x18\x05 \x01(\x0b\x32\x31.operations_research.SimulatedAnnealingParametersB\x1d\n\x1b_improve_perturbed_solutionBI\n#com.google.ortools.constraintsolverP\x01\xaa\x02\x1fGoogle.OrTools.ConstraintSolverb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.constraint_solver.routing_ils_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.google.ortools.constraintsolverP\001\252\002\037Google.OrTools.ConstraintSolver'
  _globals['_SPATIALLYCLOSEROUTESRUINSTRATEGY']._serialized_start=115
  _globals['_SPATIALLYCLOSEROUTESRUINSTRATEGY']._serialized_end=203
  _globals['_RANDOMWALKRUINSTRATEGY']._serialized_start=205
  _globals['_RANDOMWALKRUINSTRATEGY']._serialized_end=285
  _globals['_SISRRUINSTRATEGY']._serialized_start=288
  _globals['_SISRRUINSTRATEGY']._serialized_end=486
  _globals['_RUINSTRATEGY']._serialized_start=489
  _globals['_RUINSTRATEGY']._serialized_end=727
  _globals['_RUINCOMPOSITIONSTRATEGY']._serialized_start=729
  _globals['_RUINCOMPOSITIONSTRATEGY']._serialized_end=844
  _globals['_RUINCOMPOSITIONSTRATEGY_VALUE']._serialized_start=756
  _globals['_RUINCOMPOSITIONSTRATEGY_VALUE']._serialized_end=844
  _globals['_RUINRECREATEPARAMETERS']._serialized_start=847
  _globals['_RUINRECREATEPARAMETERS']._serialized_end=1333
  _globals['_PERTURBATIONSTRATEGY']._serialized_start=1335
  _globals['_PERTURBATIONSTRATEGY']._serialized_end=1400
  _globals['_PERTURBATIONSTRATEGY_VALUE']._serialized_start=1359
  _globals['_PERTURBATIONSTRATEGY_VALUE']._serialized_end=1400
  _globals['_COOLINGSCHEDULESTRATEGY']._serialized_start=1402
  _globals['_COOLINGSCHEDULESTRATEGY']._serialized_end=1476
  _globals['_COOLINGSCHEDULESTRATEGY_VALUE']._serialized_start=1429
  _globals['_COOLINGSCHEDULESTRATEGY_VALUE']._serialized_end=1476
  _globals['_SIMULATEDANNEALINGPARAMETERS']._serialized_start=1479
  _globals['_SIMULATEDANNEALINGPARAMETERS']._serialized_end=1772
  _globals['_ACCEPTANCESTRATEGY']._serialized_start=1774
  _globals['_ACCEPTANCESTRATEGY']._serialized_end=1859
  _globals['_ACCEPTANCESTRATEGY_VALUE']._serialized_start=1796
  _globals['_ACCEPTANCESTRATEGY_VALUE']._serialized_end=1859
  _globals['_ITERATEDLOCALSEARCHPARAMETERS']._serialized_start=1862
  _globals['_ITERATEDLOCALSEARCHPARAMETERS']._serialized_end=2291
# @@protoc_insertion_point(module_scope)
