"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Proto describing a general Constraint Programming (CP) problem."""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _CpSolverStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _CpSolverStatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_CpSolverStatus.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    UNKNOWN: _CpSolverStatus.ValueType  # 0
    """The status of the model is still unknown. A search limit has been reached
    before any of the statuses below could be determined.
    """
    MODEL_INVALID: _CpSolverStatus.ValueType  # 1
    """The given CpModelProto didn't pass the validation step. You can get a
    detailed error by calling ValidateCpModel(model_proto).
    """
    FEASIBLE: _CpSolverStatus.ValueType  # 2
    """A feasible solution has been found. But the search was stopped before we
    could prove optimality or before we enumerated all solutions of a
    feasibility problem (if asked).
    """
    INFEASIBLE: _CpSolverStatus.ValueType  # 3
    """The problem has been proven infeasible."""
    OPTIMAL: _CpSolverStatus.ValueType  # 4
    """An optimal feasible solution has been found.

    More generally, this status represent a success. So we also return OPTIMAL
    if we find a solution for a pure feasibility problem or if a gap limit has
    been specified and we return a solution within this limit. In the case
    where we need to return all the feasible solution, this status will only be
    returned if we enumerated all of them; If we stopped before, we will return
    FEASIBLE.
    """

class CpSolverStatus(_CpSolverStatus, metaclass=_CpSolverStatusEnumTypeWrapper):
    """The status returned by a solver trying to solve a CpModelProto."""

UNKNOWN: CpSolverStatus.ValueType  # 0
"""The status of the model is still unknown. A search limit has been reached
before any of the statuses below could be determined.
"""
MODEL_INVALID: CpSolverStatus.ValueType  # 1
"""The given CpModelProto didn't pass the validation step. You can get a
detailed error by calling ValidateCpModel(model_proto).
"""
FEASIBLE: CpSolverStatus.ValueType  # 2
"""A feasible solution has been found. But the search was stopped before we
could prove optimality or before we enumerated all solutions of a
feasibility problem (if asked).
"""
INFEASIBLE: CpSolverStatus.ValueType  # 3
"""The problem has been proven infeasible."""
OPTIMAL: CpSolverStatus.ValueType  # 4
"""An optimal feasible solution has been found.

More generally, this status represent a success. So we also return OPTIMAL
if we find a solution for a pure feasibility problem or if a gap limit has
been specified and we return a solution within this limit. In the case
where we need to return all the feasible solution, this status will only be
returned if we enumerated all of them; If we stopped before, we will return
FEASIBLE.
"""
global___CpSolverStatus = CpSolverStatus

@typing.final
class IntegerVariableProto(google.protobuf.message.Message):
    """An integer variable.

    It will be referred to by an int32 corresponding to its index in a
    CpModelProto variables field.

    Depending on the context, a reference to a variable whose domain is in [0, 1]
    can also be seen as a Boolean that will be true if the variable value is 1
    and false if it is 0. When used in this context, the field name will always
    contain the word "literal".

    Negative reference (advanced usage): to simplify the creation of a model and
    for efficiency reasons, all the "literal" or "variable" fields can also
    contain a negative index. A negative index i will refer to the negation of
    the integer variable at index -i -1 or to NOT the literal at the same index.

    Ex: A variable index 4 will refer to the integer variable model.variables(4)
    and an index of -5 will refer to the negation of the same variable. A literal
    index 4 will refer to the logical fact that model.variable(4) == 1 and a
    literal index of -5 will refer to the logical fact model.variable(4) == 0.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    DOMAIN_FIELD_NUMBER: builtins.int
    name: builtins.str
    """For debug/logging only. Can be empty."""
    @property
    def domain(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """The variable domain given as a sorted list of n disjoint intervals
        [min, max] and encoded as [min_0, max_0,  ..., min_{n-1}, max_{n-1}].

        The most common example being just [min, max].
        If min == max, then this is a constant variable.

        We have:
         - domain_size() is always even.
         - min == domain.front();
         - max == domain.back();
         - for all i < n   :      min_i <= max_i
         - for all i < n-1 :  max_i + 1 < min_{i+1}.

        Note that we check at validation that a variable domain is small enough so
        that we don't run into integer overflow in our algorithms. Because of that,
        you cannot just have "unbounded" variable like [0, kint64max] and should
        try to specify tighter domains.
        """

    def __init__(
        self,
        *,
        name: builtins.str = ...,
        domain: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["domain", b"domain", "name", b"name"]) -> None: ...

global___IntegerVariableProto = IntegerVariableProto

@typing.final
class BoolArgumentProto(google.protobuf.message.Message):
    """Argument of the constraints of the form OP(literals)."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LITERALS_FIELD_NUMBER: builtins.int
    @property
    def literals(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        literals: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["literals", b"literals"]) -> None: ...

global___BoolArgumentProto = BoolArgumentProto

@typing.final
class LinearExpressionProto(google.protobuf.message.Message):
    """Some constraints supports linear expression instead of just using a reference
    to a variable. This is especially useful during presolve to reduce the model
    size.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARS_FIELD_NUMBER: builtins.int
    COEFFS_FIELD_NUMBER: builtins.int
    OFFSET_FIELD_NUMBER: builtins.int
    offset: builtins.int
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def coeffs(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
        coeffs: collections.abc.Iterable[builtins.int] | None = ...,
        offset: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["coeffs", b"coeffs", "offset", b"offset", "vars", b"vars"]) -> None: ...

global___LinearExpressionProto = LinearExpressionProto

@typing.final
class LinearArgumentProto(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TARGET_FIELD_NUMBER: builtins.int
    EXPRS_FIELD_NUMBER: builtins.int
    @property
    def target(self) -> global___LinearExpressionProto: ...
    @property
    def exprs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]: ...
    def __init__(
        self,
        *,
        target: global___LinearExpressionProto | None = ...,
        exprs: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["target", b"target"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["exprs", b"exprs", "target", b"target"]) -> None: ...

global___LinearArgumentProto = LinearArgumentProto

@typing.final
class AllDifferentConstraintProto(google.protobuf.message.Message):
    """All affine expressions must take different values."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    EXPRS_FIELD_NUMBER: builtins.int
    @property
    def exprs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]: ...
    def __init__(
        self,
        *,
        exprs: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["exprs", b"exprs"]) -> None: ...

global___AllDifferentConstraintProto = AllDifferentConstraintProto

@typing.final
class LinearConstraintProto(google.protobuf.message.Message):
    """The linear sum vars[i] * coeffs[i] must fall in the given domain. The domain
    has the same format as the one in IntegerVariableProto.

    Note that the validation code currently checks using the domain of the
    involved variables that the sum can always be computed without integer
    overflow and throws an error otherwise.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARS_FIELD_NUMBER: builtins.int
    COEFFS_FIELD_NUMBER: builtins.int
    DOMAIN_FIELD_NUMBER: builtins.int
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def coeffs(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Same size as vars."""

    @property
    def domain(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
        coeffs: collections.abc.Iterable[builtins.int] | None = ...,
        domain: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["coeffs", b"coeffs", "domain", b"domain", "vars", b"vars"]) -> None: ...

global___LinearConstraintProto = LinearConstraintProto

@typing.final
class ElementConstraintProto(google.protobuf.message.Message):
    """The constraint target = vars[index].
    This enforces that index takes one of the value in [0, vars_size()).
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INDEX_FIELD_NUMBER: builtins.int
    TARGET_FIELD_NUMBER: builtins.int
    VARS_FIELD_NUMBER: builtins.int
    LINEAR_INDEX_FIELD_NUMBER: builtins.int
    LINEAR_TARGET_FIELD_NUMBER: builtins.int
    EXPRS_FIELD_NUMBER: builtins.int
    index: builtins.int
    """Legacy field."""
    target: builtins.int
    """Legacy field."""
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Legacy field."""

    @property
    def linear_index(self) -> global___LinearExpressionProto:
        """All expressions below must be affine function with at most one variable."""

    @property
    def linear_target(self) -> global___LinearExpressionProto: ...
    @property
    def exprs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]: ...
    def __init__(
        self,
        *,
        index: builtins.int = ...,
        target: builtins.int = ...,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
        linear_index: global___LinearExpressionProto | None = ...,
        linear_target: global___LinearExpressionProto | None = ...,
        exprs: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["linear_index", b"linear_index", "linear_target", b"linear_target"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["exprs", b"exprs", "index", b"index", "linear_index", b"linear_index", "linear_target", b"linear_target", "target", b"target", "vars", b"vars"]) -> None: ...

global___ElementConstraintProto = ElementConstraintProto

@typing.final
class IntervalConstraintProto(google.protobuf.message.Message):
    """This is not really a constraint. It is there so it can be referred by other
    constraints using this "interval" concept.

    IMPORTANT: For now, this constraint do not enforce any relations on the
    components, and it is up to the client to add in the model:
    - enforcement => start + size == end.
    - enforcement => size >= 0  // Only needed if size is not already >= 0.

    IMPORTANT: For now, we just support affine relation. We could easily
    create an intermediate variable to support full linear expression, but this
    isn't done currently.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    START_FIELD_NUMBER: builtins.int
    END_FIELD_NUMBER: builtins.int
    SIZE_FIELD_NUMBER: builtins.int
    @property
    def start(self) -> global___LinearExpressionProto: ...
    @property
    def end(self) -> global___LinearExpressionProto: ...
    @property
    def size(self) -> global___LinearExpressionProto: ...
    def __init__(
        self,
        *,
        start: global___LinearExpressionProto | None = ...,
        end: global___LinearExpressionProto | None = ...,
        size: global___LinearExpressionProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["end", b"end", "size", b"size", "start", b"start"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["end", b"end", "size", b"size", "start", b"start"]) -> None: ...

global___IntervalConstraintProto = IntervalConstraintProto

@typing.final
class NoOverlapConstraintProto(google.protobuf.message.Message):
    """All the intervals (index of IntervalConstraintProto) must be disjoint. More
    formally, there must exist a sequence so that for each consecutive intervals,
    we have end_i <= start_{i+1}. In particular, intervals of size zero do matter
    for this constraint. This is also known as a disjunctive constraint in
    scheduling.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INTERVALS_FIELD_NUMBER: builtins.int
    @property
    def intervals(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        intervals: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["intervals", b"intervals"]) -> None: ...

global___NoOverlapConstraintProto = NoOverlapConstraintProto

@typing.final
class NoOverlap2DConstraintProto(google.protobuf.message.Message):
    """The boxes defined by [start_x, end_x) * [start_y, end_y) cannot overlap.
    Furthermore, one box is optional if at least one of the x or y interval is
    optional.

    Note that the case of boxes of size zero is special. The following cases
    violate the constraint:
      - a point box inside a box with a non zero area
      - a line box overlapping a box with a non zero area
      - one vertical line box crossing an horizontal line box.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    X_INTERVALS_FIELD_NUMBER: builtins.int
    Y_INTERVALS_FIELD_NUMBER: builtins.int
    @property
    def x_intervals(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def y_intervals(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Same size as x_intervals."""

    def __init__(
        self,
        *,
        x_intervals: collections.abc.Iterable[builtins.int] | None = ...,
        y_intervals: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["x_intervals", b"x_intervals", "y_intervals", b"y_intervals"]) -> None: ...

global___NoOverlap2DConstraintProto = NoOverlap2DConstraintProto

@typing.final
class CumulativeConstraintProto(google.protobuf.message.Message):
    """The sum of the demands of the intervals at each interval point cannot exceed
    a capacity. Note that intervals are interpreted as [start, end) and as
    such intervals like [2,3) and [3,4) do not overlap for the point of view of
    this constraint. Moreover, intervals of size zero are ignored.

    All demands must not contain any negative value in their domains. This is
    checked at validation. Even if there are no intervals, this constraint
    implicit enforces capacity >= 0. In other words, a negative capacity is
    considered valid but always infeasible.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CAPACITY_FIELD_NUMBER: builtins.int
    INTERVALS_FIELD_NUMBER: builtins.int
    DEMANDS_FIELD_NUMBER: builtins.int
    @property
    def capacity(self) -> global___LinearExpressionProto: ...
    @property
    def intervals(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def demands(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]:
        """Same size as intervals."""

    def __init__(
        self,
        *,
        capacity: global___LinearExpressionProto | None = ...,
        intervals: collections.abc.Iterable[builtins.int] | None = ...,
        demands: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["capacity", b"capacity"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["capacity", b"capacity", "demands", b"demands", "intervals", b"intervals"]) -> None: ...

global___CumulativeConstraintProto = CumulativeConstraintProto

@typing.final
class ReservoirConstraintProto(google.protobuf.message.Message):
    """Maintain a reservoir level within bounds. The water level starts at 0, and at
    any time, it must be within [min_level, max_level].

    If the variable active_literals[i] is true, and if the expression
    time_exprs[i] is assigned a value t, then the current level changes by
    level_changes[i] at the time t. Therefore, at any time t:

    sum(level_changes[i] * active_literals[i] if time_exprs[i] <= t)
      in [min_level, max_level]

    Note that min level must be <= 0, and the max level must be >= 0. Please use
    fixed level_changes to simulate initial state.

    The array of boolean variables 'actives', if defined, indicates which actions
    are actually performed. If this array is not defined, then it is assumed that
    all actions will be performed.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MIN_LEVEL_FIELD_NUMBER: builtins.int
    MAX_LEVEL_FIELD_NUMBER: builtins.int
    TIME_EXPRS_FIELD_NUMBER: builtins.int
    LEVEL_CHANGES_FIELD_NUMBER: builtins.int
    ACTIVE_LITERALS_FIELD_NUMBER: builtins.int
    min_level: builtins.int
    max_level: builtins.int
    @property
    def time_exprs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]:
        """affine expressions."""

    @property
    def level_changes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]:
        """Currently, we only support constant level changes.
        affine expressions.
        """

    @property
    def active_literals(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        min_level: builtins.int = ...,
        max_level: builtins.int = ...,
        time_exprs: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
        level_changes: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
        active_literals: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["active_literals", b"active_literals", "level_changes", b"level_changes", "max_level", b"max_level", "min_level", b"min_level", "time_exprs", b"time_exprs"]) -> None: ...

global___ReservoirConstraintProto = ReservoirConstraintProto

@typing.final
class CircuitConstraintProto(google.protobuf.message.Message):
    """The circuit constraint is defined on a graph where the arc presence are
    controlled by literals. Each arc is given by an index in the
    tails/heads/literals lists that must have the same size.

    For now, we ignore node indices with no incident arc. All the other nodes
    must have exactly one incoming and one outgoing selected arc (i.e. literal at
    true). All the selected arcs that are not self-loops must form a single
    circuit. Note that multi-arcs are allowed, but only one of them will be true
    at the same time. Multi-self loop are disallowed though.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TAILS_FIELD_NUMBER: builtins.int
    HEADS_FIELD_NUMBER: builtins.int
    LITERALS_FIELD_NUMBER: builtins.int
    @property
    def tails(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def heads(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def literals(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        tails: collections.abc.Iterable[builtins.int] | None = ...,
        heads: collections.abc.Iterable[builtins.int] | None = ...,
        literals: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["heads", b"heads", "literals", b"literals", "tails", b"tails"]) -> None: ...

global___CircuitConstraintProto = CircuitConstraintProto

@typing.final
class RoutesConstraintProto(google.protobuf.message.Message):
    """The "VRP" (Vehicle Routing Problem) constraint.

    The direct graph where arc #i (from tails[i] to head[i]) is present iff
    literals[i] is true must satisfy this set of properties:
    - #incoming arcs == 1 except for node 0.
    - #outgoing arcs == 1 except for node 0.
    - for node zero, #incoming arcs == #outgoing arcs.
    - There are no duplicate arcs.
    - Self-arcs are allowed except for node 0.
    - There is no cycle in this graph, except through node 0.

    Note: Currently this constraint expects all the nodes in [0, num_nodes) to
    have at least one incident arc. The model will be considered invalid if it
    is not the case. You can add self-arc fixed to one to ignore some nodes if
    needed.

    TODO(user): It is probably possible to generalize this constraint to a
    no-cycle in a general graph, or a no-cycle with sum incoming <= 1 and sum
    outgoing <= 1 (more efficient implementation). On the other hand, having this
    specific constraint allow us to add specific "cuts" to a VRP problem.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class NodeExpressions(google.protobuf.message.Message):
        """A set of linear expressions associated with the nodes."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        EXPRS_FIELD_NUMBER: builtins.int
        @property
        def exprs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]:
            """The i-th element is the linear expression associated with the i-th node.
            All expressions must be affine expressions (a * var + b).
            """

        def __init__(
            self,
            *,
            exprs: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["exprs", b"exprs"]) -> None: ...

    TAILS_FIELD_NUMBER: builtins.int
    HEADS_FIELD_NUMBER: builtins.int
    LITERALS_FIELD_NUMBER: builtins.int
    DEMANDS_FIELD_NUMBER: builtins.int
    CAPACITY_FIELD_NUMBER: builtins.int
    DIMENSIONS_FIELD_NUMBER: builtins.int
    capacity: builtins.int
    @property
    def tails(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def heads(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def literals(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def demands(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """DEPRECATED. These fields are no longer used. The solver ignores them."""

    @property
    def dimensions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___RoutesConstraintProto.NodeExpressions]:
        """Expressions associated with the nodes of the graph, such as the load of the
        vehicle arriving at a node, or the time at which a vehicle arrives at a
        node. Expressions with the same "dimension" (such as "load" or "time") must
        be listed together.
        This field is optional. If it is set, the linear constraints of size 1 or 2
        between the variables in these expressions will be used to derive cuts for
        this constraint. If it is not set, the solver will try to automatically
        derive it, from the linear constraints of size 1 or 2 in the model (this
        can fail in complex cases).
        """

    def __init__(
        self,
        *,
        tails: collections.abc.Iterable[builtins.int] | None = ...,
        heads: collections.abc.Iterable[builtins.int] | None = ...,
        literals: collections.abc.Iterable[builtins.int] | None = ...,
        demands: collections.abc.Iterable[builtins.int] | None = ...,
        capacity: builtins.int = ...,
        dimensions: collections.abc.Iterable[global___RoutesConstraintProto.NodeExpressions] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["capacity", b"capacity", "demands", b"demands", "dimensions", b"dimensions", "heads", b"heads", "literals", b"literals", "tails", b"tails"]) -> None: ...

global___RoutesConstraintProto = RoutesConstraintProto

@typing.final
class TableConstraintProto(google.protobuf.message.Message):
    """The values of the n-tuple formed by the given expression can only be one of
    the listed n-tuples in values. The n-tuples are encoded in a flattened way:
        [tuple0_v0, tuple0_v1, ..., tuple0_v{n-1}, tuple1_v0, ...].
    Expressions must be affine (a * var + b).
    Corner cases:
     - If all `vars`, `values` and `exprs` are empty, the constraint is trivially
       true, irrespective of the value of `negated`.
     - If `values` is empty but either vars or exprs is not, the constraint is
       trivially false if `negated` is false, and trivially true if `negated` is
       true.
     - If `vars` and `exprs` are empty but `values` is not, the model is invalid.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARS_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    EXPRS_FIELD_NUMBER: builtins.int
    NEGATED_FIELD_NUMBER: builtins.int
    negated: builtins.bool
    """If true, the meaning is "negated", that is we forbid any of the given
    tuple from a feasible assignment.
    """
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Legacy field."""

    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def exprs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]: ...
    def __init__(
        self,
        *,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
        values: collections.abc.Iterable[builtins.int] | None = ...,
        exprs: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
        negated: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["exprs", b"exprs", "negated", b"negated", "values", b"values", "vars", b"vars"]) -> None: ...

global___TableConstraintProto = TableConstraintProto

@typing.final
class InverseConstraintProto(google.protobuf.message.Message):
    """The two arrays of variable each represent a function, the second is the
    inverse of the first: f_direct[i] == j <=> f_inverse[j] == i.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    F_DIRECT_FIELD_NUMBER: builtins.int
    F_INVERSE_FIELD_NUMBER: builtins.int
    @property
    def f_direct(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def f_inverse(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        f_direct: collections.abc.Iterable[builtins.int] | None = ...,
        f_inverse: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["f_direct", b"f_direct", "f_inverse", b"f_inverse"]) -> None: ...

global___InverseConstraintProto = InverseConstraintProto

@typing.final
class AutomatonConstraintProto(google.protobuf.message.Message):
    """This constraint forces a sequence of expressions to be accepted by an
    automaton.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STARTING_STATE_FIELD_NUMBER: builtins.int
    FINAL_STATES_FIELD_NUMBER: builtins.int
    TRANSITION_TAIL_FIELD_NUMBER: builtins.int
    TRANSITION_HEAD_FIELD_NUMBER: builtins.int
    TRANSITION_LABEL_FIELD_NUMBER: builtins.int
    VARS_FIELD_NUMBER: builtins.int
    EXPRS_FIELD_NUMBER: builtins.int
    starting_state: builtins.int
    """A state is identified by a non-negative number. It is preferable to keep
    all the states dense in says [0, num_states). The automaton starts at
    starting_state and must finish in any of the final states.
    """
    @property
    def final_states(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def transition_tail(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """List of transitions (all 3 vectors have the same size). Both tail and head
        are states, label is any variable value. No two outgoing transitions from
        the same state can have the same label.
        """

    @property
    def transition_head(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def transition_label(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Legacy field."""

    @property
    def exprs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]:
        """The sequence of affine expressions (a * var + b). The automaton is ran for
        exprs_size() "steps" and the value of exprs[i] corresponds to the
        transition label at step i.
        """

    def __init__(
        self,
        *,
        starting_state: builtins.int = ...,
        final_states: collections.abc.Iterable[builtins.int] | None = ...,
        transition_tail: collections.abc.Iterable[builtins.int] | None = ...,
        transition_head: collections.abc.Iterable[builtins.int] | None = ...,
        transition_label: collections.abc.Iterable[builtins.int] | None = ...,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
        exprs: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["exprs", b"exprs", "final_states", b"final_states", "starting_state", b"starting_state", "transition_head", b"transition_head", "transition_label", b"transition_label", "transition_tail", b"transition_tail", "vars", b"vars"]) -> None: ...

global___AutomatonConstraintProto = AutomatonConstraintProto

@typing.final
class ListOfVariablesProto(google.protobuf.message.Message):
    """A list of variables, without any semantics."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARS_FIELD_NUMBER: builtins.int
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["vars", b"vars"]) -> None: ...

global___ListOfVariablesProto = ListOfVariablesProto

@typing.final
class ConstraintProto(google.protobuf.message.Message):
    """Next id: 31"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    ENFORCEMENT_LITERAL_FIELD_NUMBER: builtins.int
    BOOL_OR_FIELD_NUMBER: builtins.int
    BOOL_AND_FIELD_NUMBER: builtins.int
    AT_MOST_ONE_FIELD_NUMBER: builtins.int
    EXACTLY_ONE_FIELD_NUMBER: builtins.int
    BOOL_XOR_FIELD_NUMBER: builtins.int
    INT_DIV_FIELD_NUMBER: builtins.int
    INT_MOD_FIELD_NUMBER: builtins.int
    INT_PROD_FIELD_NUMBER: builtins.int
    LIN_MAX_FIELD_NUMBER: builtins.int
    LINEAR_FIELD_NUMBER: builtins.int
    ALL_DIFF_FIELD_NUMBER: builtins.int
    ELEMENT_FIELD_NUMBER: builtins.int
    CIRCUIT_FIELD_NUMBER: builtins.int
    ROUTES_FIELD_NUMBER: builtins.int
    TABLE_FIELD_NUMBER: builtins.int
    AUTOMATON_FIELD_NUMBER: builtins.int
    INVERSE_FIELD_NUMBER: builtins.int
    RESERVOIR_FIELD_NUMBER: builtins.int
    INTERVAL_FIELD_NUMBER: builtins.int
    NO_OVERLAP_FIELD_NUMBER: builtins.int
    NO_OVERLAP_2D_FIELD_NUMBER: builtins.int
    CUMULATIVE_FIELD_NUMBER: builtins.int
    DUMMY_CONSTRAINT_FIELD_NUMBER: builtins.int
    name: builtins.str
    """For debug/logging only. Can be empty."""
    @property
    def enforcement_literal(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """The constraint will be enforced iff all literals listed here are true. If
        this is empty, then the constraint will always be enforced. An enforced
        constraint must be satisfied, and an un-enforced one will simply be
        ignored.

        This is also called half-reification. To have an equivalence between a
        literal and a constraint (full reification), one must add both a constraint
        (controlled by a literal l) and its negation (controlled by the negation of
        l).

        Important: as of September 2018, only a few constraint support enforcement:
        - bool_or, bool_and, linear: fully supported.
        - interval: only support a single enforcement literal.
        - other: no support (but can be added on a per-demand basis).
        """

    @property
    def bool_or(self) -> global___BoolArgumentProto:
        """The bool_or constraint forces at least one literal to be true."""

    @property
    def bool_and(self) -> global___BoolArgumentProto:
        """The bool_and constraint forces all of the literals to be true.

        This is a "redundant" constraint in the sense that this can easily be
        encoded with many bool_or or at_most_one. It is just more space efficient
        and handled slightly differently internally.
        """

    @property
    def at_most_one(self) -> global___BoolArgumentProto:
        """The at_most_one constraint enforces that no more than one literal is
        true at the same time.

        Note that an at most one constraint of length n could be encoded with n
        bool_and constraint with n-1 term on the right hand side. So in a sense,
        this constraint contribute directly to the "implication-graph" or the
        2-SAT part of the model.

        This constraint does not support enforcement_literal. Just use a linear
        constraint if you need to enforce it. You also do not need to use it
        directly, we will extract it from the model in most situations.
        """

    @property
    def exactly_one(self) -> global___BoolArgumentProto:
        """The exactly_one constraint force exactly one literal to true and no more.

        Anytime a bool_or (it could have been called at_least_one) is included
        into an at_most_one, then the bool_or is actually an exactly one
        constraint, and the extra literal in the at_most_one can be set to false.
        So in this sense, this constraint is not really needed. it is just here
        for a better description of the problem structure and to facilitate some
        algorithm.

        This constraint does not support enforcement_literal. Just use a linear
        constraint if you need to enforce it. You also do not need to use it
        directly, we will extract it from the model in most situations.
        """

    @property
    def bool_xor(self) -> global___BoolArgumentProto:
        """The bool_xor constraint forces an odd number of the literals to be true."""

    @property
    def int_div(self) -> global___LinearArgumentProto:
        """The int_div constraint forces the target to equal exprs[0] / exprs[1].
        The division is "rounded" towards zero, so we can have for instance
        (2 = 12 / 5) or (-3 = -10 / 3). If you only want exact integer division,
        then you should use instead of t = a / b, the int_prod constraint
        a = b * t.

        If 0 belongs to the domain of exprs[1], then the model is deemed invalid.
        """

    @property
    def int_mod(self) -> global___LinearArgumentProto:
        """The int_mod constraint forces the target to equal exprs[0] % exprs[1].
        The domain of exprs[1] must be strictly positive. The sign of the target
        is the same as the sign of exprs[0].
        """

    @property
    def int_prod(self) -> global___LinearArgumentProto:
        """The int_prod constraint forces the target to equal the product of all
        variables. By convention, because we can just remove term equal to one,
        the empty product forces the target to be one.

        Note that the solver checks for potential integer overflow. So the
        product of the maximum absolute value of all the terms (using the initial
        domain) should fit on an int64. Otherwise the model will be declared
        invalid.
        """

    @property
    def lin_max(self) -> global___LinearArgumentProto:
        """The lin_max constraint forces the target to equal the maximum of all
        linear expressions.
        Note that this can model a minimum simply by negating all expressions.
        """

    @property
    def linear(self) -> global___LinearConstraintProto:
        """The linear constraint enforces a linear inequality among the variables,
        such as 0 <= x + 2y <= 10.
        """

    @property
    def all_diff(self) -> global___AllDifferentConstraintProto:
        """The all_diff constraint forces all variables to take different values."""

    @property
    def element(self) -> global___ElementConstraintProto:
        """The element constraint forces the variable with the given index
        to be equal to the target.
        """

    @property
    def circuit(self) -> global___CircuitConstraintProto:
        """The circuit constraint takes a graph and forces the arcs present
        (with arc presence indicated by a literal) to form a unique cycle.
        """

    @property
    def routes(self) -> global___RoutesConstraintProto:
        """The routes constraint implements the vehicle routing problem."""

    @property
    def table(self) -> global___TableConstraintProto:
        """The table constraint enforces what values a tuple of variables may
        take.
        """

    @property
    def automaton(self) -> global___AutomatonConstraintProto:
        """The automaton constraint forces a sequence of variables to be accepted
        by an automaton.
        """

    @property
    def inverse(self) -> global___InverseConstraintProto:
        """The inverse constraint forces two arrays to be inverses of each other:
        the values of one are the indices of the other, and vice versa.
        """

    @property
    def reservoir(self) -> global___ReservoirConstraintProto:
        """The reservoir constraint forces the sum of a set of active demands
        to always be between a specified minimum and maximum value during
        specific times.
        """

    @property
    def interval(self) -> global___IntervalConstraintProto:
        """Constraints on intervals.

        The first constraint defines what an "interval" is and the other
        constraints use references to it. All the intervals that have an
        enforcement_literal set to false are ignored by these constraints.

        TODO(user): Explain what happen for intervals of size zero. Some
        constraints ignore them; others do take them into account.

        The interval constraint takes a start, end, and size, and forces
        start + size == end.
        """

    @property
    def no_overlap(self) -> global___NoOverlapConstraintProto:
        """The no_overlap constraint prevents a set of intervals from
        overlapping; in scheduling, this is called a disjunctive
        constraint.
        """

    @property
    def no_overlap_2d(self) -> global___NoOverlap2DConstraintProto:
        """The no_overlap_2d constraint prevents a set of boxes from overlapping."""

    @property
    def cumulative(self) -> global___CumulativeConstraintProto:
        """The cumulative constraint ensures that for any integer point, the sum
        of the demands of the intervals containing that point does not exceed
        the capacity.
        """

    @property
    def dummy_constraint(self) -> global___ListOfVariablesProto:
        """This constraint is not meant to be used and will be rejected by the
        solver. It is meant to mark variable when testing the presolve code.
        """

    def __init__(
        self,
        *,
        name: builtins.str = ...,
        enforcement_literal: collections.abc.Iterable[builtins.int] | None = ...,
        bool_or: global___BoolArgumentProto | None = ...,
        bool_and: global___BoolArgumentProto | None = ...,
        at_most_one: global___BoolArgumentProto | None = ...,
        exactly_one: global___BoolArgumentProto | None = ...,
        bool_xor: global___BoolArgumentProto | None = ...,
        int_div: global___LinearArgumentProto | None = ...,
        int_mod: global___LinearArgumentProto | None = ...,
        int_prod: global___LinearArgumentProto | None = ...,
        lin_max: global___LinearArgumentProto | None = ...,
        linear: global___LinearConstraintProto | None = ...,
        all_diff: global___AllDifferentConstraintProto | None = ...,
        element: global___ElementConstraintProto | None = ...,
        circuit: global___CircuitConstraintProto | None = ...,
        routes: global___RoutesConstraintProto | None = ...,
        table: global___TableConstraintProto | None = ...,
        automaton: global___AutomatonConstraintProto | None = ...,
        inverse: global___InverseConstraintProto | None = ...,
        reservoir: global___ReservoirConstraintProto | None = ...,
        interval: global___IntervalConstraintProto | None = ...,
        no_overlap: global___NoOverlapConstraintProto | None = ...,
        no_overlap_2d: global___NoOverlap2DConstraintProto | None = ...,
        cumulative: global___CumulativeConstraintProto | None = ...,
        dummy_constraint: global___ListOfVariablesProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["all_diff", b"all_diff", "at_most_one", b"at_most_one", "automaton", b"automaton", "bool_and", b"bool_and", "bool_or", b"bool_or", "bool_xor", b"bool_xor", "circuit", b"circuit", "constraint", b"constraint", "cumulative", b"cumulative", "dummy_constraint", b"dummy_constraint", "element", b"element", "exactly_one", b"exactly_one", "int_div", b"int_div", "int_mod", b"int_mod", "int_prod", b"int_prod", "interval", b"interval", "inverse", b"inverse", "lin_max", b"lin_max", "linear", b"linear", "no_overlap", b"no_overlap", "no_overlap_2d", b"no_overlap_2d", "reservoir", b"reservoir", "routes", b"routes", "table", b"table"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["all_diff", b"all_diff", "at_most_one", b"at_most_one", "automaton", b"automaton", "bool_and", b"bool_and", "bool_or", b"bool_or", "bool_xor", b"bool_xor", "circuit", b"circuit", "constraint", b"constraint", "cumulative", b"cumulative", "dummy_constraint", b"dummy_constraint", "element", b"element", "enforcement_literal", b"enforcement_literal", "exactly_one", b"exactly_one", "int_div", b"int_div", "int_mod", b"int_mod", "int_prod", b"int_prod", "interval", b"interval", "inverse", b"inverse", "lin_max", b"lin_max", "linear", b"linear", "name", b"name", "no_overlap", b"no_overlap", "no_overlap_2d", b"no_overlap_2d", "reservoir", b"reservoir", "routes", b"routes", "table", b"table"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["constraint", b"constraint"]) -> typing.Literal["bool_or", "bool_and", "at_most_one", "exactly_one", "bool_xor", "int_div", "int_mod", "int_prod", "lin_max", "linear", "all_diff", "element", "circuit", "routes", "table", "automaton", "inverse", "reservoir", "interval", "no_overlap", "no_overlap_2d", "cumulative", "dummy_constraint"] | None: ...

global___ConstraintProto = ConstraintProto

@typing.final
class CpObjectiveProto(google.protobuf.message.Message):
    """Optimization objective."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARS_FIELD_NUMBER: builtins.int
    COEFFS_FIELD_NUMBER: builtins.int
    OFFSET_FIELD_NUMBER: builtins.int
    SCALING_FACTOR_FIELD_NUMBER: builtins.int
    DOMAIN_FIELD_NUMBER: builtins.int
    SCALING_WAS_EXACT_FIELD_NUMBER: builtins.int
    INTEGER_BEFORE_OFFSET_FIELD_NUMBER: builtins.int
    INTEGER_AFTER_OFFSET_FIELD_NUMBER: builtins.int
    INTEGER_SCALING_FACTOR_FIELD_NUMBER: builtins.int
    offset: builtins.float
    """The displayed objective is always:
      scaling_factor * (sum(coefficients[i] * objective_vars[i]) + offset).
    This is needed to have a consistent objective after presolve or when
    scaling a double problem to express it with integers.

    Note that if scaling_factor is zero, then it is assumed to be 1, so that by
    default these fields have no effect.
    """
    scaling_factor: builtins.float
    scaling_was_exact: builtins.bool
    """Internal field. Do not set. When we scale a FloatObjectiveProto to a
    integer version, we set this to true if the scaling was exact (i.e. all
    original coeff were integer for instance).

    TODO(user): Put the error bounds we computed instead?
    """
    integer_before_offset: builtins.int
    """Internal fields to recover a bound on the original integer objective from
    the presolved one. Basically, initially the integer objective fit on an
    int64 and is in [Initial_lb, Initial_ub]. During presolve, we might change
    the linear expression to have a new domain [Presolved_lb, Presolved_ub]
    that will also always fit on an int64.

    The two domain will always be linked with an affine transformation between
    the two of the form:
      old = (new + before_offset) * integer_scaling_factor + after_offset.
    Note that we use both offsets to always be able to do the computation while
    staying in the int64 domain. In particular, the after_offset will always
    be in (-integer_scaling_factor, integer_scaling_factor).
    """
    integer_after_offset: builtins.int
    integer_scaling_factor: builtins.int
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """The linear terms of the objective to minimize.
        For a maximization problem, one can negate all coefficients in the
        objective and set scaling_factor to -1.
        """

    @property
    def coeffs(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def domain(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """If non-empty, only look for an objective value in the given domain.
        Note that this does not depend on the offset or scaling factor, it is a
        domain on the sum of the objective terms only.
        """

    def __init__(
        self,
        *,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
        coeffs: collections.abc.Iterable[builtins.int] | None = ...,
        offset: builtins.float = ...,
        scaling_factor: builtins.float = ...,
        domain: collections.abc.Iterable[builtins.int] | None = ...,
        scaling_was_exact: builtins.bool = ...,
        integer_before_offset: builtins.int = ...,
        integer_after_offset: builtins.int = ...,
        integer_scaling_factor: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["coeffs", b"coeffs", "domain", b"domain", "integer_after_offset", b"integer_after_offset", "integer_before_offset", b"integer_before_offset", "integer_scaling_factor", b"integer_scaling_factor", "offset", b"offset", "scaling_factor", b"scaling_factor", "scaling_was_exact", b"scaling_was_exact", "vars", b"vars"]) -> None: ...

global___CpObjectiveProto = CpObjectiveProto

@typing.final
class FloatObjectiveProto(google.protobuf.message.Message):
    """A linear floating point objective: sum coeffs[i] * vars[i] + offset.
    Note that the variable can only still take integer value.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARS_FIELD_NUMBER: builtins.int
    COEFFS_FIELD_NUMBER: builtins.int
    OFFSET_FIELD_NUMBER: builtins.int
    MAXIMIZE_FIELD_NUMBER: builtins.int
    offset: builtins.float
    maximize: builtins.bool
    """The optimization direction. The default is to minimize"""
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def coeffs(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]: ...
    def __init__(
        self,
        *,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
        coeffs: collections.abc.Iterable[builtins.float] | None = ...,
        offset: builtins.float = ...,
        maximize: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["coeffs", b"coeffs", "maximize", b"maximize", "offset", b"offset", "vars", b"vars"]) -> None: ...

global___FloatObjectiveProto = FloatObjectiveProto

@typing.final
class DecisionStrategyProto(google.protobuf.message.Message):
    """Define the strategy to follow when the solver needs to take a new decision.
    Note that this strategy is only defined on a subset of variables.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _VariableSelectionStrategy:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _VariableSelectionStrategyEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[DecisionStrategyProto._VariableSelectionStrategy.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        CHOOSE_FIRST: DecisionStrategyProto._VariableSelectionStrategy.ValueType  # 0
        CHOOSE_LOWEST_MIN: DecisionStrategyProto._VariableSelectionStrategy.ValueType  # 1
        CHOOSE_HIGHEST_MAX: DecisionStrategyProto._VariableSelectionStrategy.ValueType  # 2
        CHOOSE_MIN_DOMAIN_SIZE: DecisionStrategyProto._VariableSelectionStrategy.ValueType  # 3
        CHOOSE_MAX_DOMAIN_SIZE: DecisionStrategyProto._VariableSelectionStrategy.ValueType  # 4

    class VariableSelectionStrategy(_VariableSelectionStrategy, metaclass=_VariableSelectionStrategyEnumTypeWrapper):
        """The order in which the variables (resp. affine expression) above should be
        considered. Note that only variables that are not already fixed are
        considered.

        TODO(user): extend as needed.
        """

    CHOOSE_FIRST: DecisionStrategyProto.VariableSelectionStrategy.ValueType  # 0
    CHOOSE_LOWEST_MIN: DecisionStrategyProto.VariableSelectionStrategy.ValueType  # 1
    CHOOSE_HIGHEST_MAX: DecisionStrategyProto.VariableSelectionStrategy.ValueType  # 2
    CHOOSE_MIN_DOMAIN_SIZE: DecisionStrategyProto.VariableSelectionStrategy.ValueType  # 3
    CHOOSE_MAX_DOMAIN_SIZE: DecisionStrategyProto.VariableSelectionStrategy.ValueType  # 4

    class _DomainReductionStrategy:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _DomainReductionStrategyEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[DecisionStrategyProto._DomainReductionStrategy.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        SELECT_MIN_VALUE: DecisionStrategyProto._DomainReductionStrategy.ValueType  # 0
        SELECT_MAX_VALUE: DecisionStrategyProto._DomainReductionStrategy.ValueType  # 1
        SELECT_LOWER_HALF: DecisionStrategyProto._DomainReductionStrategy.ValueType  # 2
        SELECT_UPPER_HALF: DecisionStrategyProto._DomainReductionStrategy.ValueType  # 3
        SELECT_MEDIAN_VALUE: DecisionStrategyProto._DomainReductionStrategy.ValueType  # 4
        SELECT_RANDOM_HALF: DecisionStrategyProto._DomainReductionStrategy.ValueType  # 5

    class DomainReductionStrategy(_DomainReductionStrategy, metaclass=_DomainReductionStrategyEnumTypeWrapper):
        """Once a variable (resp. affine expression) has been chosen, this enum
        describe what decision is taken on its domain.

        TODO(user): extend as needed.
        """

    SELECT_MIN_VALUE: DecisionStrategyProto.DomainReductionStrategy.ValueType  # 0
    SELECT_MAX_VALUE: DecisionStrategyProto.DomainReductionStrategy.ValueType  # 1
    SELECT_LOWER_HALF: DecisionStrategyProto.DomainReductionStrategy.ValueType  # 2
    SELECT_UPPER_HALF: DecisionStrategyProto.DomainReductionStrategy.ValueType  # 3
    SELECT_MEDIAN_VALUE: DecisionStrategyProto.DomainReductionStrategy.ValueType  # 4
    SELECT_RANDOM_HALF: DecisionStrategyProto.DomainReductionStrategy.ValueType  # 5

    VARIABLES_FIELD_NUMBER: builtins.int
    EXPRS_FIELD_NUMBER: builtins.int
    VARIABLE_SELECTION_STRATEGY_FIELD_NUMBER: builtins.int
    DOMAIN_REDUCTION_STRATEGY_FIELD_NUMBER: builtins.int
    variable_selection_strategy: global___DecisionStrategyProto.VariableSelectionStrategy.ValueType
    domain_reduction_strategy: global___DecisionStrategyProto.DomainReductionStrategy.ValueType
    @property
    def variables(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """The variables to be considered for the next decision. The order matter and
        is always used as a tie-breaker after the variable selection strategy
        criteria defined below.
        """

    @property
    def exprs(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LinearExpressionProto]:
        """If this is set, then the variables field must be empty.
        We currently only support affine expression.

        Note that this is needed so that if a variable has an affine
        representative, we can properly transform a DecisionStrategyProto through
        presolve.
        """

    def __init__(
        self,
        *,
        variables: collections.abc.Iterable[builtins.int] | None = ...,
        exprs: collections.abc.Iterable[global___LinearExpressionProto] | None = ...,
        variable_selection_strategy: global___DecisionStrategyProto.VariableSelectionStrategy.ValueType = ...,
        domain_reduction_strategy: global___DecisionStrategyProto.DomainReductionStrategy.ValueType = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["domain_reduction_strategy", b"domain_reduction_strategy", "exprs", b"exprs", "variable_selection_strategy", b"variable_selection_strategy", "variables", b"variables"]) -> None: ...

global___DecisionStrategyProto = DecisionStrategyProto

@typing.final
class PartialVariableAssignment(google.protobuf.message.Message):
    """This message encodes a partial (or full) assignment of the variables of a
    CpModelProto. The variable indices should be unique and valid variable
    indices.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARS_FIELD_NUMBER: builtins.int
    VALUES_FIELD_NUMBER: builtins.int
    @property
    def vars(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        vars: collections.abc.Iterable[builtins.int] | None = ...,
        values: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["values", b"values", "vars", b"vars"]) -> None: ...

global___PartialVariableAssignment = PartialVariableAssignment

@typing.final
class SparsePermutationProto(google.protobuf.message.Message):
    """A permutation of integers encoded as a list of cycles, hence the "sparse"
    format. The image of an element cycle[i] is cycle[(i + 1) % cycle_length].
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SUPPORT_FIELD_NUMBER: builtins.int
    CYCLE_SIZES_FIELD_NUMBER: builtins.int
    @property
    def support(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Each cycle is listed one after the other in the support field.
        The size of each cycle is given (in order) in the cycle_sizes field.
        """

    @property
    def cycle_sizes(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        support: collections.abc.Iterable[builtins.int] | None = ...,
        cycle_sizes: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["cycle_sizes", b"cycle_sizes", "support", b"support"]) -> None: ...

global___SparsePermutationProto = SparsePermutationProto

@typing.final
class DenseMatrixProto(google.protobuf.message.Message):
    """A dense matrix of numbers encoded in a flat way, row by row.
    That is matrix[i][j] = entries[i * num_cols + j];
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NUM_ROWS_FIELD_NUMBER: builtins.int
    NUM_COLS_FIELD_NUMBER: builtins.int
    ENTRIES_FIELD_NUMBER: builtins.int
    num_rows: builtins.int
    num_cols: builtins.int
    @property
    def entries(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        num_rows: builtins.int = ...,
        num_cols: builtins.int = ...,
        entries: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["entries", b"entries", "num_cols", b"num_cols", "num_rows", b"num_rows"]) -> None: ...

global___DenseMatrixProto = DenseMatrixProto

@typing.final
class SymmetryProto(google.protobuf.message.Message):
    """EXPERIMENTAL. For now, this is meant to be used by the solver and not filled
    by clients.

    Hold symmetry information about the set of feasible solutions. If we permute
    the variable values of any feasible solution using one of the permutation
    described here, we should always get another feasible solution.

    We usually also enforce that the objective of the new solution is the same.

    The group of permutations encoded here is usually computed from the encoding
    of the model, so it is not meant to be a complete representation of the
    feasible solution symmetries, just a valid subgroup.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PERMUTATIONS_FIELD_NUMBER: builtins.int
    ORBITOPES_FIELD_NUMBER: builtins.int
    @property
    def permutations(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SparsePermutationProto]:
        """A list of variable indices permutations that leave the feasible space of
        solution invariant. Usually, we only encode a set of generators of the
        group.
        """

    @property
    def orbitopes(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DenseMatrixProto]:
        """An orbitope is a special symmetry structure of the solution space. If the
        variable indices are arranged in a matrix (with no duplicates), then any
        permutation of the columns will be a valid permutation of the feasible
        space.

        This arise quite often. The typical example is a graph coloring problem
        where for each node i, you have j booleans to indicate its color. If the
        variables color_of_i_is_j are arranged in a matrix[i][j], then any columns
        permutations leave the problem invariant.
        """

    def __init__(
        self,
        *,
        permutations: collections.abc.Iterable[global___SparsePermutationProto] | None = ...,
        orbitopes: collections.abc.Iterable[global___DenseMatrixProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["orbitopes", b"orbitopes", "permutations", b"permutations"]) -> None: ...

global___SymmetryProto = SymmetryProto

@typing.final
class CpModelProto(google.protobuf.message.Message):
    """A constraint programming problem."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    VARIABLES_FIELD_NUMBER: builtins.int
    CONSTRAINTS_FIELD_NUMBER: builtins.int
    OBJECTIVE_FIELD_NUMBER: builtins.int
    FLOATING_POINT_OBJECTIVE_FIELD_NUMBER: builtins.int
    SEARCH_STRATEGY_FIELD_NUMBER: builtins.int
    SOLUTION_HINT_FIELD_NUMBER: builtins.int
    ASSUMPTIONS_FIELD_NUMBER: builtins.int
    SYMMETRY_FIELD_NUMBER: builtins.int
    name: builtins.str
    """For debug/logging only. Can be empty."""
    @property
    def variables(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___IntegerVariableProto]:
        """The associated Protos should be referred by their index in these fields."""

    @property
    def constraints(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ConstraintProto]: ...
    @property
    def objective(self) -> global___CpObjectiveProto:
        """The objective to minimize. Can be empty for pure decision problems."""

    @property
    def floating_point_objective(self) -> global___FloatObjectiveProto:
        """Advanced usage.
        It is invalid to have both an objective and a floating point objective.

        The objective of the model, in floating point format. The solver will
        automatically scale this to integer during expansion and thus convert it to
        a normal CpObjectiveProto. See the mip* parameters to control how this is
        scaled. In most situation the precision will be good enough, but you can
        see the logs to see what are the precision guaranteed when this is
        converted to a fixed point representation.

        Note that even if the precision is bad, the returned objective_value and
        best_objective_bound will be computed correctly. So at the end of the solve
        you can check the gap if you only want precise optimal.
        """

    @property
    def search_strategy(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___DecisionStrategyProto]:
        """Defines the strategy that the solver should follow when the
        search_branching parameter is set to FIXED_SEARCH. Note that this strategy
        is also used as a heuristic when we are not in fixed search.

        Advanced Usage: if not all variables appears and the parameter
        "instantiate_all_variables" is set to false, then the solver will not try
        to instantiate the variables that do not appear. Thus, at the end of the
        search, not all variables may be fixed. Currently, we will set them to
        their lower bound in the solution.
        """

    @property
    def solution_hint(self) -> global___PartialVariableAssignment:
        """Solution hint.

        If a feasible or almost-feasible solution to the problem is already known,
        it may be helpful to pass it to the solver so that it can be used. The
        solver will try to use this information to create its initial feasible
        solution.

        Note that it may not always be faster to give a hint like this to the
        solver. There is also no guarantee that the solver will use this hint or
        try to return a solution "close" to this assignment in case of multiple
        optimal solutions.
        """

    @property
    def assumptions(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """A list of literals. The model will be solved assuming all these literals
        are true. Compared to just fixing the domain of these literals, using this
        mechanism is slower but allows in case the model is INFEASIBLE to get a
        potentially small subset of them that can be used to explain the
        infeasibility.

        Think (IIS), except when you are only concerned by the provided
        assumptions. This is powerful as it allows to group a set of logically
        related constraint under only one enforcement literal which can potentially
        give you a good and interpretable explanation for infeasiblity.

        Such infeasibility explanation will be available in the
        sufficient_assumptions_for_infeasibility response field.
        """

    @property
    def symmetry(self) -> global___SymmetryProto:
        """For now, this is not meant to be filled by a client writing a model, but
        by our preprocessing step.

        Information about the symmetries of the feasible solution space.
        These usually leaves the objective invariant.
        """

    def __init__(
        self,
        *,
        name: builtins.str = ...,
        variables: collections.abc.Iterable[global___IntegerVariableProto] | None = ...,
        constraints: collections.abc.Iterable[global___ConstraintProto] | None = ...,
        objective: global___CpObjectiveProto | None = ...,
        floating_point_objective: global___FloatObjectiveProto | None = ...,
        search_strategy: collections.abc.Iterable[global___DecisionStrategyProto] | None = ...,
        solution_hint: global___PartialVariableAssignment | None = ...,
        assumptions: collections.abc.Iterable[builtins.int] | None = ...,
        symmetry: global___SymmetryProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["floating_point_objective", b"floating_point_objective", "objective", b"objective", "solution_hint", b"solution_hint", "symmetry", b"symmetry"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["assumptions", b"assumptions", "constraints", b"constraints", "floating_point_objective", b"floating_point_objective", "name", b"name", "objective", b"objective", "search_strategy", b"search_strategy", "solution_hint", b"solution_hint", "symmetry", b"symmetry", "variables", b"variables"]) -> None: ...

global___CpModelProto = CpModelProto

@typing.final
class CpSolverSolution(google.protobuf.message.Message):
    """Just a message used to store dense solution.
    This is used by the additional_solutions field.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUES_FIELD_NUMBER: builtins.int
    @property
    def values(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        values: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["values", b"values"]) -> None: ...

global___CpSolverSolution = CpSolverSolution

@typing.final
class CpSolverResponse(google.protobuf.message.Message):
    """The response returned by a solver trying to solve a CpModelProto.

    Next id: 32
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STATUS_FIELD_NUMBER: builtins.int
    SOLUTION_FIELD_NUMBER: builtins.int
    OBJECTIVE_VALUE_FIELD_NUMBER: builtins.int
    BEST_OBJECTIVE_BOUND_FIELD_NUMBER: builtins.int
    ADDITIONAL_SOLUTIONS_FIELD_NUMBER: builtins.int
    TIGHTENED_VARIABLES_FIELD_NUMBER: builtins.int
    SUFFICIENT_ASSUMPTIONS_FOR_INFEASIBILITY_FIELD_NUMBER: builtins.int
    INTEGER_OBJECTIVE_FIELD_NUMBER: builtins.int
    INNER_OBJECTIVE_LOWER_BOUND_FIELD_NUMBER: builtins.int
    NUM_INTEGERS_FIELD_NUMBER: builtins.int
    NUM_BOOLEANS_FIELD_NUMBER: builtins.int
    NUM_FIXED_BOOLEANS_FIELD_NUMBER: builtins.int
    NUM_CONFLICTS_FIELD_NUMBER: builtins.int
    NUM_BRANCHES_FIELD_NUMBER: builtins.int
    NUM_BINARY_PROPAGATIONS_FIELD_NUMBER: builtins.int
    NUM_INTEGER_PROPAGATIONS_FIELD_NUMBER: builtins.int
    NUM_RESTARTS_FIELD_NUMBER: builtins.int
    NUM_LP_ITERATIONS_FIELD_NUMBER: builtins.int
    WALL_TIME_FIELD_NUMBER: builtins.int
    USER_TIME_FIELD_NUMBER: builtins.int
    DETERMINISTIC_TIME_FIELD_NUMBER: builtins.int
    GAP_INTEGRAL_FIELD_NUMBER: builtins.int
    SOLUTION_INFO_FIELD_NUMBER: builtins.int
    SOLVE_LOG_FIELD_NUMBER: builtins.int
    status: global___CpSolverStatus.ValueType
    """The status of the solve."""
    objective_value: builtins.float
    """Only make sense for an optimization problem. The objective value of the
    returned solution if it is non-empty. If there is no solution, then for a
    minimization problem, this will be an upper-bound of the objective of any
    feasible solution, and a lower-bound for a maximization problem.
    """
    best_objective_bound: builtins.float
    """Only make sense for an optimization problem. A proven lower-bound on the
    objective for a minimization problem, or a proven upper-bound for a
    maximization problem.
    """
    inner_objective_lower_bound: builtins.int
    """Advanced usage.

    A lower bound on the integer expression of the objective. This is either a
    bound on the expression in the returned integer_objective or on the integer
    expression of the original objective if the problem already has an integer
    objective.

    TODO(user): This should be renamed integer_objective_lower_bound.
    """
    num_integers: builtins.int
    """Some statistics about the solve.

    Important: in multithread, this correspond the statistics of the first
    subsolver. Which is usually the one with the user defined parameters. Or
    the default-search if none are specified.
    """
    num_booleans: builtins.int
    num_fixed_booleans: builtins.int
    num_conflicts: builtins.int
    num_branches: builtins.int
    num_binary_propagations: builtins.int
    num_integer_propagations: builtins.int
    num_restarts: builtins.int
    num_lp_iterations: builtins.int
    wall_time: builtins.float
    """The time counted from the beginning of the Solve() call."""
    user_time: builtins.float
    deterministic_time: builtins.float
    gap_integral: builtins.float
    """The integral of log(1 + absolute_objective_gap) over time."""
    solution_info: builtins.str
    """Additional information about how the solution was found. It also stores
    model or parameters errors that caused the model to be invalid.
    """
    solve_log: builtins.str
    """The solve log will be filled if the parameter log_to_response is set to
    true.
    """
    @property
    def solution(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """A feasible solution to the given problem. Depending on the returned status
        it may be optimal or just feasible. This is in one-to-one correspondence
        with a CpModelProto::variables repeated field and list the values of all
        the variables.
        """

    @property
    def additional_solutions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CpSolverSolution]:
        """If the parameter fill_additional_solutions_in_response is set, then we
        copy all the solutions from our internal solution pool here.

        Note that the one returned in the solution field will likely appear here
        too. Do not rely on the solutions order as it depends on our internal
        representation (after postsolve).
        """

    @property
    def tightened_variables(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___IntegerVariableProto]:
        """Advanced usage.

        If the option fill_tightened_domains_in_response is set, then this field
        will be a copy of the CpModelProto.variables where each domain has been
        reduced using the information the solver was able to derive. Note that this
        is only filled with the info derived during a normal search and we do not
        have any dedicated algorithm to improve it.

        Warning: if you didn't set keep_all_feasible_solutions_in_presolve, then
        these domains might exclude valid feasible solution. Otherwise for a
        feasibility problem, all feasible solution should be there.

        Warning: For an optimization problem, these will correspond to valid bounds
        for the problem of finding an improving solution to the best one found so
        far. It might be better to solve a feasibility version if one just want to
        explore the feasible region.
        """

    @property
    def sufficient_assumptions_for_infeasibility(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """A subset of the model "assumptions" field. This will only be filled if the
        status is INFEASIBLE. This subset of assumption will be enough to still get
        an infeasible problem.

        This is related to what is called the irreducible inconsistent subsystem or
        IIS. Except one is only concerned by the provided assumptions. There is
        also no guarantee that we return an irreducible (aka minimal subset).
        However, this is based on SAT explanation and there is a good chance it is
        not too large.

        If you really want a minimal subset, a possible way to get one is by
        changing your model to minimize the number of assumptions at false, but
        this is likely an harder problem to solve.

        Important: Currently, this is minimized only in single-thread and if the
        problem is not an optimization problem, otherwise, it will always include
        all the assumptions.

        TODO(user): Allows for returning multiple core at once.
        """

    @property
    def integer_objective(self) -> global___CpObjectiveProto:
        """Contains the integer objective optimized internally. This is only filled if
        the problem had a floating point objective, and on the final response, not
        the ones given to callbacks.
        """

    def __init__(
        self,
        *,
        status: global___CpSolverStatus.ValueType = ...,
        solution: collections.abc.Iterable[builtins.int] | None = ...,
        objective_value: builtins.float = ...,
        best_objective_bound: builtins.float = ...,
        additional_solutions: collections.abc.Iterable[global___CpSolverSolution] | None = ...,
        tightened_variables: collections.abc.Iterable[global___IntegerVariableProto] | None = ...,
        sufficient_assumptions_for_infeasibility: collections.abc.Iterable[builtins.int] | None = ...,
        integer_objective: global___CpObjectiveProto | None = ...,
        inner_objective_lower_bound: builtins.int = ...,
        num_integers: builtins.int = ...,
        num_booleans: builtins.int = ...,
        num_fixed_booleans: builtins.int = ...,
        num_conflicts: builtins.int = ...,
        num_branches: builtins.int = ...,
        num_binary_propagations: builtins.int = ...,
        num_integer_propagations: builtins.int = ...,
        num_restarts: builtins.int = ...,
        num_lp_iterations: builtins.int = ...,
        wall_time: builtins.float = ...,
        user_time: builtins.float = ...,
        deterministic_time: builtins.float = ...,
        gap_integral: builtins.float = ...,
        solution_info: builtins.str = ...,
        solve_log: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["integer_objective", b"integer_objective"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["additional_solutions", b"additional_solutions", "best_objective_bound", b"best_objective_bound", "deterministic_time", b"deterministic_time", "gap_integral", b"gap_integral", "inner_objective_lower_bound", b"inner_objective_lower_bound", "integer_objective", b"integer_objective", "num_binary_propagations", b"num_binary_propagations", "num_booleans", b"num_booleans", "num_branches", b"num_branches", "num_conflicts", b"num_conflicts", "num_fixed_booleans", b"num_fixed_booleans", "num_integer_propagations", b"num_integer_propagations", "num_integers", b"num_integers", "num_lp_iterations", b"num_lp_iterations", "num_restarts", b"num_restarts", "objective_value", b"objective_value", "solution", b"solution", "solution_info", b"solution_info", "solve_log", b"solve_log", "status", b"status", "sufficient_assumptions_for_infeasibility", b"sufficient_assumptions_for_infeasibility", "tightened_variables", b"tightened_variables", "user_time", b"user_time", "wall_time", b"wall_time"]) -> None: ...

global___CpSolverResponse = CpSolverResponse
