"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
The suggested resources to solve an optimization problem."""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class SolverResourcesProto(google.protobuf.message.Message):
    """This message is used to specify some hints on the resources a remote solve is
    expected to use. These parameters are hints and may be ignored by the remote
    server (in particular in case of solve in a local subprocess, for example).

    When using SolveService.Solve and SolveService.ComputeInfeasibleSubsystem,
    these hints are mostly optional as some defaults will be computed based on
    the other parameters.

    When using SolveService.StreamSolve these hints are used to dimension the
    resources available during the execution of every action; thus it is
    recommended to set them.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CPU_FIELD_NUMBER: builtins.int
    RAM_FIELD_NUMBER: builtins.int
    cpu: builtins.float
    """The number of CPUs this solve should be allocated (time-averaged over a
    short implementation-defined window). Must be finite and >=1.0.
    Note that if the SolveParametersProto.threads is not set then this
    parameter should also be left unset.
    """
    ram: builtins.float
    """The limit of RAM for the solve in bytes. Must be finite and >=512000."""
    def __init__(
        self,
        *,
        cpu: builtins.float | None = ...,
        ram: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_cpu", b"_cpu", "_ram", b"_ram", "cpu", b"cpu", "ram", b"ram"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_cpu", b"_cpu", "_ram", b"_ram", "cpu", b"cpu", "ram", b"ram"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_cpu", b"_cpu"]) -> typing.Literal["cpu"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_ram", b"_ram"]) -> typing.Literal["ram"] | None: ...

global___SolverResourcesProto = SolverResourcesProto
