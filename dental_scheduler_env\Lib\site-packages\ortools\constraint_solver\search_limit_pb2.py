# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/constraint_solver/search_limit.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/constraint_solver/search_limit.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,ortools/constraint_solver/search_limit.proto\x12\x13operations_research\"\x8b\x01\n\x16RegularLimitParameters\x12\x0c\n\x04time\x18\x01 \x01(\x03\x12\x10\n\x08\x62ranches\x18\x02 \x01(\x03\x12\x10\n\x08\x66\x61ilures\x18\x03 \x01(\x03\x12\x11\n\tsolutions\x18\x04 \x01(\x03\x12\x18\n\x10smart_time_check\x18\x05 \x01(\x08\x12\x12\n\ncumulative\x18\x06 \x01(\x08\x42^\n#com.google.ortools.constraintsolverB\x13SearchLimitProtobufP\x01\xaa\x02\x1fGoogle.OrTools.ConstraintSolverb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.constraint_solver.search_limit_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.google.ortools.constraintsolverB\023SearchLimitProtobufP\001\252\002\037Google.OrTools.ConstraintSolver'
  _globals['_REGULARLIMITPARAMETERS']._serialized_start=70
  _globals['_REGULARLIMITPARAMETERS']._serialized_end=209
# @@protoc_insertion_point(module_scope)
