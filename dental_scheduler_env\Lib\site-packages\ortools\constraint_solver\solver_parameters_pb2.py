# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/constraint_solver/solver_parameters.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/constraint_solver/solver_parameters.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1ortools/constraint_solver/solver_parameters.proto\x12\x13operations_research\"\xcd\x07\n\x1a\x43onstraintSolverParameters\x12X\n\x0e\x63ompress_trail\x18\x01 \x01(\x0e\x32@.operations_research.ConstraintSolverParameters.TrailCompression\x12\x18\n\x10trail_block_size\x18\x02 \x01(\x05\x12\x18\n\x10\x61rray_split_size\x18\x03 \x01(\x05\x12\x13\n\x0bstore_names\x18\x04 \x01(\x08\x12\x1b\n\x13name_cast_variables\x18\x05 \x01(\x08\x12\x1a\n\x12name_all_variables\x18\x06 \x01(\x08\x12\x1b\n\x13profile_propagation\x18\x07 \x01(\x08\x12\x14\n\x0cprofile_file\x18\x08 \x01(\t\x12\x1c\n\x14profile_local_search\x18\x10 \x01(\x08\x12\"\n\x1aprint_local_search_profile\x18\x11 \x01(\x08\x12\x19\n\x11trace_propagation\x18\t \x01(\x08\x12\x14\n\x0ctrace_search\x18\n \x01(\x08\x12\x13\n\x0bprint_model\x18\x0b \x01(\x08\x12\x19\n\x11print_model_stats\x18\x0c \x01(\x08\x12\x1f\n\x17print_added_constraints\x18\r \x01(\x08\x12\x15\n\rdisable_solve\x18\x0f \x01(\x08\x12\x17\n\x0fuse_small_table\x18\x65 \x01(\x08\x12\"\n\x1ause_cumulative_edge_finder\x18i \x01(\x08\x12!\n\x19use_cumulative_time_table\x18j \x01(\x08\x12&\n\x1euse_cumulative_time_table_sync\x18p \x01(\x08\x12&\n\x1euse_sequence_high_demand_tasks\x18k \x01(\x08\x12%\n\x1duse_all_possible_disjunctions\x18l \x01(\x08\x12\x1c\n\x14max_edge_finder_size\x18m \x01(\x05\x12\x1c\n\x14\x64iffn_use_cumulative\x18n \x01(\x08\x12\x17\n\x0fuse_element_rmq\x18o \x01(\x08\x12\"\n\x1askip_locally_optimal_paths\x18q \x01(\x08\x12\x1d\n\x15\x63heck_solution_period\x18r \x01(\x05\">\n\x10TrailCompression\x12\x12\n\x0eNO_COMPRESSION\x10\x00\x12\x16\n\x12\x43OMPRESS_WITH_ZLIB\x10\x01J\x04\x08\x64\x10\x65J\x04\x08\x66\x10gJ\x04\x08g\x10hJ\x04\x08h\x10iBI\n#com.google.ortools.constraintsolverP\x01\xaa\x02\x1fGoogle.OrTools.ConstraintSolverb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.constraint_solver.solver_parameters_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.google.ortools.constraintsolverP\001\252\002\037Google.OrTools.ConstraintSolver'
  _globals['_CONSTRAINTSOLVERPARAMETERS']._serialized_start=75
  _globals['_CONSTRAINTSOLVERPARAMETERS']._serialized_end=1048
  _globals['_CONSTRAINTSOLVERPARAMETERS_TRAILCOMPRESSION']._serialized_start=962
  _globals['_CONSTRAINTSOLVERPARAMETERS_TRAILCOMPRESSION']._serialized_end=1024
# @@protoc_insertion_point(module_scope)
