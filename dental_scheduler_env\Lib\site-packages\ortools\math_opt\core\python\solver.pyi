class SolveInterrupter:
    def __init__(self) -> None: ...
    def interrupt(self) -> None: ...
    def is_interrupted(self) -> bool: ...

class Solver:
    def __init__(self, *args, **kwargs) -> None: ...
    def solve(self, *args, **kwargs): ...
    def update(self, arg0) -> bool: ...

def compute_infeasible_subsystem(*args, **kwargs): ...
def debug_num_solver() -> int: ...
def new(*args, **kwargs): ...
def solve(*args, **kwargs): ...
