# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/solution.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/solution.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.math_opt import sparse_containers_pb2 as ortools_dot_math__opt_dot_sparse__containers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fortools/math_opt/solution.proto\x12\x1coperations_research.math_opt\x1a(ortools/math_opt/sparse_containers.proto\"\x83\x03\n\x13PrimalSolutionProto\x12N\n\x0fvariable_values\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12\x17\n\x0fobjective_value\x18\x02 \x01(\x01\x12s\n\x1a\x61uxiliary_objective_values\x18\x04 \x03(\x0b\x32O.operations_research.math_opt.PrimalSolutionProto.AuxiliaryObjectiveValuesEntry\x12M\n\x12\x66\x65\x61sibility_status\x18\x03 \x01(\x0e\x32\x31.operations_research.math_opt.SolutionStatusProto\x1a?\n\x1d\x41uxiliaryObjectiveValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"`\n\x0ePrimalRayProto\x12N\n\x0fvariable_values\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\"\x84\x03\n\x11\x44ualSolutionProto\x12J\n\x0b\x64ual_values\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12T\n\x15quadratic_dual_values\x18\x05 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12L\n\rreduced_costs\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12\x1c\n\x0fobjective_value\x18\x03 \x01(\x01H\x00\x88\x01\x01\x12M\n\x12\x66\x65\x61sibility_status\x18\x04 \x01(\x0e\x32\x31.operations_research.math_opt.SolutionStatusProtoB\x12\n\x10_objective_value\"\xa8\x01\n\x0c\x44ualRayProto\x12J\n\x0b\x64ual_values\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12L\n\rreduced_costs\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\"f\n\x17SparseBasisStatusVector\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12>\n\x06values\x18\x02 \x03(\x0e\x32..operations_research.math_opt.BasisStatusProto\"\x81\x02\n\nBasisProto\x12P\n\x11\x63onstraint_status\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseBasisStatusVector\x12N\n\x0fvariable_status\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseBasisStatusVector\x12Q\n\x16\x62\x61sic_dual_feasibility\x18\x03 \x01(\x0e\x32\x31.operations_research.math_opt.SolutionStatusProto\"\x9b\x02\n\rSolutionProto\x12O\n\x0fprimal_solution\x18\x01 \x01(\x0b\x32\x31.operations_research.math_opt.PrimalSolutionProtoH\x00\x88\x01\x01\x12K\n\rdual_solution\x18\x02 \x01(\x0b\x32/.operations_research.math_opt.DualSolutionProtoH\x01\x88\x01\x01\x12<\n\x05\x62\x61sis\x18\x03 \x01(\x0b\x32(.operations_research.math_opt.BasisProtoH\x02\x88\x01\x01\x42\x12\n\x10_primal_solutionB\x10\n\x0e_dual_solutionB\x08\n\x06_basis*\x96\x01\n\x13SolutionStatusProto\x12\x1f\n\x1bSOLUTION_STATUS_UNSPECIFIED\x10\x00\x12 \n\x1cSOLUTION_STATUS_UNDETERMINED\x10\x01\x12\x1c\n\x18SOLUTION_STATUS_FEASIBLE\x10\x02\x12\x1e\n\x1aSOLUTION_STATUS_INFEASIBLE\x10\x03*\xbf\x01\n\x10\x42\x61sisStatusProto\x12\x1c\n\x18\x42\x41SIS_STATUS_UNSPECIFIED\x10\x00\x12\x15\n\x11\x42\x41SIS_STATUS_FREE\x10\x01\x12\x1f\n\x1b\x42\x41SIS_STATUS_AT_LOWER_BOUND\x10\x02\x12\x1f\n\x1b\x42\x41SIS_STATUS_AT_UPPER_BOUND\x10\x03\x12\x1c\n\x18\x42\x41SIS_STATUS_FIXED_VALUE\x10\x04\x12\x16\n\x12\x42\x41SIS_STATUS_BASIC\x10\x05\x42\x1e\n\x1a\x63om.google.ortools.mathoptP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.solution_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.ortools.mathoptP\001'
  _globals['_PRIMALSOLUTIONPROTO_AUXILIARYOBJECTIVEVALUESENTRY']._loaded_options = None
  _globals['_PRIMALSOLUTIONPROTO_AUXILIARYOBJECTIVEVALUESENTRY']._serialized_options = b'8\001'
  _globals['_SOLUTIONSTATUSPROTO']._serialized_start=1808
  _globals['_SOLUTIONSTATUSPROTO']._serialized_end=1958
  _globals['_BASISSTATUSPROTO']._serialized_start=1961
  _globals['_BASISSTATUSPROTO']._serialized_end=2152
  _globals['_PRIMALSOLUTIONPROTO']._serialized_start=108
  _globals['_PRIMALSOLUTIONPROTO']._serialized_end=495
  _globals['_PRIMALSOLUTIONPROTO_AUXILIARYOBJECTIVEVALUESENTRY']._serialized_start=432
  _globals['_PRIMALSOLUTIONPROTO_AUXILIARYOBJECTIVEVALUESENTRY']._serialized_end=495
  _globals['_PRIMALRAYPROTO']._serialized_start=497
  _globals['_PRIMALRAYPROTO']._serialized_end=593
  _globals['_DUALSOLUTIONPROTO']._serialized_start=596
  _globals['_DUALSOLUTIONPROTO']._serialized_end=984
  _globals['_DUALRAYPROTO']._serialized_start=987
  _globals['_DUALRAYPROTO']._serialized_end=1155
  _globals['_SPARSEBASISSTATUSVECTOR']._serialized_start=1157
  _globals['_SPARSEBASISSTATUSVECTOR']._serialized_end=1259
  _globals['_BASISPROTO']._serialized_start=1262
  _globals['_BASISPROTO']._serialized_end=1519
  _globals['_SOLUTIONPROTO']._serialized_start=1522
  _globals['_SOLUTIONPROTO']._serialized_end=1805
# @@protoc_insertion_point(module_scope)
