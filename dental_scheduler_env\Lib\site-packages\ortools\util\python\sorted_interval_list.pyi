class Domain:
    def __init__(self, arg0: int, arg1: int) -> None: ...
    @staticmethod
    def AllValues() -> Domain: ...
    def FlattenedIntervals(self) -> list[int]: ...
    @staticmethod
    def FromFlatIntervals(flat_intervals: list[int]) -> Domain: ...
    @staticmethod
    def FromIntervals(intervals: list[list[int]]) -> Domain: ...
    @staticmethod
    def FromValues(values: list[int]) -> Domain: ...
    def addition_with(self, domain: Domain) -> Domain: ...
    @staticmethod
    def all_values() -> Domain: ...
    def complement(self) -> Domain: ...
    def contains(self, value: int) -> bool: ...
    def flattened_intervals(self) -> list[int]: ...
    @staticmethod
    def from_flat_intervals(flat_intervals: list[int]) -> Domain: ...
    @staticmethod
    def from_intervals(intervals: list[list[int]]) -> Domain: ...
    @staticmethod
    def from_values(values: list[int]) -> Domain: ...
    def intersection_with(self, domain: Domain) -> Domain: ...
    def is_empty(self) -> bool: ...
    def max(self) -> int: ...
    def min(self) -> int: ...
    def negation(self) -> Domain: ...
    def size(self) -> int: ...
    def union_with(self, domain: Domain) -> Domain: ...
