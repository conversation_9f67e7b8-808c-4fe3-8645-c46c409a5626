r'''
# OR-Tools

This is the reference documentation for Google OR-Tools.

This repository contains several component:

### Link with the C++ layer
 - `ortools.init.python.init`,

### Knapsack solver
 - `ortools.algorithms.python.knapsack_solver`,

### Graph and flow algorithms
- `ortools.graph.python.linear_sum_assignment`,
- `ortools.graph.python.max_flow`,
- `ortools.graph.python.min_cost_flow`,

### Routing library and legacy Constraint Programming Solver
 - `ortools.constraint_solver.pywrapcp`,

### Minimalistic linear solver wrapper
- `ortools.linear_solver.python.model_builder`

### CP-SAT
 - `ortools.sat.python.cp_model`,
 - `ortools.util.python.sorted_interval_list`,
 - `ortools.sat.cp_model_pb2`
 - `ortools.sat.sat_parameters_pb2`

### Legacy linear solver wrapper
 - `ortools.linear_solver.pywraplp`,
 - `ortools.linear_solver.linear_solver_pb2`,

### Linear solver backends
 - `ortools.bop.bop_parameters_pb2`,
 - `ortools.glop.parameters_pb2`,
 - `ortools.pdlp`,

### PSPLIB Parser
 - `ortools.scheduling.python.rcpsp`,
'''

__docformat__ = "markdown"  # explicitly disable rST processing above.
__version__ = "9.14.6206"

import os
import logging

def _load_ortools_libs():
    """Load shared libraries on Windows"""
    if os.name == "nt":
        logger = logging.getLogger("ortools")
        try:
            from ctypes import WinDLL
            basedir = os.path.dirname(__file__)
        except:
            logger.error(f"ImportError: Cannot import WinDLL")
        else:
            for dll in ["zlib1.dll", "bz2.dll",
                        "abseil_dll.dll", "utf8_validity.dll", "re2.dll", "libprotobuf.dll",
                        "highs.dll", "libscip.dll",
                        "ortools.dll"]:
                dll_path = os.path.join(basedir, ".libs", dll)
                if os.path.exists(dll_path):
                    logger.debug(f"Loading {dll_path}...")
                    WinDLL(dll_path)
                else:
                    logger.debug(f"Cannot find {dll_path}")


_load_ortools_libs()
