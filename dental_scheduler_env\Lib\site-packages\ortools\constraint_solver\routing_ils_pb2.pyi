"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Protocol buffer used to parametrize an iterated local search (ILS) approach.
ILS is an iterative metaheuristic in which every iteration consists in
performing a perturbation followed by an improvement step on a reference
solution to generate a neighbor solution.
The neighbor solution is accepted as the new reference solution according
to an acceptance criterion.
The best found solution is eventually returned.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import ortools.constraint_solver.routing_enums_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class SpatiallyCloseRoutesRuinStrategy(google.protobuf.message.Message):
    """Ruin strategy that removes a number of spatially close routes."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NUM_RUINED_ROUTES_FIELD_NUMBER: builtins.int
    num_ruined_routes: builtins.int
    """Number of spatially close routes ruined at each ruin application."""
    def __init__(
        self,
        *,
        num_ruined_routes: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_num_ruined_routes", b"_num_ruined_routes", "num_ruined_routes", b"num_ruined_routes"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_num_ruined_routes", b"_num_ruined_routes", "num_ruined_routes", b"num_ruined_routes"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_num_ruined_routes", b"_num_ruined_routes"]) -> typing.Literal["num_ruined_routes"] | None: ...

global___SpatiallyCloseRoutesRuinStrategy = SpatiallyCloseRoutesRuinStrategy

@typing.final
class RandomWalkRuinStrategy(google.protobuf.message.Message):
    """Ruin strategy that removes a number of customers by performing a random walk
    on the underlying routing solution. More precisely, starting from a randomly
    selected seed visit, the walk is extended by either moving within the
    same route or by jumping to a visit served by a different neighboring
    route. Every active visit encountered along this random walk is made
    unperformed.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NUM_REMOVED_VISITS_FIELD_NUMBER: builtins.int
    num_removed_visits: builtins.int
    """Number of visits removed during a ruin application defined on visits.
    Note that pickup and delivery pairs are considered as a single entity,
    i.e., the removal of a pickup (respectively delivery) causes the removal of
    the associated delivery (respectively pickup) and it counts as a single
    removal.
    """
    def __init__(
        self,
        *,
        num_removed_visits: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_num_removed_visits", b"_num_removed_visits", "num_removed_visits", b"num_removed_visits"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_num_removed_visits", b"_num_removed_visits", "num_removed_visits", b"num_removed_visits"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_num_removed_visits", b"_num_removed_visits"]) -> typing.Literal["num_removed_visits"] | None: ...

global___RandomWalkRuinStrategy = RandomWalkRuinStrategy

@typing.final
class SISRRuinStrategy(google.protobuf.message.Message):
    """Ruin strategy based on the "Slack Induction by String Removals for Vehicle
    Routing Problems" by Jan Christiaens and Greet Vanden Berghe, Transportation
    Science 2020.
    Link to paper:
    https://kuleuven.limo.libis.be/discovery/fulldisplay?docid=lirias1988666&context=SearchWebhook&vid=32KUL_KUL:Lirias&lang=en&search_scope=lirias_profile&adaptor=SearchWebhook&tab=LIRIAS&query=any,contains,LIRIAS1988666&offset=0

    Note that, in this implementation, the notion of "string" is replaced by
    "sequence".

    The main idea of this ruin is to remove a number of geographically close
    sequences of nodes. In particular, at every ruin application, a maximum
    number max_ruined_routes of routes are disrupted. The value for
    max_ruined_routes is defined as
          (4 * avg_num_removed_visits) / (1 + max_sequence_size) + 1
    with
    - avg_num_removed_visits: user-defined parameter ruling the average number of
      visits that are removed in face of several ruin applications (see also the
      proto message below)
    - max_sequence_size is defined as
           min{max_removed_sequence_size, average_route_size}
      with
      - max_removed_sequence_size: user-defined parameter that specifies
        the maximum number of visits removed from a single route (see also the
        proto message below)
      - average_route_size: the average size of a non-empty route in the current
        solution

    The actual number of ruined routes is then obtained as
                     floor(U(1, max_ruined_routes + 1))
    where U is a continuous uniform distribution of real values in the given
    interval.

    The routes affected by the ruin procedure are selected as follows.
    First, a non start/end seed node is randomly selected. The route serving this
    node is the first ruined route. Then, until the required number of routes has
    been ruined, neighbor nodes of the initial seed node are scanned and the
    associated not yet ruined routes are disrupted. Nodes defining the selected
    routes are designated as seed nodes for the "sequence" and "split sequence"
    removal procedures described below.

    For every selected route, a maximum number route_max_sequence_size of nodes
    are removed. In particular, route_max_sequence_size is defined as
                   min{route_size, max_sequence_size}
    with route_size being the size of the current route.

    Then, the actual number of removed nodes num_removed_nodes is defined as
                 floor(U(1, route_max_sequence_size + 1))
    where U is a continuous uniform distribution of real values in the given
    interval.

    As mentioned above, the selected num_removed_nodes number of nodes is removed
    either via the "sequence" removal or "split sequence" removal procedures. The
    two removal procedures are executed with equal probabilities.

    The "sequence" removal procedure removes a randomly selected sequence of size
    num_removed_nodes that includes the seed node.

    The "split sequence" removal procedure also removes a randomly selected
    sequence of size num_removed_nodes that includes the seed node, but it can
    possibly preserve a subsequence of contiguous nodes.
    In particular, the procedure first selects a sequence of size
    num_removed_nodes + num_bypassed, then num_bypassed contiguous nodes in the
    selected sequence are preserved while the others removed.

    The definition of num_bypassed is as follows. First num_bypassed = 1. The
    current value of num_bypassed is maintaned if
             U(0, 1) < bypass_factor * U(0, 1)
    or the maximum value for num_bypassed, equal to
              route_size - num_removed_nodes
    is reached. The value is incremented of a unit otherwise,
    and the process is repeated. The value assigned to bypass_factor affects the
    number of preserved visits (see also the proto message below).
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    MAX_REMOVED_SEQUENCE_SIZE_FIELD_NUMBER: builtins.int
    AVG_NUM_REMOVED_VISITS_FIELD_NUMBER: builtins.int
    BYPASS_FACTOR_FIELD_NUMBER: builtins.int
    max_removed_sequence_size: builtins.int
    """Maximum number of removed visits per sequence. The parameter name in the
    paper is L^{max} and the suggested value is 10.
    """
    avg_num_removed_visits: builtins.int
    """Number of visits that are removed on average. The parameter name in the
    paper is \\bar{c} and the suggested value is 10.
    """
    bypass_factor: builtins.float
    """Value in [0, 1] ruling the number of preserved customers in the split
    sequence removal. The parameter name in the paper is \\alpha and the
    suggested value is 0.01.
    """
    def __init__(
        self,
        *,
        max_removed_sequence_size: builtins.int | None = ...,
        avg_num_removed_visits: builtins.int | None = ...,
        bypass_factor: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_avg_num_removed_visits", b"_avg_num_removed_visits", "_bypass_factor", b"_bypass_factor", "_max_removed_sequence_size", b"_max_removed_sequence_size", "avg_num_removed_visits", b"avg_num_removed_visits", "bypass_factor", b"bypass_factor", "max_removed_sequence_size", b"max_removed_sequence_size"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_avg_num_removed_visits", b"_avg_num_removed_visits", "_bypass_factor", b"_bypass_factor", "_max_removed_sequence_size", b"_max_removed_sequence_size", "avg_num_removed_visits", b"avg_num_removed_visits", "bypass_factor", b"bypass_factor", "max_removed_sequence_size", b"max_removed_sequence_size"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_avg_num_removed_visits", b"_avg_num_removed_visits"]) -> typing.Literal["avg_num_removed_visits"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_bypass_factor", b"_bypass_factor"]) -> typing.Literal["bypass_factor"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_max_removed_sequence_size", b"_max_removed_sequence_size"]) -> typing.Literal["max_removed_sequence_size"] | None: ...

global___SISRRuinStrategy = SISRRuinStrategy

@typing.final
class RuinStrategy(google.protobuf.message.Message):
    """Ruin strategies, used in perturbation based on ruin and recreate approaches."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPATIALLY_CLOSE_ROUTES_FIELD_NUMBER: builtins.int
    RANDOM_WALK_FIELD_NUMBER: builtins.int
    SISR_FIELD_NUMBER: builtins.int
    @property
    def spatially_close_routes(self) -> global___SpatiallyCloseRoutesRuinStrategy: ...
    @property
    def random_walk(self) -> global___RandomWalkRuinStrategy: ...
    @property
    def sisr(self) -> global___SISRRuinStrategy: ...
    def __init__(
        self,
        *,
        spatially_close_routes: global___SpatiallyCloseRoutesRuinStrategy | None = ...,
        random_walk: global___RandomWalkRuinStrategy | None = ...,
        sisr: global___SISRRuinStrategy | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["random_walk", b"random_walk", "sisr", b"sisr", "spatially_close_routes", b"spatially_close_routes", "strategy", b"strategy"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["random_walk", b"random_walk", "sisr", b"sisr", "spatially_close_routes", b"spatially_close_routes", "strategy", b"strategy"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["strategy", b"strategy"]) -> typing.Literal["spatially_close_routes", "random_walk", "sisr"] | None: ...

global___RuinStrategy = RuinStrategy

@typing.final
class RuinCompositionStrategy(google.protobuf.message.Message):
    """The ruin composition strategies specifies how ruin are selected at every ILS
    iteration.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Value:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ValueEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[RuinCompositionStrategy._Value.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSET: RuinCompositionStrategy._Value.ValueType  # 0
        """Unspecified value."""
        RUN_ALL_SEQUENTIALLY: RuinCompositionStrategy._Value.ValueType  # 1
        """Execute all ruin strategies sequentially in the same order provided in
        input.
        """
        RUN_ALL_RANDOMLY: RuinCompositionStrategy._Value.ValueType  # 2
        """Execute all ruin strategies in a random order."""
        RUN_ONE_RANDOMLY: RuinCompositionStrategy._Value.ValueType  # 3
        """Execute a randomly selected ruin."""

    class Value(_Value, metaclass=_ValueEnumTypeWrapper): ...
    UNSET: RuinCompositionStrategy.Value.ValueType  # 0
    """Unspecified value."""
    RUN_ALL_SEQUENTIALLY: RuinCompositionStrategy.Value.ValueType  # 1
    """Execute all ruin strategies sequentially in the same order provided in
    input.
    """
    RUN_ALL_RANDOMLY: RuinCompositionStrategy.Value.ValueType  # 2
    """Execute all ruin strategies in a random order."""
    RUN_ONE_RANDOMLY: RuinCompositionStrategy.Value.ValueType  # 3
    """Execute a randomly selected ruin."""

    def __init__(
        self,
    ) -> None: ...

global___RuinCompositionStrategy = RuinCompositionStrategy

@typing.final
class RuinRecreateParameters(google.protobuf.message.Message):
    """Parameters to configure a perturbation based on a ruin and recreate approach."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RUIN_STRATEGIES_FIELD_NUMBER: builtins.int
    RUIN_COMPOSITION_STRATEGY_FIELD_NUMBER: builtins.int
    RECREATE_STRATEGY_FIELD_NUMBER: builtins.int
    ROUTE_SELECTION_NEIGHBORS_RATIO_FIELD_NUMBER: builtins.int
    ROUTE_SELECTION_MIN_NEIGHBORS_FIELD_NUMBER: builtins.int
    ROUTE_SELECTION_MAX_NEIGHBORS_FIELD_NUMBER: builtins.int
    ruin_composition_strategy: global___RuinCompositionStrategy.Value.ValueType
    """The composition strategy to use when combining the given 'ruin_strategies'.
    Has no effect when ruin_strategies is composed of a single strategy.
    """
    recreate_strategy: ortools.constraint_solver.routing_enums_pb2.FirstSolutionStrategy.Value.ValueType
    """Strategy defining how a reference solution is recreated."""
    route_selection_neighbors_ratio: builtins.float
    """Ratio in [0, 1] of non start/end nodes to consider as neighbors for the
    identification of routes spatially close to a non start/end seed node.

    In particular, given a non start/end seed node s served by route r, we say
    that a route r' is spatially close to the seed node s if there is at
    least one non start/end node s' among the neighbors of s, such that s' is
    served by r'.

    The neighbors_ratio is coupled with the corresponding min_neighbors and
    max_neighbors values, defining the minimum and maximum number of neighbor
    nodes considered for a given seed node:
    num_neighbors = min(max_neighbors,
             max(min_neighbors, neighbors_ratio * NUM_NON_START_END_NODES))

    Neighbors ratio, and minimum and maximum number of non start/end neighbor
    nodes for the identification of spatially close routes.
    """
    route_selection_min_neighbors: builtins.int
    route_selection_max_neighbors: builtins.int
    @property
    def ruin_strategies(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___RuinStrategy]:
        """List of ruin strategies determining how a reference solution is ruined."""

    def __init__(
        self,
        *,
        ruin_strategies: collections.abc.Iterable[global___RuinStrategy] | None = ...,
        ruin_composition_strategy: global___RuinCompositionStrategy.Value.ValueType = ...,
        recreate_strategy: ortools.constraint_solver.routing_enums_pb2.FirstSolutionStrategy.Value.ValueType = ...,
        route_selection_neighbors_ratio: builtins.float | None = ...,
        route_selection_min_neighbors: builtins.int | None = ...,
        route_selection_max_neighbors: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_route_selection_max_neighbors", b"_route_selection_max_neighbors", "_route_selection_min_neighbors", b"_route_selection_min_neighbors", "_route_selection_neighbors_ratio", b"_route_selection_neighbors_ratio", "route_selection_max_neighbors", b"route_selection_max_neighbors", "route_selection_min_neighbors", b"route_selection_min_neighbors", "route_selection_neighbors_ratio", b"route_selection_neighbors_ratio"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_route_selection_max_neighbors", b"_route_selection_max_neighbors", "_route_selection_min_neighbors", b"_route_selection_min_neighbors", "_route_selection_neighbors_ratio", b"_route_selection_neighbors_ratio", "recreate_strategy", b"recreate_strategy", "route_selection_max_neighbors", b"route_selection_max_neighbors", "route_selection_min_neighbors", b"route_selection_min_neighbors", "route_selection_neighbors_ratio", b"route_selection_neighbors_ratio", "ruin_composition_strategy", b"ruin_composition_strategy", "ruin_strategies", b"ruin_strategies"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_route_selection_max_neighbors", b"_route_selection_max_neighbors"]) -> typing.Literal["route_selection_max_neighbors"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_route_selection_min_neighbors", b"_route_selection_min_neighbors"]) -> typing.Literal["route_selection_min_neighbors"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_route_selection_neighbors_ratio", b"_route_selection_neighbors_ratio"]) -> typing.Literal["route_selection_neighbors_ratio"] | None: ...

global___RuinRecreateParameters = RuinRecreateParameters

@typing.final
class PerturbationStrategy(google.protobuf.message.Message):
    """Defines how a reference solution is perturbed."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Value:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ValueEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[PerturbationStrategy._Value.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSET: PerturbationStrategy._Value.ValueType  # 0
        """Unspecified value."""
        RUIN_AND_RECREATE: PerturbationStrategy._Value.ValueType  # 1
        """Performs a perturbation in a ruin and recreate fashion."""

    class Value(_Value, metaclass=_ValueEnumTypeWrapper): ...
    UNSET: PerturbationStrategy.Value.ValueType  # 0
    """Unspecified value."""
    RUIN_AND_RECREATE: PerturbationStrategy.Value.ValueType  # 1
    """Performs a perturbation in a ruin and recreate fashion."""

    def __init__(
        self,
    ) -> None: ...

global___PerturbationStrategy = PerturbationStrategy

@typing.final
class CoolingScheduleStrategy(google.protobuf.message.Message):
    """The cooling schedule strategy defines how to compute the current simulated
    annealing temperature t given
    - the initial temperature t0
    - the final temperature t1
    - the current search progress 0 <= p <= 1

    The value of t0 and t1 is defined by the initial_temperature and
    final_temperature in SimulatedAnnealingParameters, respectively.

    The search progress p is derived, at any given time, by the search limits.
    In particular, p measures how far we are in the search process w.r.t. to the
    number of explored solutions and the time limit.

    The temperature t, computed according to one of the strategies defined below,
    together with the selected AcceptanceStrategy, is used to guide the search
    trajectory. In particular, given a neighbor solution S', generated by the
    the application of the perturbation and improvement step to a reference
    solution S, we have that S will be replaced by S' iff
                     cost(S') + t * log(U(0, 1)) < cost(S)
    where U(0, 1) is a random number sampled from a uniform distribution of real
    numbers in [0, 1].
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Value:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ValueEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[CoolingScheduleStrategy._Value.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSET: CoolingScheduleStrategy._Value.ValueType  # 0
        """Unspecified value."""
        EXPONENTIAL: CoolingScheduleStrategy._Value.ValueType  # 1
        """Exponentially decreases the temperature as the search progresses.
        More precisely, t = t0 * (t1/t0)^p.
        """
        LINEAR: CoolingScheduleStrategy._Value.ValueType  # 2
        """Linearly decreases the temperature as the search progresses.
        More precisely, t = t0 - p * (t0 - t1).
        """

    class Value(_Value, metaclass=_ValueEnumTypeWrapper): ...
    UNSET: CoolingScheduleStrategy.Value.ValueType  # 0
    """Unspecified value."""
    EXPONENTIAL: CoolingScheduleStrategy.Value.ValueType  # 1
    """Exponentially decreases the temperature as the search progresses.
    More precisely, t = t0 * (t1/t0)^p.
    """
    LINEAR: CoolingScheduleStrategy.Value.ValueType  # 2
    """Linearly decreases the temperature as the search progresses.
    More precisely, t = t0 - p * (t0 - t1).
    """

    def __init__(
        self,
    ) -> None: ...

global___CoolingScheduleStrategy = CoolingScheduleStrategy

@typing.final
class SimulatedAnnealingParameters(google.protobuf.message.Message):
    """Specifies the behavior of a simulated annealing acceptance strategy."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    COOLING_SCHEDULE_STRATEGY_FIELD_NUMBER: builtins.int
    INITIAL_TEMPERATURE_FIELD_NUMBER: builtins.int
    FINAL_TEMPERATURE_FIELD_NUMBER: builtins.int
    AUTOMATIC_TEMPERATURES_FIELD_NUMBER: builtins.int
    cooling_schedule_strategy: global___CoolingScheduleStrategy.Value.ValueType
    """Determines the speed at which the temperature changes from initial to
    final.
    """
    initial_temperature: builtins.float
    """The initial temperature. See CoolingScheduleStrategy for its usage."""
    final_temperature: builtins.float
    """The final temperature. See CoolingScheduleStrategy for its usage."""
    automatic_temperatures: builtins.bool
    """Automatically define the value for the temperatures as follows.
    First, a  reference temperature t is defined as
              w1 * c1 + w2 * c2 + ... + wK * cK
    where 0 < wJ <= 1 is the fraction of vehicles of cost class J and cJ is the
    average arc cost for the cost class J.
    The value of cJ is identified by randomly sampling N arc costs for the cost
    class J, where N is equal to the number of instance nodes.
    The initial and final temperatures are then defined as
    - initial_temperature: 0.1 * t
    - final_temperature: 0.001 * t
    """
    def __init__(
        self,
        *,
        cooling_schedule_strategy: global___CoolingScheduleStrategy.Value.ValueType = ...,
        initial_temperature: builtins.float | None = ...,
        final_temperature: builtins.float | None = ...,
        automatic_temperatures: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_automatic_temperatures", b"_automatic_temperatures", "_final_temperature", b"_final_temperature", "_initial_temperature", b"_initial_temperature", "automatic_temperatures", b"automatic_temperatures", "final_temperature", b"final_temperature", "initial_temperature", b"initial_temperature"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_automatic_temperatures", b"_automatic_temperatures", "_final_temperature", b"_final_temperature", "_initial_temperature", b"_initial_temperature", "automatic_temperatures", b"automatic_temperatures", "cooling_schedule_strategy", b"cooling_schedule_strategy", "final_temperature", b"final_temperature", "initial_temperature", b"initial_temperature"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_automatic_temperatures", b"_automatic_temperatures"]) -> typing.Literal["automatic_temperatures"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_final_temperature", b"_final_temperature"]) -> typing.Literal["final_temperature"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_initial_temperature", b"_initial_temperature"]) -> typing.Literal["initial_temperature"] | None: ...

global___SimulatedAnnealingParameters = SimulatedAnnealingParameters

@typing.final
class AcceptanceStrategy(google.protobuf.message.Message):
    """Determines when a neighbor solution, obtained by the application of a
    perturbation and improvement step to a reference solution, is used to
    replace the reference solution.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Value:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ValueEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[AcceptanceStrategy._Value.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSET: AcceptanceStrategy._Value.ValueType  # 0
        """Unspecified value."""
        GREEDY_DESCENT: AcceptanceStrategy._Value.ValueType  # 1
        """Accepts only solutions that are improving with respect to the reference
        one.
        """
        SIMULATED_ANNEALING: AcceptanceStrategy._Value.ValueType  # 2
        """Accepts a candidate solution with a probability that depends on its
        quality and on the current state of the search.
        """

    class Value(_Value, metaclass=_ValueEnumTypeWrapper): ...
    UNSET: AcceptanceStrategy.Value.ValueType  # 0
    """Unspecified value."""
    GREEDY_DESCENT: AcceptanceStrategy.Value.ValueType  # 1
    """Accepts only solutions that are improving with respect to the reference
    one.
    """
    SIMULATED_ANNEALING: AcceptanceStrategy.Value.ValueType  # 2
    """Accepts a candidate solution with a probability that depends on its
    quality and on the current state of the search.
    """

    def __init__(
        self,
    ) -> None: ...

global___AcceptanceStrategy = AcceptanceStrategy

@typing.final
class IteratedLocalSearchParameters(google.protobuf.message.Message):
    """Specifies the behavior of a search based on ILS."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    PERTURBATION_STRATEGY_FIELD_NUMBER: builtins.int
    RUIN_RECREATE_PARAMETERS_FIELD_NUMBER: builtins.int
    IMPROVE_PERTURBED_SOLUTION_FIELD_NUMBER: builtins.int
    ACCEPTANCE_STRATEGY_FIELD_NUMBER: builtins.int
    SIMULATED_ANNEALING_PARAMETERS_FIELD_NUMBER: builtins.int
    perturbation_strategy: global___PerturbationStrategy.Value.ValueType
    """Determines how a reference solution S is perturbed to obtain a neighbor
    solution S'.
    """
    improve_perturbed_solution: builtins.bool
    """Determines whether solution S', obtained from the perturbation, should be
    optimized with a local search application.
    """
    acceptance_strategy: global___AcceptanceStrategy.Value.ValueType
    """Determines when the neighbor solution S', possibly improved if
    `improve_perturbed_solution` is true, replaces the reference solution S.
    """
    @property
    def ruin_recreate_parameters(self) -> global___RuinRecreateParameters:
        """Parameters to customize a ruin and recreate perturbation."""

    @property
    def simulated_annealing_parameters(self) -> global___SimulatedAnnealingParameters:
        """Parameters to customize a simulated annealing acceptance strategy. These
        parameters are required iff the acceptance_strategy is SIMULATED_ANNEALING.
        """

    def __init__(
        self,
        *,
        perturbation_strategy: global___PerturbationStrategy.Value.ValueType = ...,
        ruin_recreate_parameters: global___RuinRecreateParameters | None = ...,
        improve_perturbed_solution: builtins.bool | None = ...,
        acceptance_strategy: global___AcceptanceStrategy.Value.ValueType = ...,
        simulated_annealing_parameters: global___SimulatedAnnealingParameters | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_improve_perturbed_solution", b"_improve_perturbed_solution", "improve_perturbed_solution", b"improve_perturbed_solution", "ruin_recreate_parameters", b"ruin_recreate_parameters", "simulated_annealing_parameters", b"simulated_annealing_parameters"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_improve_perturbed_solution", b"_improve_perturbed_solution", "acceptance_strategy", b"acceptance_strategy", "improve_perturbed_solution", b"improve_perturbed_solution", "perturbation_strategy", b"perturbation_strategy", "ruin_recreate_parameters", b"ruin_recreate_parameters", "simulated_annealing_parameters", b"simulated_annealing_parameters"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_improve_perturbed_solution", b"_improve_perturbed_solution"]) -> typing.Literal["improve_perturbed_solution"] | None: ...

global___IteratedLocalSearchParameters = IteratedLocalSearchParameters
