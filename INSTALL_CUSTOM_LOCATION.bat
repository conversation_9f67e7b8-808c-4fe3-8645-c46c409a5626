@echo off
title Dental Implant Scheduler - Custom Location Installer

echo.
echo ========================================
echo  🦷 Dental Implant Scheduler
echo     Custom Location Installer
echo ========================================
echo.
echo This installer allows you to choose where to install
echo the Dental Implant Manufacturing Scheduler.
echo.

:: Get current directory
set "SOURCE_DIR=%CD%"
echo Current location: %SOURCE_DIR%
echo.

:: Ask user for installation preference
echo Installation Options:
echo.
echo 1. Install in current location (recommended)
echo 2. Choose custom location
echo 3. Install in Documents folder
echo 4. Install in Programs folder
echo.
set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    set "INSTALL_DIR=%SOURCE_DIR%"
    echo Installing in current location...
) else if "%choice%"=="2" (
    echo.
    echo Enter the full path where you want to install:
    echo Example: C:\MyPrograms\DentalScheduler
    set /p "INSTALL_DIR=Installation path: "
) else if "%choice%"=="3" (
    set "INSTALL_DIR=%USERPROFILE%\Documents\DentalScheduler"
    echo Installing in Documents folder...
) else if "%choice%"=="4" (
    set "INSTALL_DIR=C:\Programs\DentalScheduler"
    echo Installing in Programs folder...
) else (
    echo Invalid choice. Installing in current location...
    set "INSTALL_DIR=%SOURCE_DIR%"
)

echo.
echo Installation directory: %INSTALL_DIR%
echo.

:: If installing to a different location, copy files
if not "%INSTALL_DIR%"=="%SOURCE_DIR%" (
    echo Creating installation directory...
    if not exist "%INSTALL_DIR%" (
        mkdir "%INSTALL_DIR%"
        if %errorlevel% neq 0 (
            echo ERROR: Could not create directory %INSTALL_DIR%
            echo Please check permissions or choose a different location.
            pause
            exit /b 1
        )
    )
    
    echo Copying files to installation directory...
    xcopy "%SOURCE_DIR%\*" "%INSTALL_DIR%\" /E /I /H /Y
    if %errorlevel% neq 0 (
        echo ERROR: Failed to copy files to %INSTALL_DIR%
        pause
        exit /b 1
    )
    
    echo Files copied successfully.
    echo.
    
    :: Change to installation directory
    cd /d "%INSTALL_DIR%"
)

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✓ Python found
python --version

:: Run the Python installer
echo.
echo Running installation...
python install.py

if %errorlevel% neq 0 (
    echo.
    echo Installation failed. Please check the error messages above.
    pause
    exit /b 1
)

:: Create desktop shortcut if requested
echo.
set /p desktop_shortcut="Create desktop shortcut? (y/N): "
if /i "%desktop_shortcut%"=="y" (
    echo Creating desktop shortcut...
    set DESKTOP=%USERPROFILE%\Desktop
    set SHORTCUT_GUI=%DESKTOP%\Dental Scheduler.bat
    
    echo @echo off > "%SHORTCUT_GUI%"
    echo cd /d "%INSTALL_DIR%" >> "%SHORTCUT_GUI%"
    echo call dental_scheduler_env\Scripts\activate.bat >> "%SHORTCUT_GUI%"
    echo python dental_scheduler_gui.py >> "%SHORTCUT_GUI%"
    
    echo ✓ Desktop shortcut created: Dental Scheduler.bat
)

echo.
echo ========================================
echo  Installation Complete!
echo ========================================
echo.
echo Installation location: %INSTALL_DIR%
echo.
echo To run the application:
if "%INSTALL_DIR%"=="%SOURCE_DIR%" (
    echo   • Double-click: run_gui.bat
    echo   • Or run: dental_scheduler_gui.py
) else (
    echo   • Navigate to: %INSTALL_DIR%
    echo   • Double-click: run_gui.bat
    if /i "%desktop_shortcut%"=="y" (
        echo   • Or use desktop shortcut: Dental Scheduler.bat
    )
)
echo.
echo Documentation: README.md
echo Sample jobs: Job_Upload_Template.xlsx
echo.
pause
