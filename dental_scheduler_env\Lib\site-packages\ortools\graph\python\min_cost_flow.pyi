import numpy
from typing import ClassVar

class SimpleMinCostFlow:
    class Status:
        __members__: ClassVar[dict] = ...  # read-only
        BAD_CAPACITY_RANGE: ClassVar[SimpleMinCostFlow.Status] = ...
        BAD_COST_RANGE: ClassVar[SimpleMinCostFlow.Status] = ...
        BAD_RESULT: ClassVar[SimpleMinCostFlow.Status] = ...
        FEASIBLE: ClassVar[SimpleMinCostFlow.Status] = ...
        INFEASIBLE: ClassVar[SimpleMinCostFlow.Status] = ...
        NOT_SOLVED: ClassVar[SimpleMinCostFlow.Status] = ...
        OPTIMAL: ClassVar[SimpleMinCostFlow.Status] = ...
        UNBALANCED: ClassVar[SimpleMinCostFlow.Status] = ...
        __entries: ClassVar[dict] = ...
        def __init__(self, value: int) -> None: ...
        def __eq__(self, other: object) -> bool: ...
        def __hash__(self) -> int: ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...
        def __ne__(self, other: object) -> bool: ...
        @property
        def name(self) -> str: ...
        @property
        def value(self) -> int: ...
    BAD_CAPACITY_RANGE: ClassVar[SimpleMinCostFlow.Status] = ...
    BAD_COST_RANGE: ClassVar[SimpleMinCostFlow.Status] = ...
    BAD_RESULT: ClassVar[SimpleMinCostFlow.Status] = ...
    FEASIBLE: ClassVar[SimpleMinCostFlow.Status] = ...
    INFEASIBLE: ClassVar[SimpleMinCostFlow.Status] = ...
    NOT_SOLVED: ClassVar[SimpleMinCostFlow.Status] = ...
    OPTIMAL: ClassVar[SimpleMinCostFlow.Status] = ...
    UNBALANCED: ClassVar[SimpleMinCostFlow.Status] = ...
    def __init__(self) -> None: ...
    def add_arc_with_capacity_and_unit_cost(self, tail: int, head: int, capacity: int, unit_cost: int) -> int: ...
    def add_arcs_with_capacity_and_unit_cost(self, tails: numpy.ndarray[numpy.int32], heads: numpy.ndarray[numpy.int32], capacities: numpy.ndarray[numpy.int64], unit_costs: numpy.ndarray[numpy.int64]) -> object: ...
    def capacity(self, arc: int) -> int: ...
    def flow(self, arc: int) -> int: ...
    def flows(self, arcs: numpy.ndarray[numpy.int32]) -> object: ...
    def head(self, arc: int) -> int: ...
    def maximum_flow(self) -> int: ...
    def num_arcs(self) -> int: ...
    def num_nodes(self) -> int: ...
    def optimal_cost(self) -> int: ...
    def set_arc_capacities(self, arcs: numpy.ndarray[numpy.int32], capacities: numpy.ndarray[numpy.int64]) -> object: ...
    def set_arc_capacity(self, arc: int, capacity: int) -> None: ...
    def set_node_supply(self, node: int, supply: int) -> None: ...
    def set_nodes_supplies(self, nodes: numpy.ndarray[numpy.int32], supplies: numpy.ndarray[numpy.int64]) -> object: ...
    def solve(self, *args, **kwargs): ...
    def solve_max_flow_with_min_cost(self, *args, **kwargs): ...
    def supply(self, node: int) -> int: ...
    def tail(self, arc: int) -> int: ...
    def unit_cost(self, arc: int) -> int: ...
