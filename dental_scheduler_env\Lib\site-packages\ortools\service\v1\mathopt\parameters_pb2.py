# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/service/v1/mathopt/parameters.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/service/v1/mathopt/parameters.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+ortools/service/v1/mathopt/parameters.proto\x12&operations_research.service.v1.mathopt\x1a\x1egoogle/protobuf/duration.proto\"\xa3\x08\n\x14SolveParametersProto\x12-\n\ntime_limit\x18\x01 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x1c\n\x0fiteration_limit\x18\x02 \x01(\x03H\x00\x88\x01\x01\x12\x17\n\nnode_limit\x18\x18 \x01(\x03H\x01\x88\x01\x01\x12\x19\n\x0c\x63utoff_limit\x18\x14 \x01(\x01H\x02\x88\x01\x01\x12\x1c\n\x0fobjective_limit\x18\x15 \x01(\x01H\x03\x88\x01\x01\x12\x1d\n\x10\x62\x65st_bound_limit\x18\x16 \x01(\x01H\x04\x88\x01\x01\x12\x1b\n\x0esolution_limit\x18\x17 \x01(\x05H\x05\x88\x01\x01\x12\x15\n\renable_output\x18\x03 \x01(\x08\x12\x14\n\x07threads\x18\x04 \x01(\x05H\x06\x88\x01\x01\x12\x18\n\x0brandom_seed\x18\x05 \x01(\x05H\x07\x88\x01\x01\x12#\n\x16\x61\x62solute_gap_tolerance\x18\x12 \x01(\x01H\x08\x88\x01\x01\x12#\n\x16relative_gap_tolerance\x18\x11 \x01(\x01H\t\x88\x01\x01\x12\x1f\n\x12solution_pool_size\x18\x19 \x01(\x05H\n\x88\x01\x01\x12N\n\x0clp_algorithm\x18\x06 \x01(\x0e\x32\x38.operations_research.service.v1.mathopt.LPAlgorithmProto\x12G\n\x08presolve\x18\x07 \x01(\x0e\x32\x35.operations_research.service.v1.mathopt.EmphasisProto\x12\x43\n\x04\x63uts\x18\x08 \x01(\x0e\x32\x35.operations_research.service.v1.mathopt.EmphasisProto\x12I\n\nheuristics\x18\t \x01(\x0e\x32\x35.operations_research.service.v1.mathopt.EmphasisProto\x12\x46\n\x07scaling\x18\n \x01(\x0e\x32\x35.operations_research.service.v1.mathopt.EmphasisProtoB\x12\n\x10_iteration_limitB\r\n\x0b_node_limitB\x0f\n\r_cutoff_limitB\x12\n\x10_objective_limitB\x13\n\x11_best_bound_limitB\x11\n\x0f_solution_limitB\n\n\x08_threadsB\x0e\n\x0c_random_seedB\x19\n\x17_absolute_gap_toleranceB\x19\n\x17_relative_gap_toleranceB\x15\n\x13_solution_pool_sizeJ\x04\x08\x0c\x10\rJ\x04\x08\r\x10\x0eJ\x04\x08\x0e\x10\x0fJ\x04\x08\x0f\x10\x10J\x04\x08\x10\x10\x11J\x04\x08\x13\x10\x14J\x04\x08\x1a\x10\x1bJ\x04\x08\x1b\x10\x1cJ\x04\x08\x0b\x10\x0c*\xc2\x02\n\x0fSolverTypeProto\x12\x1b\n\x17SOLVER_TYPE_UNSPECIFIED\x10\x00\x12\x15\n\x11SOLVER_TYPE_GSCIP\x10\x01\x12\x16\n\x12SOLVER_TYPE_GUROBI\x10\x02\x12\x14\n\x10SOLVER_TYPE_GLOP\x10\x03\x12\x16\n\x12SOLVER_TYPE_CP_SAT\x10\x04\x12\x14\n\x10SOLVER_TYPE_PDLP\x10\x05\x12\x14\n\x10SOLVER_TYPE_GLPK\x10\x06\x12\x14\n\x10SOLVER_TYPE_OSQP\x10\x07\x12\x14\n\x10SOLVER_TYPE_ECOS\x10\x08\x12\x13\n\x0fSOLVER_TYPE_SCS\x10\t\x12\x15\n\x11SOLVER_TYPE_HIGHS\x10\n\x12\x19\n\x15SOLVER_TYPE_SANTORINI\x10\x0b\x12\x16\n\x12SOLVER_TYPE_XPRESS\x10\x0c*\xa8\x01\n\x10LPAlgorithmProto\x12\x1c\n\x18LP_ALGORITHM_UNSPECIFIED\x10\x00\x12\x1f\n\x1bLP_ALGORITHM_PRIMAL_SIMPLEX\x10\x01\x12\x1d\n\x19LP_ALGORITHM_DUAL_SIMPLEX\x10\x02\x12\x18\n\x14LP_ALGORITHM_BARRIER\x10\x03\x12\x1c\n\x18LP_ALGORITHM_FIRST_ORDER\x10\x04*\x8d\x01\n\rEmphasisProto\x12\x18\n\x14\x45MPHASIS_UNSPECIFIED\x10\x00\x12\x10\n\x0c\x45MPHASIS_OFF\x10\x01\x12\x10\n\x0c\x45MPHASIS_LOW\x10\x02\x12\x13\n\x0f\x45MPHASIS_MEDIUM\x10\x03\x12\x11\n\rEMPHASIS_HIGH\x10\x04\x12\x16\n\x12\x45MPHASIS_VERY_HIGH\x10\x05\x42\x42\n%com.google.ortools.service.v1.mathoptP\x01\xaa\x02\x16Google.OrTools.Serviceb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.service.v1.mathopt.parameters_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.google.ortools.service.v1.mathoptP\001\252\002\026Google.OrTools.Service'
  _globals['_SOLVERTYPEPROTO']._serialized_start=1182
  _globals['_SOLVERTYPEPROTO']._serialized_end=1504
  _globals['_LPALGORITHMPROTO']._serialized_start=1507
  _globals['_LPALGORITHMPROTO']._serialized_end=1675
  _globals['_EMPHASISPROTO']._serialized_start=1678
  _globals['_EMPHASISPROTO']._serialized_end=1819
  _globals['_SOLVEPARAMETERSPROTO']._serialized_start=120
  _globals['_SOLVEPARAMETERSPROTO']._serialized_end=1179
# @@protoc_insertion_point(module_scope)
