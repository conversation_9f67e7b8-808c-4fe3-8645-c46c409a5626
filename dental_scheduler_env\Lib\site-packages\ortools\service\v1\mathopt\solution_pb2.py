# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/service/v1/mathopt/solution.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/service/v1/mathopt/solution.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.service.v1.mathopt import sparse_containers_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_sparse__containers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)ortools/service/v1/mathopt/solution.proto\x12&operations_research.service.v1.mathopt\x1a\x32ortools/service/v1/mathopt/sparse_containers.proto\"\xa1\x03\n\x13PrimalSolutionProto\x12X\n\x0fvariable_values\x18\x01 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\x12\x17\n\x0fobjective_value\x18\x02 \x01(\x01\x12}\n\x1a\x61uxiliary_objective_values\x18\x04 \x03(\x0b\x32Y.operations_research.service.v1.mathopt.PrimalSolutionProto.AuxiliaryObjectiveValuesEntry\x12W\n\x12\x66\x65\x61sibility_status\x18\x03 \x01(\x0e\x32;.operations_research.service.v1.mathopt.SolutionStatusProto\x1a?\n\x1d\x41uxiliaryObjectiveValuesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\"j\n\x0ePrimalRayProto\x12X\n\x0fvariable_values\x18\x01 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\"\xcc\x02\n\x11\x44ualSolutionProto\x12T\n\x0b\x64ual_values\x18\x01 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\x12V\n\rreduced_costs\x18\x02 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\x12\x1c\n\x0fobjective_value\x18\x03 \x01(\x01H\x00\x88\x01\x01\x12W\n\x12\x66\x65\x61sibility_status\x18\x04 \x01(\x0e\x32;.operations_research.service.v1.mathopt.SolutionStatusProtoB\x12\n\x10_objective_value\"\xbc\x01\n\x0c\x44ualRayProto\x12T\n\x0b\x64ual_values\x18\x01 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\x12V\n\rreduced_costs\x18\x02 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\"p\n\x17SparseBasisStatusVector\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12H\n\x06values\x18\x02 \x03(\x0e\x32\x38.operations_research.service.v1.mathopt.BasisStatusProto\"\x9f\x02\n\nBasisProto\x12Z\n\x11\x63onstraint_status\x18\x01 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseBasisStatusVector\x12X\n\x0fvariable_status\x18\x02 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseBasisStatusVector\x12[\n\x16\x62\x61sic_dual_feasibility\x18\x03 \x01(\x0e\x32;.operations_research.service.v1.mathopt.SolutionStatusProto\"\xb9\x02\n\rSolutionProto\x12Y\n\x0fprimal_solution\x18\x01 \x01(\x0b\x32;.operations_research.service.v1.mathopt.PrimalSolutionProtoH\x00\x88\x01\x01\x12U\n\rdual_solution\x18\x02 \x01(\x0b\x32\x39.operations_research.service.v1.mathopt.DualSolutionProtoH\x01\x88\x01\x01\x12\x46\n\x05\x62\x61sis\x18\x03 \x01(\x0b\x32\x32.operations_research.service.v1.mathopt.BasisProtoH\x02\x88\x01\x01\x42\x12\n\x10_primal_solutionB\x10\n\x0e_dual_solutionB\x08\n\x06_basis*\x96\x01\n\x13SolutionStatusProto\x12\x1f\n\x1bSOLUTION_STATUS_UNSPECIFIED\x10\x00\x12 \n\x1cSOLUTION_STATUS_UNDETERMINED\x10\x01\x12\x1c\n\x18SOLUTION_STATUS_FEASIBLE\x10\x02\x12\x1e\n\x1aSOLUTION_STATUS_INFEASIBLE\x10\x03*\xbf\x01\n\x10\x42\x61sisStatusProto\x12\x1c\n\x18\x42\x41SIS_STATUS_UNSPECIFIED\x10\x00\x12\x15\n\x11\x42\x41SIS_STATUS_FREE\x10\x01\x12\x1f\n\x1b\x42\x41SIS_STATUS_AT_LOWER_BOUND\x10\x02\x12\x1f\n\x1b\x42\x41SIS_STATUS_AT_UPPER_BOUND\x10\x03\x12\x1c\n\x18\x42\x41SIS_STATUS_FIXED_VALUE\x10\x04\x12\x16\n\x12\x42\x41SIS_STATUS_BASIC\x10\x05\x42\x42\n%com.google.ortools.service.v1.mathoptP\x01\xaa\x02\x16Google.OrTools.Serviceb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.service.v1.mathopt.solution_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.google.ortools.service.v1.mathoptP\001\252\002\026Google.OrTools.Service'
  _globals['_PRIMALSOLUTIONPROTO_AUXILIARYOBJECTIVEVALUESENTRY']._loaded_options = None
  _globals['_PRIMALSOLUTIONPROTO_AUXILIARYOBJECTIVEVALUESENTRY']._serialized_options = b'8\001'
  _globals['_SOLUTIONSTATUSPROTO']._serialized_start=1912
  _globals['_SOLUTIONSTATUSPROTO']._serialized_end=2062
  _globals['_BASISSTATUSPROTO']._serialized_start=2065
  _globals['_BASISSTATUSPROTO']._serialized_end=2256
  _globals['_PRIMALSOLUTIONPROTO']._serialized_start=138
  _globals['_PRIMALSOLUTIONPROTO']._serialized_end=555
  _globals['_PRIMALSOLUTIONPROTO_AUXILIARYOBJECTIVEVALUESENTRY']._serialized_start=492
  _globals['_PRIMALSOLUTIONPROTO_AUXILIARYOBJECTIVEVALUESENTRY']._serialized_end=555
  _globals['_PRIMALRAYPROTO']._serialized_start=557
  _globals['_PRIMALRAYPROTO']._serialized_end=663
  _globals['_DUALSOLUTIONPROTO']._serialized_start=666
  _globals['_DUALSOLUTIONPROTO']._serialized_end=998
  _globals['_DUALRAYPROTO']._serialized_start=1001
  _globals['_DUALRAYPROTO']._serialized_end=1189
  _globals['_SPARSEBASISSTATUSVECTOR']._serialized_start=1191
  _globals['_SPARSEBASISSTATUSVECTOR']._serialized_end=1303
  _globals['_BASISPROTO']._serialized_start=1306
  _globals['_BASISPROTO']._serialized_end=1593
  _globals['_SOLUTIONPROTO']._serialized_start=1596
  _globals['_SOLUTIONPROTO']._serialized_end=1909
# @@protoc_insertion_point(module_scope)
