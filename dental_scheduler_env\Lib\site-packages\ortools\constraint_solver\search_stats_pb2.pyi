"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Protocol buffer used to store search statistics."""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class LocalSearchStatistics(google.protobuf.message.Message):
    """Statistics on local search."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class FirstSolutionStatistics(google.protobuf.message.Message):
        """First solution statistics collected during search."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        STRATEGY_FIELD_NUMBER: builtins.int
        DURATION_SECONDS_FIELD_NUMBER: builtins.int
        strategy: builtins.str
        """Name of the strategy used."""
        duration_seconds: builtins.float
        """Time spent in the decision builder."""
        def __init__(
            self,
            *,
            strategy: builtins.str = ...,
            duration_seconds: builtins.float = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["duration_seconds", b"duration_seconds", "strategy", b"strategy"]) -> None: ...

    @typing.final
    class LocalSearchOperatorStatistics(google.protobuf.message.Message):
        """Statistics on local search operators called during the search."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        LOCAL_SEARCH_OPERATOR_FIELD_NUMBER: builtins.int
        NUM_NEIGHBORS_FIELD_NUMBER: builtins.int
        NUM_FILTERED_NEIGHBORS_FIELD_NUMBER: builtins.int
        NUM_ACCEPTED_NEIGHBORS_FIELD_NUMBER: builtins.int
        DURATION_SECONDS_FIELD_NUMBER: builtins.int
        MAKE_NEXT_NEIGHBOR_DURATION_SECONDS_FIELD_NUMBER: builtins.int
        ACCEPT_NEIGHBOR_DURATION_SECONDS_FIELD_NUMBER: builtins.int
        local_search_operator: builtins.str
        """Name of the operator."""
        num_neighbors: builtins.int
        """Number of neighbors generated by the operator."""
        num_filtered_neighbors: builtins.int
        """Number of neighbors which were filtered."""
        num_accepted_neighbors: builtins.int
        """Number of neighbors eventually accepted."""
        duration_seconds: builtins.float
        """Time spent in the operator."""
        make_next_neighbor_duration_seconds: builtins.float
        """Time spent in creating neighbors (calling MakeNextNeighbor)."""
        accept_neighbor_duration_seconds: builtins.float
        """Time spent in accepting a neighbor (restoration and storage, not
        including filtering).
        """
        def __init__(
            self,
            *,
            local_search_operator: builtins.str = ...,
            num_neighbors: builtins.int = ...,
            num_filtered_neighbors: builtins.int = ...,
            num_accepted_neighbors: builtins.int = ...,
            duration_seconds: builtins.float = ...,
            make_next_neighbor_duration_seconds: builtins.float = ...,
            accept_neighbor_duration_seconds: builtins.float = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["accept_neighbor_duration_seconds", b"accept_neighbor_duration_seconds", "duration_seconds", b"duration_seconds", "local_search_operator", b"local_search_operator", "make_next_neighbor_duration_seconds", b"make_next_neighbor_duration_seconds", "num_accepted_neighbors", b"num_accepted_neighbors", "num_filtered_neighbors", b"num_filtered_neighbors", "num_neighbors", b"num_neighbors"]) -> None: ...

    @typing.final
    class LocalSearchFilterStatistics(google.protobuf.message.Message):
        """Statistics on local search filters called during the search."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        LOCAL_SEARCH_FILTER_FIELD_NUMBER: builtins.int
        NUM_CALLS_FIELD_NUMBER: builtins.int
        NUM_REJECTS_FIELD_NUMBER: builtins.int
        DURATION_SECONDS_FIELD_NUMBER: builtins.int
        NUM_REJECTS_PER_SECOND_FIELD_NUMBER: builtins.int
        CONTEXT_FIELD_NUMBER: builtins.int
        local_search_filter: builtins.str
        """Name of the filter."""
        num_calls: builtins.int
        """Number of times the filter was called."""
        num_rejects: builtins.int
        """Number of times the filter rejected a neighbor."""
        duration_seconds: builtins.float
        """Time spent in the filter."""
        num_rejects_per_second: builtins.float
        """Number of rejects per second."""
        context: builtins.str
        """Context within which the filter was called."""
        def __init__(
            self,
            *,
            local_search_filter: builtins.str = ...,
            num_calls: builtins.int = ...,
            num_rejects: builtins.int = ...,
            duration_seconds: builtins.float = ...,
            num_rejects_per_second: builtins.float = ...,
            context: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["context", b"context", "duration_seconds", b"duration_seconds", "local_search_filter", b"local_search_filter", "num_calls", b"num_calls", "num_rejects", b"num_rejects", "num_rejects_per_second", b"num_rejects_per_second"]) -> None: ...

    FIRST_SOLUTION_STATISTICS_FIELD_NUMBER: builtins.int
    LOCAL_SEARCH_OPERATOR_STATISTICS_FIELD_NUMBER: builtins.int
    TOTAL_NUM_NEIGHBORS_FIELD_NUMBER: builtins.int
    TOTAL_NUM_FILTERED_NEIGHBORS_FIELD_NUMBER: builtins.int
    TOTAL_NUM_ACCEPTED_NEIGHBORS_FIELD_NUMBER: builtins.int
    LOCAL_SEARCH_FILTER_STATISTICS_FIELD_NUMBER: builtins.int
    total_num_neighbors: builtins.int
    """Total number of (filtered/accepted) neighbors created during the search."""
    total_num_filtered_neighbors: builtins.int
    total_num_accepted_neighbors: builtins.int
    @property
    def first_solution_statistics(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LocalSearchStatistics.FirstSolutionStatistics]:
        """Statistics for each first solution called during the search."""

    @property
    def local_search_operator_statistics(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LocalSearchStatistics.LocalSearchOperatorStatistics]:
        """Statistics for each operator called during the search."""

    @property
    def local_search_filter_statistics(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LocalSearchStatistics.LocalSearchFilterStatistics]:
        """Statistics for each filter called during the search."""

    def __init__(
        self,
        *,
        first_solution_statistics: collections.abc.Iterable[global___LocalSearchStatistics.FirstSolutionStatistics] | None = ...,
        local_search_operator_statistics: collections.abc.Iterable[global___LocalSearchStatistics.LocalSearchOperatorStatistics] | None = ...,
        total_num_neighbors: builtins.int = ...,
        total_num_filtered_neighbors: builtins.int = ...,
        total_num_accepted_neighbors: builtins.int = ...,
        local_search_filter_statistics: collections.abc.Iterable[global___LocalSearchStatistics.LocalSearchFilterStatistics] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["first_solution_statistics", b"first_solution_statistics", "local_search_filter_statistics", b"local_search_filter_statistics", "local_search_operator_statistics", b"local_search_operator_statistics", "total_num_accepted_neighbors", b"total_num_accepted_neighbors", "total_num_filtered_neighbors", b"total_num_filtered_neighbors", "total_num_neighbors", b"total_num_neighbors"]) -> None: ...

global___LocalSearchStatistics = LocalSearchStatistics

@typing.final
class ConstraintSolverStatistics(google.protobuf.message.Message):
    """Statistics on the search in the constraint solver."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NUM_BRANCHES_FIELD_NUMBER: builtins.int
    NUM_FAILURES_FIELD_NUMBER: builtins.int
    NUM_SOLUTIONS_FIELD_NUMBER: builtins.int
    BYTES_USED_FIELD_NUMBER: builtins.int
    DURATION_SECONDS_FIELD_NUMBER: builtins.int
    num_branches: builtins.int
    """Number of branches explored."""
    num_failures: builtins.int
    """Number of failures/backtracks."""
    num_solutions: builtins.int
    """Number of solutions found."""
    bytes_used: builtins.int
    """Memory usage of the solver."""
    duration_seconds: builtins.float
    """Total time spent in the solver."""
    def __init__(
        self,
        *,
        num_branches: builtins.int = ...,
        num_failures: builtins.int = ...,
        num_solutions: builtins.int = ...,
        bytes_used: builtins.int = ...,
        duration_seconds: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["bytes_used", b"bytes_used", "duration_seconds", b"duration_seconds", "num_branches", b"num_branches", "num_failures", b"num_failures", "num_solutions", b"num_solutions"]) -> None: ...

global___ConstraintSolverStatistics = ConstraintSolverStatistics

@typing.final
class SearchStatistics(google.protobuf.message.Message):
    """Search statistics."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOCAL_SEARCH_STATISTICS_FIELD_NUMBER: builtins.int
    CONSTRAINT_SOLVER_STATISTICS_FIELD_NUMBER: builtins.int
    @property
    def local_search_statistics(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___LocalSearchStatistics]:
        """Local search statistics for each solver context."""

    @property
    def constraint_solver_statistics(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___ConstraintSolverStatistics]:
        """Constraint solver statistics."""

    def __init__(
        self,
        *,
        local_search_statistics: collections.abc.Iterable[global___LocalSearchStatistics] | None = ...,
        constraint_solver_statistics: collections.abc.Iterable[global___ConstraintSolverStatistics] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["constraint_solver_statistics", b"constraint_solver_statistics", "local_search_statistics", b"local_search_statistics"]) -> None: ...

global___SearchStatistics = SearchStatistics
