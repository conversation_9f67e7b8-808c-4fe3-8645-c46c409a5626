# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/set_cover/capacity.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/set_cover/capacity.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n ortools/set_cover/capacity.proto\x12\x13operations_research\"\xd4\x02\n\x17\x43\x61pacityConstraintProto\x12P\n\rcapacity_term\x18\x01 \x03(\x0b\x32\x39.operations_research.CapacityConstraintProto.CapacityTerm\x12\x14\n\x0cmin_capacity\x18\x02 \x01(\x03\x12\x14\n\x0cmax_capacity\x18\x03 \x01(\x03\x1a\xba\x01\n\x0c\x43\x61pacityTerm\x12\x0e\n\x06subset\x18\x01 \x01(\x03\x12\x64\n\x0f\x65lement_weights\x18\x02 \x03(\x0b\x32K.operations_research.CapacityConstraintProto.CapacityTerm.ElementWeightPair\x1a\x34\n\x11\x45lementWeightPair\x12\x0f\n\x07\x65lement\x18\x01 \x01(\x03\x12\x0e\n\x06weight\x18\x02 \x01(\x03\x42\x1f\n\x1b\x63om.google.ortools.setcoverP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.set_cover.capacity_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\033com.google.ortools.setcoverP\001'
  _globals['_CAPACITYCONSTRAINTPROTO']._serialized_start=58
  _globals['_CAPACITYCONSTRAINTPROTO']._serialized_end=398
  _globals['_CAPACITYCONSTRAINTPROTO_CAPACITYTERM']._serialized_start=212
  _globals['_CAPACITYCONSTRAINTPROTO_CAPACITYTERM']._serialized_end=398
  _globals['_CAPACITYCONSTRAINTPROTO_CAPACITYTERM_ELEMENTWEIGHTPAIR']._serialized_start=346
  _globals['_CAPACITYCONSTRAINTPROTO_CAPACITYTERM_ELEMENTWEIGHTPAIR']._serialized_end=398
# @@protoc_insertion_point(module_scope)
