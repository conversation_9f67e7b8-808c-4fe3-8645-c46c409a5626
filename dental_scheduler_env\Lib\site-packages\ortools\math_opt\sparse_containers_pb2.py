# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/sparse_containers.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/sparse_containers.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(ortools/math_opt/sparse_containers.proto\x12\x1coperations_research.math_opt\"6\n\x17SparseDoubleVectorProto\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\x0e\n\x06values\x18\x02 \x03(\x01\"4\n\x15SparseBoolVectorProto\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\x0e\n\x06values\x18\x02 \x03(\x08\"5\n\x16SparseInt32VectorProto\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\x0e\n\x06values\x18\x02 \x03(\x05\"`\n\x17SparseVectorFilterProto\x12\x18\n\x10skip_zero_values\x18\x01 \x01(\x08\x12\x15\n\rfilter_by_ids\x18\x02 \x01(\x08\x12\x14\n\x0c\x66iltered_ids\x18\x03 \x03(\x03\"T\n\x17SparseDoubleMatrixProto\x12\x0f\n\x07row_ids\x18\x01 \x03(\x03\x12\x12\n\ncolumn_ids\x18\x02 \x03(\x03\x12\x14\n\x0c\x63oefficients\x18\x03 \x03(\x01\"J\n\x15LinearExpressionProto\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\x14\n\x0c\x63oefficients\x18\x02 \x03(\x01\x12\x0e\n\x06offset\x18\x03 \x01(\x01\x42\x1e\n\x1a\x63om.google.ortools.mathoptP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.sparse_containers_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.ortools.mathoptP\001'
  _globals['_SPARSEDOUBLEVECTORPROTO']._serialized_start=74
  _globals['_SPARSEDOUBLEVECTORPROTO']._serialized_end=128
  _globals['_SPARSEBOOLVECTORPROTO']._serialized_start=130
  _globals['_SPARSEBOOLVECTORPROTO']._serialized_end=182
  _globals['_SPARSEINT32VECTORPROTO']._serialized_start=184
  _globals['_SPARSEINT32VECTORPROTO']._serialized_end=237
  _globals['_SPARSEVECTORFILTERPROTO']._serialized_start=239
  _globals['_SPARSEVECTORFILTERPROTO']._serialized_end=335
  _globals['_SPARSEDOUBLEMATRIXPROTO']._serialized_start=337
  _globals['_SPARSEDOUBLEMATRIXPROTO']._serialized_end=421
  _globals['_LINEAREXPRESSIONPROTO']._serialized_start=423
  _globals['_LINEAREXPRESSIONPROTO']._serialized_end=497
# @@protoc_insertion_point(module_scope)
