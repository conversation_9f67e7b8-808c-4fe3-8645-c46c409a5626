# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/rpc.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/rpc.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.math_opt import callback_pb2 as ortools_dot_math__opt_dot_callback__pb2
from ortools.math_opt import infeasible_subsystem_pb2 as ortools_dot_math__opt_dot_infeasible__subsystem__pb2
from ortools.math_opt import model_pb2 as ortools_dot_math__opt_dot_model__pb2
from ortools.math_opt import model_parameters_pb2 as ortools_dot_math__opt_dot_model__parameters__pb2
from ortools.math_opt import model_update_pb2 as ortools_dot_math__opt_dot_model__update__pb2
from ortools.math_opt import parameters_pb2 as ortools_dot_math__opt_dot_parameters__pb2
from ortools.math_opt import result_pb2 as ortools_dot_math__opt_dot_result__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1aortools/math_opt/rpc.proto\x12\x1coperations_research.math_opt\x1a\x1fortools/math_opt/callback.proto\x1a+ortools/math_opt/infeasible_subsystem.proto\x1a\x1cortools/math_opt/model.proto\x1a\'ortools/math_opt/model_parameters.proto\x1a#ortools/math_opt/model_update.proto\x1a!ortools/math_opt/parameters.proto\x1a\x1dortools/math_opt/result.proto\"J\n\x14SolverResourcesProto\x12\x10\n\x03\x63pu\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x10\n\x03ram\x18\x02 \x01(\x01H\x01\x88\x01\x01\x42\x06\n\x04_cpuB\x06\n\x04_ram\"\xb8\x03\n\x0cSolveRequest\x12\x42\n\x0bsolver_type\x18\x01 \x01(\x0e\x32-.operations_research.math_opt.SolverTypeProto\x12\x37\n\x05model\x18\x02 \x01(\x0b\x32(.operations_research.math_opt.ModelProto\x12\x45\n\tresources\x18\x06 \x01(\x0b\x32\x32.operations_research.math_opt.SolverResourcesProto\x12I\n\x0binitializer\x18\x03 \x01(\x0b\x32\x34.operations_research.math_opt.SolverInitializerProto\x12\x46\n\nparameters\x18\x04 \x01(\x0b\x32\x32.operations_research.math_opt.SolveParametersProto\x12Q\n\x10model_parameters\x18\x05 \x01(\x0b\x32\x37.operations_research.math_opt.ModelSolveParametersProto\"\xad\x01\n\rSolveResponse\x12@\n\x06result\x18\x01 \x01(\x0b\x32..operations_research.math_opt.SolveResultProtoH\x00\x12;\n\x06status\x18\x03 \x01(\x0b\x32).operations_research.math_opt.StatusProtoH\x00\x12\x10\n\x08messages\x18\x02 \x03(\tB\x0b\n\tstatus_or\",\n\x0bStatusProto\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\tB\x1e\n\x1a\x63om.google.ortools.mathoptP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.rpc_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.ortools.mathoptP\001'
  _globals['_SOLVERRESOURCESPROTO']._serialized_start=312
  _globals['_SOLVERRESOURCESPROTO']._serialized_end=386
  _globals['_SOLVEREQUEST']._serialized_start=389
  _globals['_SOLVEREQUEST']._serialized_end=829
  _globals['_SOLVERESPONSE']._serialized_start=832
  _globals['_SOLVERESPONSE']._serialized_end=1005
  _globals['_STATUSPROTO']._serialized_start=1007
  _globals['_STATUSPROTO']._serialized_end=1051
# @@protoc_insertion_point(module_scope)
