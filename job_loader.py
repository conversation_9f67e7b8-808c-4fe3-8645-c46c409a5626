"""
Job Loader - Load jobs from Excel template and integrate with scheduling system
"""
import pandas as pd
from datetime import datetime, timedelta
import sys
import os

def load_jobs_from_excel(file_path="Job_Upload_Template.xlsx"):
    """
    Load jobs from Excel template and convert to format used by scheduling system
    
    Args:
        file_path (str): Path to the Excel file
        
    Returns:
        list: Jobs data in the format expected by the scheduling system
    """
    
    try:
        # Read the Jobs sheet from Excel
        df = pd.read_excel(file_path, sheet_name='Jobs')
        
        # Validate required columns
        required_columns = [
            'Job_ID', 'Job_Name', 'Job_Type', 'Description', 'Patient_ID',
            'Received_Date', 'Due_Date', 'Priority', 'Step_Sequence',
            'Red_Team_Hours', 'Red_Team_Description', 'Red_Team_Step_Order',
            'Blue_Team_Hours', 'Blue_Team_Description', 'Blue_Team_Step_Order',
            'Green_Team_Hours', 'Green_Team_Description', 'Green_Team_Step_Order'
        ]
        
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Convert to scheduling system format
        jobs_data = []
        
        for idx, row in df.iterrows():
            # Skip empty rows
            if pd.isna(row['Job_ID']) or row['Job_ID'] == '':
                continue
                
            # Parse dates
            try:
                if isinstance(row['Received_Date'], str):
                    received_date = datetime.strptime(row['Received_Date'], '%Y-%m-%d').date()
                else:
                    received_date = row['Received_Date'].date()
                    
                if isinstance(row['Due_Date'], str):
                    due_date = datetime.strptime(row['Due_Date'], '%Y-%m-%d').date()
                else:
                    due_date = row['Due_Date'].date()
            except ValueError as e:
                print(f"Error parsing dates for Job {row['Job_ID']}: {e}")
                continue
            
            # Validate team hours and step orders
            try:
                red_hours = int(row['Red_Team_Hours'])
                blue_hours = int(row['Blue_Team_Hours'])
                green_hours = int(row['Green_Team_Hours'])

                red_order = int(row['Red_Team_Step_Order'])
                blue_order = int(row['Blue_Team_Step_Order'])
                green_order = int(row['Green_Team_Step_Order'])

                if not (1 <= red_hours <= 8 and 1 <= blue_hours <= 8 and 1 <= green_hours <= 8):
                    print(f"Warning: Team hours for Job {row['Job_ID']} should be between 1-8")

                if not (1 <= red_order <= 3 and 1 <= blue_order <= 3 and 1 <= green_order <= 3):
                    print(f"Warning: Step orders for Job {row['Job_ID']} should be between 1-3")

            except (ValueError, TypeError):
                print(f"Error: Invalid team hours or step orders for Job {row['Job_ID']}")
                continue

            # Create ordered task list based on step sequence
            team_data = [
                (red_order, 0, red_hours, row['Red_Team_Description']),    # (order, team_id, hours, description)
                (blue_order, 1, blue_hours, row['Blue_Team_Description']),
                (green_order, 2, green_hours, row['Green_Team_Description'])
            ]

            # Sort by step order to ensure correct sequence
            team_data.sort(key=lambda x: x[0])

            # Create tasks in correct order
            tasks = [(team_id, hours) for _, team_id, hours, _ in team_data]
            task_descriptions = [desc for _, _, _, desc in team_data]

            # Create job data structure
            job_data = {
                'job_id': row['Job_ID'],
                'job_name': row['Job_Name'],
                'job_type': row['Job_Type'],
                'description': row['Description'],
                'patient_id': row['Patient_ID'],
                'priority': row['Priority'],
                'step_sequence': row['Step_Sequence'],
                'tasks': tasks,
                'task_descriptions': task_descriptions,
                'received_date': received_date,
                'completion_date': due_date
            }
            
            jobs_data.append(job_data)
            
        print(f"Successfully loaded {len(jobs_data)} jobs from Excel file")
        return jobs_data
        
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found")
        return []
    except Exception as e:
        print(f"Error loading jobs from Excel: {e}")
        return []

def validate_jobs_data(jobs_data):
    """
    Validate the loaded jobs data
    
    Args:
        jobs_data (list): List of job dictionaries
        
    Returns:
        bool: True if all jobs are valid, False otherwise
    """
    
    if not jobs_data:
        print("No jobs to validate")
        return False
    
    job_ids = set()
    valid_jobs = 0
    
    for job in jobs_data:
        # Check for duplicate job IDs
        if job['job_id'] in job_ids:
            print(f"Error: Duplicate Job ID '{job['job_id']}'")
            continue
        job_ids.add(job['job_id'])
        
        # Check date logic
        if job['completion_date'] <= job['received_date']:
            print(f"Warning: Job {job['job_id']} due date is not after received date")
        
        # Check if due date is reasonable (not too far in the future)
        days_to_complete = (job['completion_date'] - job['received_date']).days
        if days_to_complete > 30:
            print(f"Warning: Job {job['job_id']} has {days_to_complete} days to complete")
        
        valid_jobs += 1
    
    print(f"Validated {valid_jobs} jobs successfully")
    return valid_jobs > 0

def export_jobs_summary(jobs_data, output_file="jobs_summary.txt"):
    """
    Export a summary of loaded jobs to a text file
    
    Args:
        jobs_data (list): List of job dictionaries
        output_file (str): Output file path
    """
    
    if not jobs_data:
        print("No jobs to export")
        return
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("JOB SUMMARY REPORT\n")
        f.write("=" * 50 + "\n\n")
        
        # Overall statistics
        total_jobs = len(jobs_data)
        total_red_hours = sum(job['tasks'][0][1] for job in jobs_data)
        total_blue_hours = sum(job['tasks'][1][1] for job in jobs_data)
        total_green_hours = sum(job['tasks'][2][1] for job in jobs_data)
        total_hours = total_red_hours + total_blue_hours + total_green_hours
        
        f.write(f"Total Jobs: {total_jobs}\n")
        f.write(f"Total Work Hours: {total_hours}\n")
        f.write(f"Red Team Hours: {total_red_hours}\n")
        f.write(f"Blue Team Hours: {total_blue_hours}\n")
        f.write(f"Green Team Hours: {total_green_hours}\n\n")
        
        # Priority breakdown
        priority_counts = {}
        job_type_counts = {}
        for job in jobs_data:
            priority = job.get('priority', 'Unknown')
            job_type = job.get('job_type', 'Unknown')
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
            job_type_counts[job_type] = job_type_counts.get(job_type, 0) + 1

        f.write("Priority Breakdown:\n")
        for priority, count in priority_counts.items():
            f.write(f"  {priority}: {count} jobs\n")
        f.write("\n")

        f.write("Job Type Breakdown:\n")
        for job_type, count in job_type_counts.items():
            f.write(f"  {job_type}: {count} jobs\n")
        f.write("\n")
        
        # Individual job details
        f.write("JOB DETAILS:\n")
        f.write("-" * 50 + "\n")
        
        for job in jobs_data:
            f.write(f"Job ID: {job['job_id']}\n")
            f.write(f"Name: {job['job_name']}\n")
            f.write(f"Type: {job.get('job_type', 'Unknown')}\n")
            f.write(f"Patient: {job.get('patient_id', 'Unknown')}\n")
            f.write(f"Priority: {job.get('priority', 'Unknown')}\n")
            f.write(f"Received: {job['received_date']}\n")
            f.write(f"Due: {job['completion_date']}\n")
            f.write(f"Step Sequence: {job.get('step_sequence', 'Unknown')}\n")

            # Write task details in order
            team_names = ['Red', 'Blue', 'Green']
            for i, (team_id, hours) in enumerate(job['tasks']):
                step_num = i + 1
                team_name = team_names[team_id]
                description = job.get('task_descriptions', [''] * len(job['tasks']))[i]
                f.write(f"Step {step_num} - {team_name} Team: {hours}h - {description}\n")

            f.write(f"Description: {job['description']}\n")
            f.write("-" * 50 + "\n")
    
    print(f"Jobs summary exported to '{output_file}'")

def main():
    """Main function to demonstrate job loading"""
    
    # Check if Excel file exists
    excel_file = "Job_Upload_Template.xlsx"
    if not os.path.exists(excel_file):
        print(f"Excel template '{excel_file}' not found.")
        print("Please run 'python create_job_template.py' first to create the template.")
        return
    
    # Load jobs from Excel
    print("Loading jobs from Excel template...")
    jobs_data = load_jobs_from_excel(excel_file)
    
    if not jobs_data:
        print("No jobs loaded. Please check the Excel file.")
        return
    
    # Validate jobs
    print("\nValidating jobs data...")
    if not validate_jobs_data(jobs_data):
        print("Job validation failed.")
        return
    
    # Export summary
    print("\nExporting jobs summary...")
    export_jobs_summary(jobs_data)
    
    # Display sample of loaded data
    print(f"\nSample of loaded jobs:")
    for i, job in enumerate(jobs_data[:3]):  # Show first 3 jobs
        print(f"  {i+1}. {job['job_id']}: {job['job_name']} "
              f"(Due: {job['completion_date']}, "
              f"Total hours: {sum(task[1] for task in job['tasks'])})")
    
    if len(jobs_data) > 3:
        print(f"  ... and {len(jobs_data) - 3} more jobs")
    
    print(f"\nJobs are ready to be used in the scheduling system!")
    print("You can now integrate this data with Main.py")

if __name__ == "__main__":
    main()
