"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Vector Bin Packing Problem.

The problem description is as follows:

Given:
  - a fixed number of resources,
  - a set of equivalent multidimentional bins with max capacity on each
    resource.
  - a set of items, with fixed usage for all resources, and a number of
    copies for each item.

The goal is either:
- optimization: minimizing a weighted sum of the number of bins used, plus a
                weighted sum of skipped items; or
- feasibility: checking if all required items can be packed using at most a
  given number of bins.

In both cases we must ensure that for each bin and each resource, the sum of
sizes of each assigned item is less than the capacity of the resource of the
bin.

An optional integer imposes an upper bound on how many copies of the same
item are allowed in a single bin, regardless of resource utilization.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _VectorBinPackingSolveStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _VectorBinPackingSolveStatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_VectorBinPackingSolveStatus.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    VECTOR_BIN_PACKING_SOLVE_STATUS_UNSPECIFIED: _VectorBinPackingSolveStatus.ValueType  # 0
    """Default state."""
    OPTIMAL: _VectorBinPackingSolveStatus.ValueType  # 1
    """The optimal solution was found and proven."""
    FEASIBLE: _VectorBinPackingSolveStatus.ValueType  # 2
    """A feasible solution has been found."""
    INFEASIBLE: _VectorBinPackingSolveStatus.ValueType  # 3
    """The problem is infeasible."""

class VectorBinPackingSolveStatus(_VectorBinPackingSolveStatus, metaclass=_VectorBinPackingSolveStatusEnumTypeWrapper):
    """Solve status"""

VECTOR_BIN_PACKING_SOLVE_STATUS_UNSPECIFIED: VectorBinPackingSolveStatus.ValueType  # 0
"""Default state."""
OPTIMAL: VectorBinPackingSolveStatus.ValueType  # 1
"""The optimal solution was found and proven."""
FEASIBLE: VectorBinPackingSolveStatus.ValueType  # 2
"""A feasible solution has been found."""
INFEASIBLE: VectorBinPackingSolveStatus.ValueType  # 3
"""The problem is infeasible."""
global___VectorBinPackingSolveStatus = VectorBinPackingSolveStatus

@typing.final
class Item(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    RESOURCE_USAGE_FIELD_NUMBER: builtins.int
    NUM_COPIES_FIELD_NUMBER: builtins.int
    NUM_OPTIONAL_COPIES_FIELD_NUMBER: builtins.int
    MAX_NUMBER_OF_COPIES_PER_BIN_FIELD_NUMBER: builtins.int
    PENALTY_PER_MISSING_COPY_FIELD_NUMBER: builtins.int
    name: builtins.str
    """Optional name. This is only used for display/debugging purposes."""
    num_copies: builtins.int
    """Number of identical copies of this item that must be packed into some bin."""
    num_optional_copies: builtins.int
    """The number of extra copies which may be skipped for a penalty.
    Currently only supported by the ArcFlow solver (arc_flow_solver.h), other
    solvers ignore this field.
    """
    max_number_of_copies_per_bin: builtins.int
    """An optional upper bound on how many copies of the same item are allowed in
    a single bin, regardless of resource utilization. A value of 0 is
    interpreted as no limit.
    """
    penalty_per_missing_copy: builtins.float
    """Minimize the total cost of bins plus this penalty for each optional copy
    not placed in any bin.
    Currently only supported by the ArcFlow solver (arc_flow_solver.h), other
    solvers ignore this field.
    """
    @property
    def resource_usage(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Resource usages for this item. All usages must be non-negative.
        Should be the same size as resource_capacity in the
        VectorBinPackingProblem.
        """

    def __init__(
        self,
        *,
        name: builtins.str = ...,
        resource_usage: collections.abc.Iterable[builtins.int] | None = ...,
        num_copies: builtins.int = ...,
        num_optional_copies: builtins.int = ...,
        max_number_of_copies_per_bin: builtins.int = ...,
        penalty_per_missing_copy: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["max_number_of_copies_per_bin", b"max_number_of_copies_per_bin", "name", b"name", "num_copies", b"num_copies", "num_optional_copies", b"num_optional_copies", "penalty_per_missing_copy", b"penalty_per_missing_copy", "resource_usage", b"resource_usage"]) -> None: ...

global___Item = Item

@typing.final
class VectorBinPackingProblem(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    RESOURCE_CAPACITY_FIELD_NUMBER: builtins.int
    RESOURCE_NAME_FIELD_NUMBER: builtins.int
    ITEM_FIELD_NUMBER: builtins.int
    MAX_BINS_FIELD_NUMBER: builtins.int
    COST_PER_BIN_FIELD_NUMBER: builtins.int
    name: builtins.str
    """Optional name."""
    max_bins: builtins.int
    """The maximum number of bins available. A value of 0 is interpreted as no
    limit. Nonzero values may be used to encode feasibility problems.
    """
    cost_per_bin: builtins.float
    """If specified, tries to maximize the value of packed items minus the cost
    per bin used. A missing value is treated as 1.
    Currently only supported by the ArcFlow solver
    (ortools/packing/arc_flow_solver.h), other solvers
    ignore this field.
    """
    @property
    def resource_capacity(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Max capacity of each resource.
        All bins have the same resource capacities.
        """

    @property
    def resource_name(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """Resources names. This can either be left empty or
        must be of the same size as resource_capacity.
        """

    @property
    def item(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Item]:
        """The list of items which are to be assigned to bins."""

    def __init__(
        self,
        *,
        name: builtins.str = ...,
        resource_capacity: collections.abc.Iterable[builtins.int] | None = ...,
        resource_name: collections.abc.Iterable[builtins.str] | None = ...,
        item: collections.abc.Iterable[global___Item] | None = ...,
        max_bins: builtins.int = ...,
        cost_per_bin: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_cost_per_bin", b"_cost_per_bin", "cost_per_bin", b"cost_per_bin"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_cost_per_bin", b"_cost_per_bin", "cost_per_bin", b"cost_per_bin", "item", b"item", "max_bins", b"max_bins", "name", b"name", "resource_capacity", b"resource_capacity", "resource_name", b"resource_name"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_cost_per_bin", b"_cost_per_bin"]) -> typing.Literal["cost_per_bin"] | None: ...

global___VectorBinPackingProblem = VectorBinPackingProblem

@typing.final
class VectorBinPackingOneBinInSolution(google.protobuf.message.Message):
    """Describe one filled bin in the solution."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ITEM_INDICES_FIELD_NUMBER: builtins.int
    ITEM_COPIES_FIELD_NUMBER: builtins.int
    @property
    def item_indices(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Which items are in this bin. They are supposed to be unique."""

    @property
    def item_copies(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """How many of each items are in this bins."""

    def __init__(
        self,
        *,
        item_indices: collections.abc.Iterable[builtins.int] | None = ...,
        item_copies: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["item_copies", b"item_copies", "item_indices", b"item_indices"]) -> None: ...

global___VectorBinPackingOneBinInSolution = VectorBinPackingOneBinInSolution

@typing.final
class VectorBinPackingSolution(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SOLVER_INFO_FIELD_NUMBER: builtins.int
    BINS_FIELD_NUMBER: builtins.int
    STATUS_FIELD_NUMBER: builtins.int
    OBJECTIVE_VALUE_FIELD_NUMBER: builtins.int
    SOLVE_TIME_IN_SECONDS_FIELD_NUMBER: builtins.int
    ARC_FLOW_TIME_IN_SECONDS_FIELD_NUMBER: builtins.int
    solver_info: builtins.str
    """Optional info from the solver."""
    status: global___VectorBinPackingSolveStatus.ValueType
    """Solve status."""
    objective_value: builtins.float
    """Objective value.
    The total cost of bins used plus the penalty for any skipped items.
    """
    solve_time_in_seconds: builtins.float
    """Solve time in seconds."""
    arc_flow_time_in_seconds: builtins.float
    """Time to create the Arc-Flow graph."""
    @property
    def bins(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___VectorBinPackingOneBinInSolution]:
        """Filled bins."""

    def __init__(
        self,
        *,
        solver_info: builtins.str = ...,
        bins: collections.abc.Iterable[global___VectorBinPackingOneBinInSolution] | None = ...,
        status: global___VectorBinPackingSolveStatus.ValueType = ...,
        objective_value: builtins.float = ...,
        solve_time_in_seconds: builtins.float = ...,
        arc_flow_time_in_seconds: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["arc_flow_time_in_seconds", b"arc_flow_time_in_seconds", "bins", b"bins", "objective_value", b"objective_value", "solve_time_in_seconds", b"solve_time_in_seconds", "solver_info", b"solver_info", "status", b"status"]) -> None: ...

global___VectorBinPackingSolution = VectorBinPackingSolution
