# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/infeasible_subsystem.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/infeasible_subsystem.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.math_opt import result_pb2 as ortools_dot_math__opt_dot_result__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+ortools/math_opt/infeasible_subsystem.proto\x12\x1coperations_research.math_opt\x1a\x1dortools/math_opt/result.proto\"\xce\x06\n\x10ModelSubsetProto\x12[\n\x0fvariable_bounds\x18\x01 \x03(\x0b\x32\x42.operations_research.math_opt.ModelSubsetProto.VariableBoundsEntry\x12\x1c\n\x14variable_integrality\x18\x02 \x03(\x03\x12\x61\n\x12linear_constraints\x18\x03 \x03(\x0b\x32\x45.operations_research.math_opt.ModelSubsetProto.LinearConstraintsEntry\x12g\n\x15quadratic_constraints\x18\x04 \x03(\x0b\x32H.operations_research.math_opt.ModelSubsetProto.QuadraticConstraintsEntry\x12%\n\x1dsecond_order_cone_constraints\x18\x05 \x03(\x03\x12\x18\n\x10sos1_constraints\x18\x06 \x03(\x03\x12\x18\n\x10sos2_constraints\x18\x07 \x03(\x03\x12\x1d\n\x15indicator_constraints\x18\x08 \x03(\x03\x1a&\n\x06\x42ounds\x12\r\n\x05lower\x18\x01 \x01(\x08\x12\r\n\x05upper\x18\x02 \x01(\x08\x1al\n\x13VariableBoundsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x44\n\x05value\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.ModelSubsetProto.Bounds:\x02\x38\x01\x1ao\n\x16LinearConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x44\n\x05value\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.ModelSubsetProto.Bounds:\x02\x38\x01\x1ar\n\x19QuadraticConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x44\n\x05value\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.ModelSubsetProto.Bounds:\x02\x38\x01\"\xd4\x01\n%ComputeInfeasibleSubsystemResultProto\x12I\n\x0b\x66\x65\x61sibility\x18\x01 \x01(\x0e\x32\x34.operations_research.math_opt.FeasibilityStatusProto\x12L\n\x14infeasible_subsystem\x18\x02 \x01(\x0b\x32..operations_research.math_opt.ModelSubsetProto\x12\x12\n\nis_minimal\x18\x03 \x01(\x08\x42\x1e\n\x1a\x63om.google.ortools.mathoptP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.infeasible_subsystem_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.ortools.mathoptP\001'
  _globals['_MODELSUBSETPROTO_VARIABLEBOUNDSENTRY']._loaded_options = None
  _globals['_MODELSUBSETPROTO_VARIABLEBOUNDSENTRY']._serialized_options = b'8\001'
  _globals['_MODELSUBSETPROTO_LINEARCONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELSUBSETPROTO_LINEARCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELSUBSETPROTO_QUADRATICCONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELSUBSETPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELSUBSETPROTO']._serialized_start=109
  _globals['_MODELSUBSETPROTO']._serialized_end=955
  _globals['_MODELSUBSETPROTO_BOUNDS']._serialized_start=578
  _globals['_MODELSUBSETPROTO_BOUNDS']._serialized_end=616
  _globals['_MODELSUBSETPROTO_VARIABLEBOUNDSENTRY']._serialized_start=618
  _globals['_MODELSUBSETPROTO_VARIABLEBOUNDSENTRY']._serialized_end=726
  _globals['_MODELSUBSETPROTO_LINEARCONSTRAINTSENTRY']._serialized_start=728
  _globals['_MODELSUBSETPROTO_LINEARCONSTRAINTSENTRY']._serialized_end=839
  _globals['_MODELSUBSETPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_start=841
  _globals['_MODELSUBSETPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_end=955
  _globals['_COMPUTEINFEASIBLESUBSYSTEMRESULTPROTO']._serialized_start=958
  _globals['_COMPUTEINFEASIBLESUBSYSTEMRESULTPROTO']._serialized_end=1170
# @@protoc_insertion_point(module_scope)
