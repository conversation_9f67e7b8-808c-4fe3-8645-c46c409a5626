; Inno Setup Script for Dental Implant Manufacturing Scheduler
; This creates a professional Windows installer

#define MyAppName "Dental Implant Manufacturing Scheduler"
#define MyAppVersion "1.0.0"
#define MyAppPublisher "Dental Manufacturing Solutions"
#define MyAppURL "https://github.com/dental-manufacturing/implant-scheduler"
#define MyAppExeName "DentalSchedulerGUI.exe"
#define MyAppCLIName "DentalSchedulerCLI.exe"
#define MyAppIcon "icon.ico"

[Setup]
; NOTE: The value of AppId uniquely identifies this application.
; Do not use the same AppId value in installers for other applications.
AppId={{8A7B9C2D-3E4F-5A6B-7C8D-9E0F1A2B3C4D}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\DentalScheduler
DefaultGroupName={#MyAppName}
AllowNoIcons=yes
LicenseFile=LICENSE.txt
InfoBeforeFile=INSTALLATION_GUIDE.md
InfoAfterFile=README.md
OutputDir=installer_output
OutputBaseFilename=DentalSchedulerSetup
SetupIconFile={#MyAppIcon}
Compression=lzma2/ultra64
SolidCompression=yes
WizardStyle=modern
WizardSizePercent=120
DisableProgramGroupPage=yes
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode
Name: "associate"; Description: "Associate .xlsx files with Dental Scheduler"; GroupDescription: "File associations:"; Flags: unchecked

[Files]
; Main application files from PyInstaller output
Source: "dist\DentalScheduler\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; Documentation files
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "PROJECT_STRUCTURE.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "INSTALLATION_GUIDE.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "requirements.txt"; DestDir: "{app}"; Flags: ignoreversion
; Sample template
Source: "Job_Upload_Template.xlsx"; DestDir: "{app}"; Flags: ignoreversion
; Icon file
Source: "icon.ico"; DestDir: "{app}"; Flags: ignoreversion
; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Icons]
; Start Menu icons
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\{#MyAppIcon}"
Name: "{group}\{#MyAppName} (Command Line)"; Filename: "{app}\{#MyAppCLIName}"; IconFilename: "{app}\{#MyAppIcon}"
Name: "{group}\Template Creator"; Filename: "{app}\TemplateCreator.exe"; IconFilename: "{app}\{#MyAppIcon}"
Name: "{group}\User Guide"; Filename: "{app}\README.md"
Name: "{group}\Sample Jobs"; Filename: "{app}\Job_Upload_Template.xlsx"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"

; Desktop icons (optional)
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\{#MyAppIcon}"; Tasks: desktopicon
Name: "{autodesktop}\{#MyAppName} CLI"; Filename: "{app}\{#MyAppCLIName}"; IconFilename: "{app}\{#MyAppIcon}"; Tasks: desktopicon

; Quick Launch icons (optional)
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; IconFilename: "{app}\{#MyAppIcon}"; Tasks: quicklaunchicon

[Registry]
; File association for .xlsx files (optional)
Root: HKCR; Subkey: ".xlsx\OpenWithProgids"; ValueType: string; ValueName: "DentalScheduler.xlsx"; ValueData: ""; Flags: uninsdeletevalue; Tasks: associate
Root: HKCR; Subkey: "DentalScheduler.xlsx"; ValueType: string; ValueName: ""; ValueData: "Dental Scheduler Job File"; Flags: uninsdeletekey; Tasks: associate
Root: HKCR; Subkey: "DentalScheduler.xlsx\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppIcon}"; Tasks: associate
Root: HKCR; Subkey: "DentalScheduler.xlsx\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""; Tasks: associate

[Run]
; Run the application after installation (optional)
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; Clean up any files created by the application
Type: filesandordirs; Name: "{app}\*.html"
Type: filesandordirs; Name: "{app}\jobs_summary.txt"
Type: filesandordirs; Name: "{app}\*.log"

[Code]
// Custom installation logic

function GetUninstallString(): String;
var
  sUnInstPath: String;
  sUnInstallString: String;
begin
  sUnInstPath := ExpandConstant('Software\Microsoft\Windows\CurrentVersion\Uninstall\{#emit SetupSetting("AppId")}_is1');
  sUnInstallString := '';
  if not RegQueryStringValue(HKLM, sUnInstPath, 'UninstallString', sUnInstallString) then
    RegQueryStringValue(HKCU, sUnInstPath, 'UninstallString', sUnInstallString);
  Result := sUnInstallString;
end;

function IsUpgrade(): Boolean;
begin
  Result := (GetUninstallString() <> '');
end;

function UnInstallOldVersion(): Integer;
var
  sUnInstallString: String;
  iResultCode: Integer;
begin
  // Return Values:
  // 1 - uninstall string is empty
  // 2 - error executing the UnInstallString
  // 3 - successfully executed the UnInstallString

  // default return value
  Result := 0;

  // get the uninstall string of the old app
  sUnInstallString := GetUninstallString();
  if sUnInstallString <> '' then begin
    sUnInstallString := RemoveQuotes(sUnInstallString);
    if Exec(sUnInstallString, '/SILENT /NORESTART /SUPPRESSMSGBOXES','', SW_HIDE, ewWaitUntilTerminated, iResultCode) then
      Result := 3
    else
      Result := 2;
  end else
    Result := 1;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if (CurStep=ssInstall) then
  begin
    if (IsUpgrade()) then
    begin
      UnInstallOldVersion();
    end;
  end;
end;

// Check for prerequisites
function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // Check Windows version (Windows 10 or later recommended)
  if Version.Major < 10 then
  begin
    if MsgBox('This application is designed for Windows 10 or later. ' +
              'It may not work properly on your version of Windows. ' +
              'Do you want to continue anyway?', 
              mbConfirmation, MB_YESNO) = IDNO then
    begin
      Result := False;
      Exit;
    end;
  end;
  
  Result := True;
end;

// Post-installation message
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // Create a sample job file if it doesn't exist
    if not FileExists(ExpandConstant('{app}\Job_Upload_Template.xlsx')) then
    begin
      // The template should be included in the installation files
    end;
  end;
end;
