"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class IntVarAssignment(google.protobuf.message.Message):
    """Storage for IntVars."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_ID_FIELD_NUMBER: builtins.int
    MIN_FIELD_NUMBER: builtins.int
    MAX_FIELD_NUMBER: builtins.int
    ACTIVE_FIELD_NUMBER: builtins.int
    var_id: builtins.str
    min: builtins.int
    max: builtins.int
    active: builtins.bool
    def __init__(
        self,
        *,
        var_id: builtins.str = ...,
        min: builtins.int = ...,
        max: builtins.int = ...,
        active: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["active", b"active", "max", b"max", "min", b"min", "var_id", b"var_id"]) -> None: ...

global___IntVarAssignment = IntVarAssignment

@typing.final
class IntervalVarAssignment(google.protobuf.message.Message):
    """Storage for IntervalVars."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_ID_FIELD_NUMBER: builtins.int
    START_MIN_FIELD_NUMBER: builtins.int
    START_MAX_FIELD_NUMBER: builtins.int
    DURATION_MIN_FIELD_NUMBER: builtins.int
    DURATION_MAX_FIELD_NUMBER: builtins.int
    END_MIN_FIELD_NUMBER: builtins.int
    END_MAX_FIELD_NUMBER: builtins.int
    PERFORMED_MIN_FIELD_NUMBER: builtins.int
    PERFORMED_MAX_FIELD_NUMBER: builtins.int
    ACTIVE_FIELD_NUMBER: builtins.int
    var_id: builtins.str
    start_min: builtins.int
    start_max: builtins.int
    duration_min: builtins.int
    duration_max: builtins.int
    end_min: builtins.int
    end_max: builtins.int
    performed_min: builtins.int
    performed_max: builtins.int
    active: builtins.bool
    def __init__(
        self,
        *,
        var_id: builtins.str = ...,
        start_min: builtins.int = ...,
        start_max: builtins.int = ...,
        duration_min: builtins.int = ...,
        duration_max: builtins.int = ...,
        end_min: builtins.int = ...,
        end_max: builtins.int = ...,
        performed_min: builtins.int = ...,
        performed_max: builtins.int = ...,
        active: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["active", b"active", "duration_max", b"duration_max", "duration_min", b"duration_min", "end_max", b"end_max", "end_min", b"end_min", "performed_max", b"performed_max", "performed_min", b"performed_min", "start_max", b"start_max", "start_min", b"start_min", "var_id", b"var_id"]) -> None: ...

global___IntervalVarAssignment = IntervalVarAssignment

@typing.final
class SequenceVarAssignment(google.protobuf.message.Message):
    """Storage for SequenceVars."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_ID_FIELD_NUMBER: builtins.int
    FORWARD_SEQUENCE_FIELD_NUMBER: builtins.int
    BACKWARD_SEQUENCE_FIELD_NUMBER: builtins.int
    UNPERFORMED_FIELD_NUMBER: builtins.int
    ACTIVE_FIELD_NUMBER: builtins.int
    var_id: builtins.str
    active: builtins.bool
    @property
    def forward_sequence(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def backward_sequence(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def unperformed(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    def __init__(
        self,
        *,
        var_id: builtins.str = ...,
        forward_sequence: collections.abc.Iterable[builtins.int] | None = ...,
        backward_sequence: collections.abc.Iterable[builtins.int] | None = ...,
        unperformed: collections.abc.Iterable[builtins.int] | None = ...,
        active: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["active", b"active", "backward_sequence", b"backward_sequence", "forward_sequence", b"forward_sequence", "unperformed", b"unperformed", "var_id", b"var_id"]) -> None: ...

global___SequenceVarAssignment = SequenceVarAssignment

@typing.final
class WorkerInfo(google.protobuf.message.Message):
    """This message indicates how the assignment was produced."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    WORKER_ID_FIELD_NUMBER: builtins.int
    BNS_FIELD_NUMBER: builtins.int
    worker_id: builtins.int
    bns: builtins.str
    def __init__(
        self,
        *,
        worker_id: builtins.int = ...,
        bns: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["bns", b"bns", "worker_id", b"worker_id"]) -> None: ...

global___WorkerInfo = WorkerInfo

@typing.final
class AssignmentProto(google.protobuf.message.Message):
    """Global container for all assignment variables and objective"""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INT_VAR_ASSIGNMENT_FIELD_NUMBER: builtins.int
    INTERVAL_VAR_ASSIGNMENT_FIELD_NUMBER: builtins.int
    SEQUENCE_VAR_ASSIGNMENT_FIELD_NUMBER: builtins.int
    OBJECTIVE_FIELD_NUMBER: builtins.int
    WORKER_INFO_FIELD_NUMBER: builtins.int
    IS_VALID_FIELD_NUMBER: builtins.int
    is_valid: builtins.bool
    @property
    def int_var_assignment(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___IntVarAssignment]: ...
    @property
    def interval_var_assignment(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___IntervalVarAssignment]: ...
    @property
    def sequence_var_assignment(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SequenceVarAssignment]: ...
    @property
    def objective(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___IntVarAssignment]: ...
    @property
    def worker_info(self) -> global___WorkerInfo: ...
    def __init__(
        self,
        *,
        int_var_assignment: collections.abc.Iterable[global___IntVarAssignment] | None = ...,
        interval_var_assignment: collections.abc.Iterable[global___IntervalVarAssignment] | None = ...,
        sequence_var_assignment: collections.abc.Iterable[global___SequenceVarAssignment] | None = ...,
        objective: collections.abc.Iterable[global___IntVarAssignment] | None = ...,
        worker_info: global___WorkerInfo | None = ...,
        is_valid: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["worker_info", b"worker_info"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["int_var_assignment", b"int_var_assignment", "interval_var_assignment", b"interval_var_assignment", "is_valid", b"is_valid", "objective", b"objective", "sequence_var_assignment", b"sequence_var_assignment", "worker_info", b"worker_info"]) -> None: ...

global___AssignmentProto = AssignmentProto
