# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/sat/cp_model_service.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/sat/cp_model_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.sat import cp_model_pb2 as ortools_dot_sat_dot_cp__model__pb2
from ortools.sat import sat_parameters_pb2 as ortools_dot_sat_dot_sat__parameters__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"ortools/sat/cp_model_service.proto\x12\x1aoperations_research.sat.v1\x1a\x1aortools/sat/cp_model.proto\x1a ortools/sat/sat_parameters.proto\"\x89\x01\n\x0f\x43pSolverRequest\x12\x34\n\x05model\x18\x01 \x01(\x0b\x32%.operations_research.sat.CpModelProto\x12:\n\nparameters\x18\x03 \x01(\x0b\x32&.operations_research.sat.SatParametersJ\x04\x08\x02\x10\x03\x32t\n\x08\x43pSolver\x12h\n\x0cSolveProblem\x12+.operations_research.sat.v1.CpSolverRequest\x1a).operations_research.sat.CpSolverResponse\"\x00\x42G\n\x19\x63om.google.ortools.sat.v1B\x13\x43pModelServiceProtoP\x01\xaa\x02\x12Google.OrTools.Satb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.sat.cp_model_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\031com.google.ortools.sat.v1B\023CpModelServiceProtoP\001\252\002\022Google.OrTools.Sat'
  _globals['_CPSOLVERREQUEST']._serialized_start=129
  _globals['_CPSOLVERREQUEST']._serialized_end=266
  _globals['_CPSOLVER']._serialized_start=268
  _globals['_CPSOLVER']._serialized_end=384
# @@protoc_insertion_point(module_scope)
