# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/sat/sat_parameters.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/sat/sat_parameters.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n ortools/sat/sat_parameters.proto\x12\x17operations_research.sat\"\xa1m\n\rSatParameters\x12\x0f\n\x04name\x18\xab\x01 \x01(\t:\x00\x12`\n\x18preferred_variable_order\x18\x01 \x01(\x0e\x32\x34.operations_research.sat.SatParameters.VariableOrder:\x08IN_ORDER\x12Y\n\x10initial_polarity\x18\x02 \x01(\x0e\x32/.operations_research.sat.SatParameters.Polarity:\x0ePOLARITY_FALSE\x12\x1e\n\x10use_phase_saving\x18, \x01(\x08:\x04true\x12)\n\x1apolarity_rephase_increment\x18\xa8\x01 \x01(\x05:\x04\x31\x30\x30\x30\x12)\n\x19polarity_exploit_ls_hints\x18\xb5\x02 \x01(\x08:\x05\x66\x61lse\x12 \n\x15random_polarity_ratio\x18- \x01(\x01:\x01\x30\x12 \n\x15random_branches_ratio\x18  \x01(\x01:\x01\x30\x12!\n\x12use_erwa_heuristic\x18K \x01(\x08:\x05\x66\x61lse\x12%\n\x1ainitial_variables_activity\x18L \x01(\x01:\x01\x30\x12\x36\n\'also_bump_variables_in_conflict_reasons\x18M \x01(\x08:\x05\x66\x61lse\x12o\n\x16minimization_algorithm\x18\x04 \x01(\x0e\x32\x44.operations_research.sat.SatParameters.ConflictMinimizationAlgorithm:\tRECURSIVE\x12\x82\x01\n\x1d\x62inary_minimization_algorithm\x18\" \x01(\x0e\x32@.operations_research.sat.SatParameters.BinaryMinizationAlgorithm:\x19\x42INARY_MINIMIZATION_FIRST\x12\x32\n$subsumption_during_conflict_analysis\x18\x38 \x01(\x08:\x04true\x12$\n\x15\x63lause_cleanup_period\x18\x0b \x01(\x05:\x05\x31\x30\x30\x30\x30\x12 \n\x15\x63lause_cleanup_target\x18\r \x01(\x05:\x01\x30\x12\"\n\x14\x63lause_cleanup_ratio\x18\xbe\x01 \x01(\x01:\x03\x30.5\x12k\n\x19\x63lause_cleanup_protection\x18: \x01(\x0e\x32\x37.operations_research.sat.SatParameters.ClauseProtection:\x0fPROTECTION_NONE\x12#\n\x18\x63lause_cleanup_lbd_bound\x18; \x01(\x05:\x01\x35\x12g\n\x17\x63lause_cleanup_ordering\x18< \x01(\x0e\x32\x35.operations_research.sat.SatParameters.ClauseOrdering:\x0f\x43LAUSE_ACTIVITY\x12!\n\x14pb_cleanup_increment\x18. \x01(\x05:\x03\x32\x30\x30\x12\x1d\n\x10pb_cleanup_ratio\x18/ \x01(\x01:\x03\x30.5\x12$\n\x17variable_activity_decay\x18\x0f \x01(\x01:\x03\x30.8\x12+\n\x1bmax_variable_activity_value\x18\x10 \x01(\x01:\x06\x31\x65+100\x12\x1f\n\x11glucose_max_decay\x18\x16 \x01(\x01:\x04\x30.95\x12%\n\x17glucose_decay_increment\x18\x17 \x01(\x01:\x04\x30.01\x12,\n\x1eglucose_decay_increment_period\x18\x18 \x01(\x05:\x04\x35\x30\x30\x30\x12$\n\x15\x63lause_activity_decay\x18\x11 \x01(\x01:\x05\x30.999\x12(\n\x19max_clause_activity_value\x18\x12 \x01(\x01:\x05\x31\x65+20\x12S\n\x12restart_algorithms\x18= \x03(\x0e\x32\x37.operations_research.sat.SatParameters.RestartAlgorithm\x12\x65\n\x1a\x64\x65\x66\x61ult_restart_algorithms\x18\x46 \x01(\t:ALUBY_RESTART,LBD_MOVING_AVERAGE_RESTART,DL_MOVING_AVERAGE_RESTART\x12\x1a\n\x0erestart_period\x18\x1e \x01(\x05:\x02\x35\x30\x12\'\n\x1brestart_running_window_size\x18> \x01(\x05:\x02\x35\x30\x12#\n\x18restart_dl_average_ratio\x18? \x01(\x01:\x01\x31\x12$\n\x19restart_lbd_average_ratio\x18G \x01(\x01:\x01\x31\x12#\n\x14use_blocking_restart\x18@ \x01(\x08:\x05\x66\x61lse\x12*\n\x1c\x62locking_restart_window_size\x18\x41 \x01(\x05:\x04\x35\x30\x30\x30\x12(\n\x1b\x62locking_restart_multiplier\x18\x42 \x01(\x01:\x03\x31.4\x12\x30\n%num_conflicts_before_strategy_changes\x18\x44 \x01(\x05:\x01\x30\x12)\n\x1estrategy_change_increase_ratio\x18\x45 \x01(\x01:\x01\x30\x12 \n\x13max_time_in_seconds\x18$ \x01(\x01:\x03inf\x12#\n\x16max_deterministic_time\x18\x43 \x01(\x01:\x03inf\x12)\n\x1dmax_num_deterministic_batches\x18\xa3\x02 \x01(\x05:\x01\x30\x12\x34\n\x17max_number_of_conflicts\x18% \x01(\x03:\x13\x39\x32\x32\x33\x33\x37\x32\x30\x33\x36\x38\x35\x34\x37\x37\x35\x38\x30\x37\x12\x1f\n\x10max_memory_in_mb\x18( \x01(\x03:\x05\x31\x30\x30\x30\x30\x12#\n\x12\x61\x62solute_gap_limit\x18\x9f\x01 \x01(\x01:\x06\x30.0001\x12\x1e\n\x12relative_gap_limit\x18\xa0\x01 \x01(\x01:\x01\x30\x12\x16\n\x0brandom_seed\x18\x1f \x01(\x05:\x01\x31\x12)\n\x19permute_variable_randomly\x18\xb2\x01 \x01(\x08:\x05\x66\x61lse\x12\x31\n!permute_presolve_constraint_order\x18\xb3\x01 \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x0fuse_absl_random\x18\xb4\x01 \x01(\x08:\x05\x66\x61lse\x12\"\n\x13log_search_progress\x18) \x01(\x08:\x05\x66\x61lse\x12(\n\x18log_subsolver_statistics\x18\xbd\x01 \x01(\x08:\x05\x66\x61lse\x12\x15\n\nlog_prefix\x18\xb9\x01 \x01(\t:\x00\x12\x1c\n\rlog_to_stdout\x18\xba\x01 \x01(\x08:\x04true\x12\x1f\n\x0flog_to_response\x18\xbb\x01 \x01(\x08:\x05\x66\x61lse\x12 \n\x11use_pb_resolution\x18+ \x01(\x08:\x05\x66\x61lse\x12\x36\n\'minimize_reduction_during_pb_resolution\x18\x30 \x01(\x08:\x05\x66\x61lse\x12,\n\x1e\x63ount_assumption_levels_in_lbd\x18\x31 \x01(\x08:\x04true\x12#\n\x16presolve_bve_threshold\x18\x36 \x01(\x05:\x03\x35\x30\x30\x12,\n\x1c\x66ilter_sat_postsolve_clauses\x18\xc4\x02 \x01(\x08:\x05\x66\x61lse\x12%\n\x1apresolve_bve_clause_weight\x18\x37 \x01(\x05:\x01\x33\x12,\n probing_deterministic_time_limit\x18\xe2\x01 \x01(\x01:\x01\x31\x12\x35\n)presolve_probing_deterministic_time_limit\x18\x39 \x01(\x01:\x02\x33\x30\x12%\n\x17presolve_blocked_clause\x18X \x01(\x08:\x04true\x12\x1e\n\x10presolve_use_bva\x18H \x01(\x08:\x04true\x12!\n\x16presolve_bva_threshold\x18I \x01(\x05:\x01\x31\x12#\n\x17max_presolve_iterations\x18\x8a\x01 \x01(\x05:\x01\x33\x12\x1f\n\x11\x63p_model_presolve\x18V \x01(\x08:\x04true\x12!\n\x16\x63p_model_probing_level\x18n \x01(\x05:\x01\x32\x12\'\n\x19\x63p_model_use_sat_presolve\x18] \x01(\x08:\x04true\x12+\n\x1cremove_fixed_variables_early\x18\xb6\x02 \x01(\x08:\x04true\x12&\n\x16\x64\x65tect_table_with_cost\x18\xd8\x01 \x01(\x08:\x05\x66\x61lse\x12#\n\x17table_compression_level\x18\xd9\x01 \x01(\x05:\x01\x32\x12*\n\x1a\x65xpand_alldiff_constraints\x18\xaa\x01 \x01(\x08:\x05\x66\x61lse\x12%\n\x17max_alldiff_domain_size\x18\xc0\x02 \x01(\x05:\x03\x32\x35\x36\x12+\n\x1c\x65xpand_reservoir_constraints\x18\xb6\x01 \x01(\x08:\x04true\x12.\n\x1e\x65xpand_reservoir_using_circuit\x18\xa0\x02 \x01(\x08:\x05\x66\x61lse\x12.\n\x1e\x65ncode_cumulative_as_reservoir\x18\x9f\x02 \x01(\x08:\x05\x66\x61lse\x12*\n\x1emax_lin_max_size_for_expansion\x18\x98\x02 \x01(\x05:\x01\x30\x12,\n\x1c\x64isable_constraint_expansion\x18\xb5\x01 \x01(\x08:\x05\x66\x61lse\x12=\n-encode_complex_linear_constraint_with_integer\x18\xdf\x01 \x01(\x08:\x05\x66\x61lse\x12\x33\n\x1bmerge_no_overlap_work_limit\x18\x91\x01 \x01(\x01:\r1000000000000\x12\x30\n\x1cmerge_at_most_one_work_limit\x18\x92\x01 \x01(\x01:\t100000000\x12\'\n\x1bpresolve_substitution_level\x18\x93\x01 \x01(\x05:\x01\x31\x12\x34\n$presolve_extract_integer_enforcement\x18\xae\x01 \x01(\x08:\x05\x66\x61lse\x12\x31\n\x1dpresolve_inclusion_work_limit\x18\xc9\x01 \x01(\x03:\t100000000\x12\x1b\n\x0cignore_names\x18\xca\x01 \x01(\x08:\x04true\x12\x1e\n\x0finfer_all_diffs\x18\xe9\x01 \x01(\x08:\x04true\x12&\n\x17\x66ind_big_linear_overlap\x18\xea\x01 \x01(\x08:\x04true\x12#\n\x14use_sat_inprocessing\x18\xa3\x01 \x01(\x08:\x04true\x12&\n\x18inprocessing_dtime_ratio\x18\x91\x02 \x01(\x01:\x03\x30.2\x12&\n\x1ainprocessing_probing_dtime\x18\x92\x02 \x01(\x01:\x01\x31\x12+\n\x1finprocessing_minimization_dtime\x18\x93\x02 \x01(\x01:\x01\x31\x12>\n/inprocessing_minimization_use_conflict_analysis\x18\xa9\x02 \x01(\x08:\x04true\x12;\n+inprocessing_minimization_use_all_orderings\x18\xaa\x02 \x01(\x08:\x05\x66\x61lse\x12\x17\n\x0bnum_workers\x18\xce\x01 \x01(\x05:\x01\x30\x12\x1d\n\x12num_search_workers\x18\x64 \x01(\x05:\x01\x30\x12\x1f\n\x13num_full_subsolvers\x18\xa6\x02 \x01(\x05:\x01\x30\x12\x13\n\nsubsolvers\x18\xcf\x01 \x03(\t\x12\x19\n\x10\x65xtra_subsolvers\x18\xdb\x01 \x03(\t\x12\x1a\n\x11ignore_subsolvers\x18\xd1\x01 \x03(\t\x12\x1a\n\x11\x66ilter_subsolvers\x18\xa5\x02 \x03(\t\x12\x41\n\x10subsolver_params\x18\xd2\x01 \x03(\x0b\x32&.operations_research.sat.SatParameters\x12!\n\x11interleave_search\x18\x88\x01 \x01(\x08:\x05\x66\x61lse\x12!\n\x15interleave_batch_size\x18\x86\x01 \x01(\x05:\x01\x30\x12$\n\x16share_objective_bounds\x18q \x01(\x08:\x04true\x12%\n\x17share_level_zero_bounds\x18r \x01(\x08:\x04true\x12#\n\x14share_binary_clauses\x18\xcb\x01 \x01(\x08:\x04true\x12\"\n\x12share_glue_clauses\x18\x9d\x02 \x01(\x08:\x05\x66\x61lse\x12&\n\x17minimize_shared_clauses\x18\xac\x02 \x01(\x08:\x04true\x12$\n\x18share_glue_clauses_dtime\x18\xc2\x02 \x01(\x01:\x01\x31\x12\x30\n debug_postsolve_with_full_solver\x18\xa2\x01 \x01(\x08:\x05\x66\x61lse\x12-\n!debug_max_num_presolve_operations\x18\x97\x01 \x01(\x05:\x01\x30\x12\'\n\x17\x64\x65\x62ug_crash_on_bad_hint\x18\xc3\x01 \x01(\x08:\x05\x66\x61lse\x12\x33\n#debug_crash_if_presolve_breaks_hint\x18\xb2\x02 \x01(\x08:\x05\x66\x61lse\x12$\n\x16use_optimization_hints\x18# \x01(\x08:\x04true\x12\"\n\x17\x63ore_minimization_level\x18\x32 \x01(\x05:\x01\x32\x12!\n\x13\x66ind_multiple_cores\x18T \x01(\x08:\x04true\x12 \n\x12\x63over_optimization\x18Y \x01(\x08:\x04true\x12x\n\x18max_sat_assumption_order\x18\x33 \x01(\x0e\x32<.operations_research.sat.SatParameters.MaxSatAssumptionOrder:\x18\x44\x45\x46\x41ULT_ASSUMPTION_ORDER\x12/\n max_sat_reverse_assumption_order\x18\x34 \x01(\x08:\x05\x66\x61lse\x12|\n\x16max_sat_stratification\x18\x35 \x01(\x0e\x32\x44.operations_research.sat.SatParameters.MaxSatStratificationAlgorithm:\x16STRATIFICATION_DESCENT\x12.\n!propagation_loop_detection_factor\x18\xdd\x01 \x01(\x01:\x02\x31\x30\x12\x37\n)use_precedences_in_disjunctive_constraint\x18J \x01(\x08:\x04true\x12\x42\n5max_size_to_create_precedence_literals_in_disjunctive\x18\xe5\x01 \x01(\x05:\x02\x36\x30\x12\x35\n%use_strong_propagation_in_disjunctive\x18\xe6\x01 \x01(\x08:\x05\x66\x61lse\x12\x35\n%use_dynamic_precedence_in_disjunctive\x18\x87\x02 \x01(\x08:\x05\x66\x61lse\x12\x34\n$use_dynamic_precedence_in_cumulative\x18\x8c\x02 \x01(\x08:\x05\x66\x61lse\x12\x31\n\"use_overload_checker_in_cumulative\x18N \x01(\x08:\x05\x66\x61lse\x12\x37\n\'use_conservative_scale_overload_checker\x18\x9e\x02 \x01(\x08:\x05\x66\x61lse\x12\x37\n(use_timetable_edge_finding_in_cumulative\x18O \x01(\x08:\x05\x66\x61lse\x12:\n,max_num_intervals_for_timetable_edge_finding\x18\x84\x02 \x01(\x05:\x03\x31\x30\x30\x12\x32\n\"use_hard_precedences_in_cumulative\x18\xd7\x01 \x01(\x08:\x05\x66\x61lse\x12\'\n\x17\x65xploit_all_precedences\x18\xdc\x01 \x01(\x08:\x05\x66\x61lse\x12\x36\n(use_disjunctive_constraint_in_cumulative\x18P \x01(\x08:\x04true\x12\x32\n%no_overlap_2d_boolean_relations_limit\x18\xc1\x02 \x01(\x05:\x02\x31\x30\x12\x30\n use_timetabling_in_no_overlap_2d\x18\xc8\x01 \x01(\x08:\x05\x66\x61lse\x12\x38\n(use_energetic_reasoning_in_no_overlap_2d\x18\xd5\x01 \x01(\x08:\x05\x66\x61lse\x12=\n-use_area_energetic_reasoning_in_no_overlap_2d\x18\x8f\x02 \x01(\x08:\x05\x66\x61lse\x12\x37\n\'use_try_edge_reasoning_in_no_overlap_2d\x18\xab\x02 \x01(\x08:\x05\x66\x61lse\x12<\n-max_pairs_pairwise_reasoning_in_no_overlap_2d\x18\x94\x02 \x01(\x05:\x04\x31\x32\x35\x30\x12\x42\n6maximum_regions_to_split_in_disconnected_no_overlap_2d\x18\xbb\x02 \x01(\x05:\x01\x30\x12\x38\n)use_linear3_for_no_overlap_2d_precedences\x18\xc3\x02 \x01(\x08:\x04true\x12-\n\x1euse_dual_scheduling_heuristics\x18\xd6\x01 \x01(\x08:\x04true\x12-\n\x1duse_all_different_for_circuit\x18\xb7\x02 \x01(\x08:\x05\x66\x61lse\x12=\n1routing_cut_subset_size_for_binary_relation_bound\x18\xb8\x02 \x01(\x05:\x01\x30\x12\x43\n7routing_cut_subset_size_for_tight_binary_relation_bound\x18\xb9\x02 \x01(\x05:\x01\x30\x12\x43\n7routing_cut_subset_size_for_exact_binary_relation_bound\x18\xbc\x02 \x01(\x05:\x01\x38\x12<\n0routing_cut_subset_size_for_shortest_paths_bound\x18\xbe\x02 \x01(\x05:\x01\x38\x12(\n\x15routing_cut_dp_effort\x18\xba\x02 \x01(\x01:\x08\x31\x30\x30\x30\x30\x30\x30\x30\x12\x32\n&routing_cut_max_infeasible_path_length\x18\xbd\x02 \x01(\x05:\x01\x36\x12\x62\n\x10search_branching\x18R \x01(\x0e\x32\x36.operations_research.sat.SatParameters.SearchBranching:\x10\x41UTOMATIC_SEARCH\x12 \n\x13hint_conflict_limit\x18\x99\x01 \x01(\x05:\x02\x31\x30\x12\x1b\n\x0brepair_hint\x18\xa7\x01 \x01(\x08:\x05\x66\x61lse\x12\x33\n#fix_variables_to_their_hinted_value\x18\xc0\x01 \x01(\x08:\x05\x66\x61lse\x12\"\n\x12use_probing_search\x18\xb0\x01 \x01(\x08:\x05\x66\x61lse\x12#\n\x14use_extended_probing\x18\x8d\x02 \x01(\x08:\x04true\x12.\n\x1eprobing_num_combinations_limit\x18\x90\x02 \x01(\x05:\x05\x32\x30\x30\x30\x30\x12<\n,shaving_deterministic_time_in_probing_search\x18\xcc\x01 \x01(\x01:\x05\x30.001\x12/\n!shaving_search_deterministic_time\x18\xcd\x01 \x01(\x01:\x03\x30.1\x12%\n\x18shaving_search_threshold\x18\xa2\x02 \x01(\x03:\x02\x36\x34\x12\'\n\x17use_objective_lb_search\x18\xe4\x01 \x01(\x08:\x05\x66\x61lse\x12,\n\x1cuse_objective_shaving_search\x18\xfd\x01 \x01(\x08:\x05\x66\x61lse\x12$\n\x17variables_shaving_level\x18\xa1\x02 \x01(\x05:\x02-1\x12.\n!pseudo_cost_reliability_threshold\x18{ \x01(\x03:\x03\x31\x30\x30\x12!\n\x12optimize_with_core\x18S \x01(\x08:\x05\x66\x61lse\x12,\n\x1coptimize_with_lb_tree_search\x18\xbc\x01 \x01(\x08:\x05\x66\x61lse\x12/\n\x1fsave_lp_basis_in_lb_tree_search\x18\x9c\x02 \x01(\x08:\x05\x66\x61lse\x12\'\n\x1b\x62inary_search_num_conflicts\x18\x63 \x01(\x05:\x02-1\x12#\n\x14optimize_with_max_hs\x18U \x01(\x08:\x05\x66\x61lse\x12#\n\x14use_feasibility_jump\x18\x89\x02 \x01(\x08:\x04true\x12\x1b\n\x0buse_ls_only\x18\xf0\x01 \x01(\x08:\x05\x66\x61lse\x12%\n\x16\x66\x65\x61sibility_jump_decay\x18\xf2\x01 \x01(\x01:\x04\x30.95\x12\x30\n$feasibility_jump_linearization_level\x18\x81\x02 \x01(\x05:\x01\x32\x12+\n\x1f\x66\x65\x61sibility_jump_restart_factor\x18\x82\x02 \x01(\x05:\x01\x31\x12*\n\x1c\x66\x65\x61sibility_jump_batch_dtime\x18\xa4\x02 \x01(\x01:\x03\x30.1\x12=\n.feasibility_jump_var_randomization_probability\x18\xf7\x01 \x01(\x01:\x04\x30.05\x12;\n-feasibility_jump_var_perburbation_range_ratio\x18\xf8\x01 \x01(\x01:\x03\x30.2\x12/\n feasibility_jump_enable_restarts\x18\xfa\x01 \x01(\x08:\x04true\x12;\n-feasibility_jump_max_expanded_constraint_size\x18\x88\x02 \x01(\x05:\x03\x35\x30\x30\x12\x1c\n\x10num_violation_ls\x18\xf4\x01 \x01(\x05:\x01\x30\x12.\n violation_ls_perturbation_period\x18\xf9\x01 \x01(\x05:\x03\x31\x30\x30\x12\x34\n&violation_ls_compound_move_probability\x18\x83\x02 \x01(\x01:\x03\x30.5\x12#\n\x17shared_tree_num_workers\x18\xeb\x01 \x01(\x05:\x01\x30\x12&\n\x16use_shared_tree_search\x18\xec\x01 \x01(\x08:\x05\x66\x61lse\x12\x37\n+shared_tree_worker_min_restarts_per_subtree\x18\x9a\x02 \x01(\x05:\x01\x31\x12\x36\n\'shared_tree_worker_enable_trail_sharing\x18\xa7\x02 \x01(\x08:\x04true\x12\x36\n\'shared_tree_worker_enable_phase_sharing\x18\xb0\x02 \x01(\x08:\x04true\x12.\n\"shared_tree_open_leaves_per_worker\x18\x99\x02 \x01(\x01:\x01\x32\x12\x30\n shared_tree_max_nodes_per_worker\x18\xee\x01 \x01(\x05:\x05\x31\x30\x30\x30\x30\x12x\n\x1ashared_tree_split_strategy\x18\xef\x01 \x01(\x0e\x32>.operations_research.sat.SatParameters.SharedTreeSplitStrategy:\x13SPLIT_STRATEGY_AUTO\x12)\n\x1dshared_tree_balance_tolerance\x18\xb1\x02 \x01(\x05:\x01\x31\x12&\n\x17\x65numerate_all_solutions\x18W \x01(\x08:\x05\x66\x61lse\x12\x37\n\'keep_all_feasible_solutions_in_presolve\x18\xad\x01 \x01(\x08:\x05\x66\x61lse\x12\x32\n\"fill_tightened_domains_in_response\x18\x84\x01 \x01(\x08:\x05\x66\x61lse\x12\x35\n%fill_additional_solutions_in_response\x18\xc2\x01 \x01(\x08:\x05\x66\x61lse\x12\'\n\x19instantiate_all_variables\x18j \x01(\x08:\x04true\x12\x36\n(auto_detect_greater_than_at_least_one_of\x18_ \x01(\x08:\x04true\x12(\n\x19stop_after_first_solution\x18\x62 \x01(\x08:\x05\x66\x61lse\x12#\n\x13stop_after_presolve\x18\x95\x01 \x01(\x08:\x05\x66\x61lse\x12+\n\x1bstop_after_root_propagation\x18\xfc\x01 \x01(\x08:\x05\x66\x61lse\x12$\n\x16lns_initial_difficulty\x18\xb3\x02 \x01(\x01:\x03\x30.5\x12-\n\x1flns_initial_deterministic_limit\x18\xb4\x02 \x01(\x01:\x03\x30.1\x12\x16\n\x07use_lns\x18\x9b\x02 \x01(\x08:\x04true\x12\x1b\n\x0cuse_lns_only\x18\x65 \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x12solution_pool_size\x18\xc1\x01 \x01(\x05:\x01\x33\x12\x1b\n\x0cuse_rins_lns\x18\x81\x01 \x01(\x08:\x04true\x12#\n\x14use_feasibility_pump\x18\xa4\x01 \x01(\x08:\x04true\x12\x1f\n\x10use_lb_relax_lns\x18\xff\x01 \x01(\x08:\x04true\x12+\n\x1elb_relax_num_workers_threshold\x18\xa8\x02 \x01(\x05:\x02\x31\x36\x12\x63\n\x0b\x66p_rounding\x18\xa5\x01 \x01(\x0e\x32\x37.operations_research.sat.SatParameters.FPRoundingMethod:\x14PROPAGATION_ASSISTED\x12$\n\x14\x64iversify_lns_params\x18\x89\x01 \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x10randomize_search\x18g \x01(\x08:\x05\x66\x61lse\x12+\n search_random_variable_pool_size\x18h \x01(\x03:\x01\x30\x12+\n\x1bpush_all_tasks_toward_start\x18\x86\x02 \x01(\x08:\x05\x66\x61lse\x12%\n\x16use_optional_variables\x18l \x01(\x08:\x05\x66\x61lse\x12!\n\x13use_exact_lp_reason\x18m \x01(\x08:\x04true\x12\'\n\x17use_combined_no_overlap\x18\x85\x01 \x01(\x08:\x05\x66\x61lse\x12*\n\x1e\x61t_most_one_max_expansion_size\x18\x8e\x02 \x01(\x05:\x01\x33\x12\"\n\x13\x63\x61tch_sigint_signal\x18\x87\x01 \x01(\x08:\x04true\x12!\n\x12use_implied_bounds\x18\x90\x01 \x01(\x08:\x04true\x12\"\n\x12polish_lp_solution\x18\xaf\x01 \x01(\x08:\x05\x66\x61lse\x12#\n\x13lp_primal_tolerance\x18\x8a\x02 \x01(\x01:\x05\x31\x65-07\x12!\n\x11lp_dual_tolerance\x18\x8b\x02 \x01(\x01:\x05\x31\x65-07\x12 \n\x11\x63onvert_intervals\x18\xb1\x01 \x01(\x08:\x04true\x12\x1a\n\x0esymmetry_level\x18\xb7\x01 \x01(\x05:\x01\x32\x12\"\n\x12use_symmetry_in_lp\x18\xad\x02 \x01(\x08:\x05\x66\x61lse\x12)\n\x19keep_symmetry_in_presolve\x18\xaf\x02 \x01(\x08:\x05\x66\x61lse\x12\x37\n+symmetry_detection_deterministic_time_limit\x18\xae\x02 \x01(\x01:\x01\x31\x12%\n\x16new_linear_propagation\x18\xe0\x01 \x01(\x08:\x04true\x12\x1f\n\x11linear_split_size\x18\x80\x02 \x01(\x05:\x03\x31\x30\x30\x12\x1e\n\x13linearization_level\x18Z \x01(\x05:\x01\x31\x12!\n\x16\x62oolean_encoding_level\x18k \x01(\x05:\x01\x31\x12=\n0max_domain_size_when_encoding_eq_neq_constraints\x18\xbf\x01 \x01(\x05:\x02\x31\x36\x12\x1b\n\x0cmax_num_cuts\x18[ \x01(\x05:\x05\x31\x30\x30\x30\x30\x12\x15\n\tcut_level\x18\xc4\x01 \x01(\x05:\x01\x31\x12*\n\x1bonly_add_cuts_at_level_zero\x18\\ \x01(\x08:\x05\x66\x61lse\x12!\n\x11\x61\x64\x64_objective_cut\x18\xc5\x01 \x01(\x08:\x05\x66\x61lse\x12\x19\n\x0b\x61\x64\x64_cg_cuts\x18u \x01(\x08:\x04true\x12\x1a\n\x0c\x61\x64\x64_mir_cuts\x18x \x01(\x08:\x04true\x12!\n\x12\x61\x64\x64_zero_half_cuts\x18\xa9\x01 \x01(\x08:\x04true\x12\x1e\n\x0f\x61\x64\x64_clique_cuts\x18\xac\x01 \x01(\x08:\x04true\x12\x1b\n\x0c\x61\x64\x64_rlt_cuts\x18\x97\x02 \x01(\x08:\x04true\x12\"\n\x15max_all_diff_cut_size\x18\x94\x01 \x01(\x05:\x02\x36\x34\x12\x1f\n\x10\x61\x64\x64_lin_max_cuts\x18\x98\x01 \x01(\x08:\x04true\x12)\n\x1cmax_integer_rounding_scaling\x18w \x01(\x05:\x03\x36\x30\x30\x12\'\n\x19\x61\x64\x64_lp_constraints_lazily\x18p \x01(\x08:\x04true\x12!\n\x12root_lp_iterations\x18\xe3\x01 \x01(\x05:\x04\x32\x30\x30\x30\x12\x32\n$min_orthogonality_for_lp_constraints\x18s \x01(\x01:\x04\x30.05\x12(\n\x1cmax_cut_rounds_at_level_zero\x18\x9a\x01 \x01(\x05:\x01\x31\x12+\n\x1emax_consecutive_inactive_count\x18y \x01(\x05:\x03\x31\x30\x30\x12\x30\n\x1a\x63ut_max_active_count_value\x18\x9b\x01 \x01(\x01:\x0b\x31\x30\x30\x30\x30\x30\x30\x30\x30\x30\x30\x12$\n\x16\x63ut_active_count_decay\x18\x9c\x01 \x01(\x01:\x03\x30.8\x12!\n\x12\x63ut_cleanup_target\x18\x9d\x01 \x01(\x05:\x04\x31\x30\x30\x30\x12&\n\x1anew_constraints_batch_size\x18z \x01(\x05:\x02\x35\x30\x12)\n\x1b\x65xploit_integer_lp_solution\x18^ \x01(\x08:\x04true\x12%\n\x17\x65xploit_all_lp_solution\x18t \x01(\x08:\x04true\x12%\n\x15\x65xploit_best_solution\x18\x82\x01 \x01(\x08:\x05\x66\x61lse\x12+\n\x1b\x65xploit_relaxation_solution\x18\xa1\x01 \x01(\x08:\x05\x66\x61lse\x12 \n\x11\x65xploit_objective\x18\x83\x01 \x01(\x08:\x04true\x12)\n\x19\x64\x65tect_linearized_product\x18\x95\x02 \x01(\x08:\x05\x66\x61lse\x12\x1f\n\rmip_max_bound\x18| \x01(\x01:\x08\x31\x30\x30\x30\x30\x30\x30\x30\x12\x1a\n\x0fmip_var_scaling\x18} \x01(\x01:\x01\x31\x12&\n\x16mip_scale_large_domain\x18\xe1\x01 \x01(\x08:\x05\x66\x61lse\x12\x30\n!mip_automatically_scale_variables\x18\xa6\x01 \x01(\x08:\x04true\x12\x1d\n\ronly_solve_ip\x18\xde\x01 \x01(\x08:\x05\x66\x61lse\x12#\n\x14mip_wanted_precision\x18~ \x01(\x01:\x05\x31\x65-06\x12%\n\x19mip_max_activity_exponent\x18\x7f \x01(\x05:\x02\x35\x33\x12$\n\x13mip_check_precision\x18\x80\x01 \x01(\x01:\x06\x30.0001\x12/\n mip_compute_true_objective_bound\x18\xc6\x01 \x01(\x08:\x04true\x12\'\n\x17mip_max_valid_magnitude\x18\xc7\x01 \x01(\x01:\x05\x31\x65+20\x12;\n+mip_treat_high_magnitude_bounds_as_infinity\x18\x96\x02 \x01(\x08:\x05\x66\x61lse\x12\"\n\x12mip_drop_tolerance\x18\xe8\x01 \x01(\x01:\x05\x31\x65-16\x12\x1e\n\x12mip_presolve_level\x18\x85\x02 \x01(\x05:\x01\x32\"H\n\rVariableOrder\x12\x0c\n\x08IN_ORDER\x10\x00\x12\x14\n\x10IN_REVERSE_ORDER\x10\x01\x12\x13\n\x0fIN_RANDOM_ORDER\x10\x02\"F\n\x08Polarity\x12\x11\n\rPOLARITY_TRUE\x10\x00\x12\x12\n\x0ePOLARITY_FALSE\x10\x01\x12\x13\n\x0fPOLARITY_RANDOM\x10\x02\"V\n\x1d\x43onflictMinimizationAlgorithm\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06SIMPLE\x10\x01\x12\r\n\tRECURSIVE\x10\x02\x12\x10\n\x0c\x45XPERIMENTAL\x10\x03\"\xe0\x01\n\x19\x42inaryMinizationAlgorithm\x12\x1a\n\x16NO_BINARY_MINIMIZATION\x10\x00\x12\x1d\n\x19\x42INARY_MINIMIZATION_FIRST\x10\x01\x12\x37\n3BINARY_MINIMIZATION_FIRST_WITH_TRANSITIVE_REDUCTION\x10\x04\x12)\n%BINARY_MINIMIZATION_WITH_REACHABILITY\x10\x02\x12$\n EXPERIMENTAL_BINARY_MINIMIZATION\x10\x03\"R\n\x10\x43lauseProtection\x12\x13\n\x0fPROTECTION_NONE\x10\x00\x12\x15\n\x11PROTECTION_ALWAYS\x10\x01\x12\x12\n\x0ePROTECTION_LBD\x10\x02\"5\n\x0e\x43lauseOrdering\x12\x13\n\x0f\x43LAUSE_ACTIVITY\x10\x00\x12\x0e\n\nCLAUSE_LBD\x10\x01\"\x86\x01\n\x10RestartAlgorithm\x12\x0e\n\nNO_RESTART\x10\x00\x12\x10\n\x0cLUBY_RESTART\x10\x01\x12\x1d\n\x19\x44L_MOVING_AVERAGE_RESTART\x10\x02\x12\x1e\n\x1aLBD_MOVING_AVERAGE_RESTART\x10\x03\x12\x11\n\rFIXED_RESTART\x10\x04\"t\n\x15MaxSatAssumptionOrder\x12\x1c\n\x18\x44\x45\x46\x41ULT_ASSUMPTION_ORDER\x10\x00\x12\x1d\n\x19ORDER_ASSUMPTION_BY_DEPTH\x10\x01\x12\x1e\n\x1aORDER_ASSUMPTION_BY_WEIGHT\x10\x02\"o\n\x1dMaxSatStratificationAlgorithm\x12\x17\n\x13STRATIFICATION_NONE\x10\x00\x12\x1a\n\x16STRATIFICATION_DESCENT\x10\x01\x12\x19\n\x15STRATIFICATION_ASCENT\x10\x02\"\xe1\x01\n\x0fSearchBranching\x12\x14\n\x10\x41UTOMATIC_SEARCH\x10\x00\x12\x10\n\x0c\x46IXED_SEARCH\x10\x01\x12\x14\n\x10PORTFOLIO_SEARCH\x10\x02\x12\r\n\tLP_SEARCH\x10\x03\x12\x16\n\x12PSEUDO_COST_SEARCH\x10\x04\x12\'\n#PORTFOLIO_WITH_QUICK_RESTART_SEARCH\x10\x05\x12\x0f\n\x0bHINT_SEARCH\x10\x06\x12\x18\n\x14PARTIAL_FIXED_SEARCH\x10\x07\x12\x15\n\x11RANDOMIZED_SEARCH\x10\x08\"\xb8\x01\n\x17SharedTreeSplitStrategy\x12\x17\n\x13SPLIT_STRATEGY_AUTO\x10\x00\x12\x1e\n\x1aSPLIT_STRATEGY_DISCREPANCY\x10\x01\x12\x1f\n\x1bSPLIT_STRATEGY_OBJECTIVE_LB\x10\x02\x12 \n\x1cSPLIT_STRATEGY_BALANCED_TREE\x10\x03\x12!\n\x1dSPLIT_STRATEGY_FIRST_PROPOSAL\x10\x04\"h\n\x10\x46PRoundingMethod\x12\x13\n\x0fNEAREST_INTEGER\x10\x00\x12\x0e\n\nLOCK_BASED\x10\x01\x12\x15\n\x11\x41\x43TIVE_LOCK_BASED\x10\x03\x12\x18\n\x14PROPAGATION_ASSISTED\x10\x02\x42k\n\x16\x63om.google.ortools.satP\x01Z:github.com/google/or-tools/ortools/sat/proto/satparameters\xaa\x02\x12Google.OrTools.Sat')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.sat.sat_parameters_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\026com.google.ortools.satP\001Z:github.com/google/or-tools/ortools/sat/proto/satparameters\252\002\022Google.OrTools.Sat'
  _globals['_SATPARAMETERS']._serialized_start=62
  _globals['_SATPARAMETERS']._serialized_end=14047
  _globals['_SATPARAMETERS_VARIABLEORDER']._serialized_start=12560
  _globals['_SATPARAMETERS_VARIABLEORDER']._serialized_end=12632
  _globals['_SATPARAMETERS_POLARITY']._serialized_start=12634
  _globals['_SATPARAMETERS_POLARITY']._serialized_end=12704
  _globals['_SATPARAMETERS_CONFLICTMINIMIZATIONALGORITHM']._serialized_start=12706
  _globals['_SATPARAMETERS_CONFLICTMINIMIZATIONALGORITHM']._serialized_end=12792
  _globals['_SATPARAMETERS_BINARYMINIZATIONALGORITHM']._serialized_start=12795
  _globals['_SATPARAMETERS_BINARYMINIZATIONALGORITHM']._serialized_end=13019
  _globals['_SATPARAMETERS_CLAUSEPROTECTION']._serialized_start=13021
  _globals['_SATPARAMETERS_CLAUSEPROTECTION']._serialized_end=13103
  _globals['_SATPARAMETERS_CLAUSEORDERING']._serialized_start=13105
  _globals['_SATPARAMETERS_CLAUSEORDERING']._serialized_end=13158
  _globals['_SATPARAMETERS_RESTARTALGORITHM']._serialized_start=13161
  _globals['_SATPARAMETERS_RESTARTALGORITHM']._serialized_end=13295
  _globals['_SATPARAMETERS_MAXSATASSUMPTIONORDER']._serialized_start=13297
  _globals['_SATPARAMETERS_MAXSATASSUMPTIONORDER']._serialized_end=13413
  _globals['_SATPARAMETERS_MAXSATSTRATIFICATIONALGORITHM']._serialized_start=13415
  _globals['_SATPARAMETERS_MAXSATSTRATIFICATIONALGORITHM']._serialized_end=13526
  _globals['_SATPARAMETERS_SEARCHBRANCHING']._serialized_start=13529
  _globals['_SATPARAMETERS_SEARCHBRANCHING']._serialized_end=13754
  _globals['_SATPARAMETERS_SHAREDTREESPLITSTRATEGY']._serialized_start=13757
  _globals['_SATPARAMETERS_SHAREDTREESPLITSTRATEGY']._serialized_end=13941
  _globals['_SATPARAMETERS_FPROUNDINGMETHOD']._serialized_start=13943
  _globals['_SATPARAMETERS_FPROUNDINGMETHOD']._serialized_end=14047
# @@protoc_insertion_point(module_scope)
