"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import ortools.service.v1.mathopt.model_parameters_pb2
import ortools.service.v1.mathopt.model_pb2
import ortools.service.v1.mathopt.parameters_pb2
import ortools.service.v1.mathopt.result_pb2
import ortools.service.v1.mathopt.solver_resources_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class SolveMathOptModelRequest(google.protobuf.message.Message):
    """Request for a unary remote solve in MathOpt."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SOLVER_TYPE_FIELD_NUMBER: builtins.int
    MODEL_FIELD_NUMBER: builtins.int
    RESOURCES_FIELD_NUMBER: builtins.int
    PARAMETERS_FIELD_NUMBER: builtins.int
    MODEL_PARAMETERS_FIELD_NUMBER: builtins.int
    solver_type: ortools.service.v1.mathopt.parameters_pb2.SolverTypeProto.ValueType
    """Solver type to numerically solve the problem. Note that if a solver does
    not support a specific feature in the model, the optimization procedure
    won't be successful.
    """
    @property
    def model(self) -> ortools.service.v1.mathopt.model_pb2.ModelProto:
        """A mathematical representation of the optimization problem to solve."""

    @property
    def resources(self) -> ortools.service.v1.mathopt.solver_resources_pb2.SolverResourcesProto:
        """Hints on resources requested for the solve."""

    @property
    def parameters(self) -> ortools.service.v1.mathopt.parameters_pb2.SolveParametersProto:
        """Parameters to control a single solve. The enable_output parameter is
        handled specifically. For solvers that support messages callbacks, setting
        it to true will have the server register a message callback. The resulting
        messages will be returned in SolveMathOptModelResponse.messages. For other
        solvers, setting enable_output to true will result in an error.
        """

    @property
    def model_parameters(self) -> ortools.service.v1.mathopt.model_parameters_pb2.ModelSolveParametersProto:
        """Parameters to control a single solve that are specific to the input model
        (see SolveParametersProto for model independent parameters).
        """

    def __init__(
        self,
        *,
        solver_type: ortools.service.v1.mathopt.parameters_pb2.SolverTypeProto.ValueType = ...,
        model: ortools.service.v1.mathopt.model_pb2.ModelProto | None = ...,
        resources: ortools.service.v1.mathopt.solver_resources_pb2.SolverResourcesProto | None = ...,
        parameters: ortools.service.v1.mathopt.parameters_pb2.SolveParametersProto | None = ...,
        model_parameters: ortools.service.v1.mathopt.model_parameters_pb2.ModelSolveParametersProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["model", b"model", "model_parameters", b"model_parameters", "parameters", b"parameters", "resources", b"resources"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["model", b"model", "model_parameters", b"model_parameters", "parameters", b"parameters", "resources", b"resources", "solver_type", b"solver_type"]) -> None: ...

global___SolveMathOptModelRequest = SolveMathOptModelRequest

@typing.final
class SolveMathOptModelResponse(google.protobuf.message.Message):
    """Response for a unary remote solve in MathOpt."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    MESSAGES_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> ortools.service.v1.mathopt.result_pb2.SolveResultProto:
        """Description of the output of solving the model in the request."""

    @property
    def messages(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """If SolveParametersProto.enable_output has been used, this will contain log
        messages for solvers that support message callbacks.
        """

    def __init__(
        self,
        *,
        result: ortools.service.v1.mathopt.result_pb2.SolveResultProto | None = ...,
        messages: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["result", b"result"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["messages", b"messages", "result", b"result"]) -> None: ...

global___SolveMathOptModelResponse = SolveMathOptModelResponse
