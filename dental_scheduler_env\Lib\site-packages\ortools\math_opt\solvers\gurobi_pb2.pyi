"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Proto messages specific to Gurobi.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class GurobiInitializerProto(google.protobuf.message.Message):
    """Parameters used to initialize the Gurobi solver."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class ISVKey(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        NAME_FIELD_NUMBER: builtins.int
        APPLICATION_NAME_FIELD_NUMBER: builtins.int
        EXPIRATION_FIELD_NUMBER: builtins.int
        KEY_FIELD_NUMBER: builtins.int
        name: builtins.str
        application_name: builtins.str
        expiration: builtins.int
        key: builtins.str
        def __init__(
            self,
            *,
            name: builtins.str = ...,
            application_name: builtins.str = ...,
            expiration: builtins.int = ...,
            key: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["application_name", b"application_name", "expiration", b"expiration", "key", b"key", "name", b"name"]) -> None: ...

    ISV_KEY_FIELD_NUMBER: builtins.int
    @property
    def isv_key(self) -> global___GurobiInitializerProto.ISVKey:
        """An optional ISV key to use.

        See http://www.gurobi.com/products/licensing-pricing/isv-program.
        """

    def __init__(
        self,
        *,
        isv_key: global___GurobiInitializerProto.ISVKey | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["isv_key", b"isv_key"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["isv_key", b"isv_key"]) -> None: ...

global___GurobiInitializerProto = GurobiInitializerProto

@typing.final
class GurobiParametersProto(google.protobuf.message.Message):
    """Gurobi specific parameters for solving. See
      https://www.gurobi.com/documentation/9.1/refman/parameters.html
    for a list of possible parameters.

    Example text proto to set the Barrier Iteration Limit:
      parameters : [{name: "BarIterLimit" value: "10}]

    With Gurobi, the order that parameters are applied can have an impact in rare
    situations. Parameters are applied in the following order:
     * LogToConsole is set from CommonSolveParameters.enable_output.
     * Any common parameters not overwritten by GurobiParameters.
     * param_values in iteration order (insertion order).
    We set LogToConsole first because setting other parameters can generate
    output.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class Parameter(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        NAME_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        name: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            name: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["name", b"name", "value", b"value"]) -> None: ...

    PARAMETERS_FIELD_NUMBER: builtins.int
    @property
    def parameters(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___GurobiParametersProto.Parameter]: ...
    def __init__(
        self,
        *,
        parameters: collections.abc.Iterable[global___GurobiParametersProto.Parameter] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["parameters", b"parameters"]) -> None: ...

global___GurobiParametersProto = GurobiParametersProto
