"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class OsqpSettingsProto(google.protobuf.message.Message):
    """This proto mirrors the fields of OsqpSettings in
    osqp_cpp/include/osqp++.h, which in turn (nearly) mirrors the
    fields of OSQPSettings in osqp/include/types.h. See also
    https://osqp.org/docs/interfaces/solver_settings.html for documentation and
    default values. This proto must be kept in sync with logic in osqp_solver.cc.
    LINT.IfChange
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RHO_FIELD_NUMBER: builtins.int
    SIGMA_FIELD_NUMBER: builtins.int
    SCALING_FIELD_NUMBER: builtins.int
    ADAPTIVE_RHO_FIELD_NUMBER: builtins.int
    ADAPTIVE_RHO_INTERVAL_FIELD_NUMBER: builtins.int
    ADAPTIVE_RHO_TOLERANCE_FIELD_NUMBER: builtins.int
    ADAPTIVE_RHO_FRACTION_FIELD_NUMBER: builtins.int
    MAX_ITER_FIELD_NUMBER: builtins.int
    EPS_ABS_FIELD_NUMBER: builtins.int
    EPS_REL_FIELD_NUMBER: builtins.int
    EPS_PRIM_INF_FIELD_NUMBER: builtins.int
    EPS_DUAL_INF_FIELD_NUMBER: builtins.int
    ALPHA_FIELD_NUMBER: builtins.int
    DELTA_FIELD_NUMBER: builtins.int
    POLISH_FIELD_NUMBER: builtins.int
    POLISH_REFINE_ITER_FIELD_NUMBER: builtins.int
    VERBOSE_FIELD_NUMBER: builtins.int
    SCALED_TERMINATION_FIELD_NUMBER: builtins.int
    CHECK_TERMINATION_FIELD_NUMBER: builtins.int
    WARM_START_FIELD_NUMBER: builtins.int
    TIME_LIMIT_FIELD_NUMBER: builtins.int
    rho: builtins.float
    """ADMM rho step. Must be > 0."""
    sigma: builtins.float
    """ADMM sigma step. Must be > 0."""
    scaling: builtins.int
    """Number of heuristic scaling iterations. Must be >= 0."""
    adaptive_rho: builtins.bool
    """Is rho step size adaptive?"""
    adaptive_rho_interval: builtins.int
    """Number of iterations between rho adaptations; if 0, then automatically
    selected. Must be >= 0.
    """
    adaptive_rho_tolerance: builtins.float
    """Tolerance X for adapting rho: The new value must be X times larger or 1/X
    times smaller than the current value. Must be >= 1.
    """
    adaptive_rho_fraction: builtins.float
    """In automatic mode (adaptive_rho_interval = 0), what fraction of setup time
    is spent on selecting rho. Must be >= 0.
    """
    max_iter: builtins.int
    """Maximum number of iterations. Must be > 0."""
    eps_abs: builtins.float
    """Absolute error tolerance for convergence. Must be >= 0."""
    eps_rel: builtins.float
    """Relative error tolerance for convergence. Must be >= 0."""
    eps_prim_inf: builtins.float
    """Absolute error tolerance for primal infeasibility. Must be >= 0."""
    eps_dual_inf: builtins.float
    """Relative error tolerance for dual infeasibility. Must be >= 0."""
    alpha: builtins.float
    """ADMM overrelaxation parameter. Must be > 0 and < 2."""
    delta: builtins.float
    """Polishing regularization parameter. Must be > 0."""
    polish: builtins.bool
    """Perform polishing?"""
    polish_refine_iter: builtins.int
    """Number of refinement iterations in polishing. Must be > 0."""
    verbose: builtins.bool
    """Print solver output?"""
    scaled_termination: builtins.bool
    """Use scaled termination criteria?"""
    check_termination: builtins.int
    """Interval for checking termination. If 0 or unset, termination checking is
    disabled. Must be >= 0.
    """
    warm_start: builtins.bool
    """Perform warm starting?."""
    time_limit: builtins.float
    """Run time limit in seconds. If 0 or unset, then no time limit. Must be >= 0."""
    def __init__(
        self,
        *,
        rho: builtins.float | None = ...,
        sigma: builtins.float | None = ...,
        scaling: builtins.int | None = ...,
        adaptive_rho: builtins.bool | None = ...,
        adaptive_rho_interval: builtins.int | None = ...,
        adaptive_rho_tolerance: builtins.float | None = ...,
        adaptive_rho_fraction: builtins.float | None = ...,
        max_iter: builtins.int | None = ...,
        eps_abs: builtins.float | None = ...,
        eps_rel: builtins.float | None = ...,
        eps_prim_inf: builtins.float | None = ...,
        eps_dual_inf: builtins.float | None = ...,
        alpha: builtins.float | None = ...,
        delta: builtins.float | None = ...,
        polish: builtins.bool | None = ...,
        polish_refine_iter: builtins.int | None = ...,
        verbose: builtins.bool | None = ...,
        scaled_termination: builtins.bool | None = ...,
        check_termination: builtins.int | None = ...,
        warm_start: builtins.bool | None = ...,
        time_limit: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_adaptive_rho", b"_adaptive_rho", "_adaptive_rho_fraction", b"_adaptive_rho_fraction", "_adaptive_rho_interval", b"_adaptive_rho_interval", "_adaptive_rho_tolerance", b"_adaptive_rho_tolerance", "_alpha", b"_alpha", "_check_termination", b"_check_termination", "_delta", b"_delta", "_eps_abs", b"_eps_abs", "_eps_dual_inf", b"_eps_dual_inf", "_eps_prim_inf", b"_eps_prim_inf", "_eps_rel", b"_eps_rel", "_max_iter", b"_max_iter", "_polish", b"_polish", "_polish_refine_iter", b"_polish_refine_iter", "_rho", b"_rho", "_scaled_termination", b"_scaled_termination", "_scaling", b"_scaling", "_sigma", b"_sigma", "_time_limit", b"_time_limit", "_verbose", b"_verbose", "_warm_start", b"_warm_start", "adaptive_rho", b"adaptive_rho", "adaptive_rho_fraction", b"adaptive_rho_fraction", "adaptive_rho_interval", b"adaptive_rho_interval", "adaptive_rho_tolerance", b"adaptive_rho_tolerance", "alpha", b"alpha", "check_termination", b"check_termination", "delta", b"delta", "eps_abs", b"eps_abs", "eps_dual_inf", b"eps_dual_inf", "eps_prim_inf", b"eps_prim_inf", "eps_rel", b"eps_rel", "max_iter", b"max_iter", "polish", b"polish", "polish_refine_iter", b"polish_refine_iter", "rho", b"rho", "scaled_termination", b"scaled_termination", "scaling", b"scaling", "sigma", b"sigma", "time_limit", b"time_limit", "verbose", b"verbose", "warm_start", b"warm_start"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_adaptive_rho", b"_adaptive_rho", "_adaptive_rho_fraction", b"_adaptive_rho_fraction", "_adaptive_rho_interval", b"_adaptive_rho_interval", "_adaptive_rho_tolerance", b"_adaptive_rho_tolerance", "_alpha", b"_alpha", "_check_termination", b"_check_termination", "_delta", b"_delta", "_eps_abs", b"_eps_abs", "_eps_dual_inf", b"_eps_dual_inf", "_eps_prim_inf", b"_eps_prim_inf", "_eps_rel", b"_eps_rel", "_max_iter", b"_max_iter", "_polish", b"_polish", "_polish_refine_iter", b"_polish_refine_iter", "_rho", b"_rho", "_scaled_termination", b"_scaled_termination", "_scaling", b"_scaling", "_sigma", b"_sigma", "_time_limit", b"_time_limit", "_verbose", b"_verbose", "_warm_start", b"_warm_start", "adaptive_rho", b"adaptive_rho", "adaptive_rho_fraction", b"adaptive_rho_fraction", "adaptive_rho_interval", b"adaptive_rho_interval", "adaptive_rho_tolerance", b"adaptive_rho_tolerance", "alpha", b"alpha", "check_termination", b"check_termination", "delta", b"delta", "eps_abs", b"eps_abs", "eps_dual_inf", b"eps_dual_inf", "eps_prim_inf", b"eps_prim_inf", "eps_rel", b"eps_rel", "max_iter", b"max_iter", "polish", b"polish", "polish_refine_iter", b"polish_refine_iter", "rho", b"rho", "scaled_termination", b"scaled_termination", "scaling", b"scaling", "sigma", b"sigma", "time_limit", b"time_limit", "verbose", b"verbose", "warm_start", b"warm_start"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_adaptive_rho", b"_adaptive_rho"]) -> typing.Literal["adaptive_rho"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_adaptive_rho_fraction", b"_adaptive_rho_fraction"]) -> typing.Literal["adaptive_rho_fraction"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_adaptive_rho_interval", b"_adaptive_rho_interval"]) -> typing.Literal["adaptive_rho_interval"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_adaptive_rho_tolerance", b"_adaptive_rho_tolerance"]) -> typing.Literal["adaptive_rho_tolerance"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_alpha", b"_alpha"]) -> typing.Literal["alpha"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_check_termination", b"_check_termination"]) -> typing.Literal["check_termination"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_delta", b"_delta"]) -> typing.Literal["delta"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_eps_abs", b"_eps_abs"]) -> typing.Literal["eps_abs"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_eps_dual_inf", b"_eps_dual_inf"]) -> typing.Literal["eps_dual_inf"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_eps_prim_inf", b"_eps_prim_inf"]) -> typing.Literal["eps_prim_inf"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_eps_rel", b"_eps_rel"]) -> typing.Literal["eps_rel"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_max_iter", b"_max_iter"]) -> typing.Literal["max_iter"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_polish", b"_polish"]) -> typing.Literal["polish"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_polish_refine_iter", b"_polish_refine_iter"]) -> typing.Literal["polish_refine_iter"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_rho", b"_rho"]) -> typing.Literal["rho"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_scaled_termination", b"_scaled_termination"]) -> typing.Literal["scaled_termination"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_scaling", b"_scaling"]) -> typing.Literal["scaling"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_sigma", b"_sigma"]) -> typing.Literal["sigma"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_time_limit", b"_time_limit"]) -> typing.Literal["time_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_verbose", b"_verbose"]) -> typing.Literal["verbose"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_warm_start", b"_warm_start"]) -> typing.Literal["warm_start"] | None: ...

global___OsqpSettingsProto = OsqpSettingsProto

@typing.final
class OsqpOutput(google.protobuf.message.Message):
    """Solver-specific output for OSQP."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    INITIALIZED_UNDERLYING_SOLVER_FIELD_NUMBER: builtins.int
    initialized_underlying_solver: builtins.bool
    """Field is true if the underlying OSQP++ object was initialized for the
    current solve, and false if the object was instead used incrementally. In
    more detail, this tracks: was osqp::OsqpSolver::Init called on the
    operations_research::math_opt::OsqpSolver::solver_ field at any point
    during the solve process?
    """
    def __init__(
        self,
        *,
        initialized_underlying_solver: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["initialized_underlying_solver", b"initialized_underlying_solver"]) -> None: ...

global___OsqpOutput = OsqpOutput
