# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/callback.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/callback.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from ortools.math_opt import sparse_containers_pb2 as ortools_dot_math__opt_dot_sparse__containers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fortools/math_opt/callback.proto\x12\x1coperations_research.math_opt\x1a\x1egoogle/protobuf/duration.proto\x1a(ortools/math_opt/sparse_containers.proto\"\xe6\r\n\x11\x43\x61llbackDataProto\x12?\n\x05\x65vent\x18\x01 \x01(\x0e\x32\x30.operations_research.math_opt.CallbackEventProto\x12U\n\x16primal_solution_vector\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12*\n\x07runtime\x18\x03 \x01(\x0b\x32\x19.google.protobuf.Duration\x12U\n\x0epresolve_stats\x18\x04 \x01(\x0b\x32=.operations_research.math_opt.CallbackDataProto.PresolveStats\x12S\n\rsimplex_stats\x18\x05 \x01(\x0b\x32<.operations_research.math_opt.CallbackDataProto.SimplexStats\x12S\n\rbarrier_stats\x18\x06 \x01(\x0b\x32<.operations_research.math_opt.CallbackDataProto.BarrierStats\x12K\n\tmip_stats\x18\x07 \x01(\x0b\x32\x38.operations_research.math_opt.CallbackDataProto.MipStats\x1a\xe7\x01\n\rPresolveStats\x12\x1e\n\x11removed_variables\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12 \n\x13removed_constraints\x18\x02 \x01(\x03H\x01\x88\x01\x01\x12\x1a\n\rbound_changes\x18\x03 \x01(\x03H\x02\x88\x01\x01\x12 \n\x13\x63oefficient_changes\x18\x04 \x01(\x03H\x03\x88\x01\x01\x42\x14\n\x12_removed_variablesB\x16\n\x14_removed_constraintsB\x10\n\x0e_bound_changesB\x16\n\x14_coefficient_changes\x1a\x94\x02\n\x0cSimplexStats\x12\x1c\n\x0fiteration_count\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x1c\n\x0fobjective_value\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12!\n\x14primal_infeasibility\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x1f\n\x12\x64ual_infeasibility\x18\x04 \x01(\x01H\x03\x88\x01\x01\x12\x1a\n\ris_pertubated\x18\x05 \x01(\x08H\x04\x88\x01\x01\x42\x12\n\x10_iteration_countB\x12\n\x10_objective_valueB\x17\n\x15_primal_infeasibilityB\x15\n\x13_dual_infeasibilityB\x10\n\x0e_is_pertubated\x1a\xca\x02\n\x0c\x42\x61rrierStats\x12\x1c\n\x0fiteration_count\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x1d\n\x10primal_objective\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x1b\n\x0e\x64ual_objective\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x1c\n\x0f\x63omplementarity\x18\x04 \x01(\x01H\x03\x88\x01\x01\x12!\n\x14primal_infeasibility\x18\x05 \x01(\x01H\x04\x88\x01\x01\x12\x1f\n\x12\x64ual_infeasibility\x18\x06 \x01(\x01H\x05\x88\x01\x01\x42\x12\n\x10_iteration_countB\x13\n\x11_primal_objectiveB\x11\n\x0f_dual_objectiveB\x12\n\x10_complementarityB\x17\n\x15_primal_infeasibilityB\x15\n\x13_dual_infeasibility\x1a\xf0\x02\n\x08MipStats\x12\x19\n\x0cprimal_bound\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x17\n\ndual_bound\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12\x1b\n\x0e\x65xplored_nodes\x18\x03 \x01(\x03H\x02\x88\x01\x01\x12\x17\n\nopen_nodes\x18\x04 \x01(\x03H\x03\x88\x01\x01\x12\x1f\n\x12simplex_iterations\x18\x05 \x01(\x03H\x04\x88\x01\x01\x12&\n\x19number_of_solutions_found\x18\x06 \x01(\x05H\x05\x88\x01\x01\x12!\n\x14\x63utting_planes_in_lp\x18\x07 \x01(\x05H\x06\x88\x01\x01\x42\x0f\n\r_primal_boundB\r\n\x0b_dual_boundB\x11\n\x0f_explored_nodesB\r\n\x0b_open_nodesB\x15\n\x13_simplex_iterationsB\x1c\n\x1a_number_of_solutions_foundB\x17\n\x15_cutting_planes_in_lp\"\x82\x03\n\x13\x43\x61llbackResultProto\x12\x11\n\tterminate\x18\x01 \x01(\x08\x12Y\n\x04\x63uts\x18\x04 \x03(\x0b\x32K.operations_research.math_opt.CallbackResultProto.GeneratedLinearConstraint\x12R\n\x13suggested_solutions\x18\x05 \x03(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x1a\xa8\x01\n\x19GeneratedLinearConstraint\x12P\n\x11linear_expression\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12\x13\n\x0blower_bound\x18\x02 \x01(\x01\x12\x13\n\x0bupper_bound\x18\x03 \x01(\x01\x12\x0f\n\x07is_lazy\x18\x04 \x01(\x08\"\xbf\x02\n\x19\x43\x61llbackRegistrationProto\x12N\n\x14request_registration\x18\x01 \x03(\x0e\x32\x30.operations_research.math_opt.CallbackEventProto\x12R\n\x13mip_solution_filter\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseVectorFilterProto\x12N\n\x0fmip_node_filter\x18\x03 \x01(\x0b\x32\x35.operations_research.math_opt.SparseVectorFilterProto\x12\x10\n\x08\x61\x64\x64_cuts\x18\x04 \x01(\x08\x12\x1c\n\x14\x61\x64\x64_lazy_constraints\x18\x05 \x01(\x08*\xdf\x01\n\x12\x43\x61llbackEventProto\x12\x1e\n\x1a\x43\x41LLBACK_EVENT_UNSPECIFIED\x10\x00\x12\x1b\n\x17\x43\x41LLBACK_EVENT_PRESOLVE\x10\x01\x12\x1a\n\x16\x43\x41LLBACK_EVENT_SIMPLEX\x10\x02\x12\x16\n\x12\x43\x41LLBACK_EVENT_MIP\x10\x03\x12\x1f\n\x1b\x43\x41LLBACK_EVENT_MIP_SOLUTION\x10\x04\x12\x1b\n\x17\x43\x41LLBACK_EVENT_MIP_NODE\x10\x05\x12\x1a\n\x16\x43\x41LLBACK_EVENT_BARRIER\x10\x06\x42\x1e\n\x1a\x63om.google.ortools.mathoptP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.callback_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.ortools.mathoptP\001'
  _globals['_CALLBACKEVENTPROTO']._serialized_start=2620
  _globals['_CALLBACKEVENTPROTO']._serialized_end=2843
  _globals['_CALLBACKDATAPROTO']._serialized_start=140
  _globals['_CALLBACKDATAPROTO']._serialized_end=1906
  _globals['_CALLBACKDATAPROTO_PRESOLVESTATS']._serialized_start=692
  _globals['_CALLBACKDATAPROTO_PRESOLVESTATS']._serialized_end=923
  _globals['_CALLBACKDATAPROTO_SIMPLEXSTATS']._serialized_start=926
  _globals['_CALLBACKDATAPROTO_SIMPLEXSTATS']._serialized_end=1202
  _globals['_CALLBACKDATAPROTO_BARRIERSTATS']._serialized_start=1205
  _globals['_CALLBACKDATAPROTO_BARRIERSTATS']._serialized_end=1535
  _globals['_CALLBACKDATAPROTO_MIPSTATS']._serialized_start=1538
  _globals['_CALLBACKDATAPROTO_MIPSTATS']._serialized_end=1906
  _globals['_CALLBACKRESULTPROTO']._serialized_start=1909
  _globals['_CALLBACKRESULTPROTO']._serialized_end=2295
  _globals['_CALLBACKRESULTPROTO_GENERATEDLINEARCONSTRAINT']._serialized_start=2127
  _globals['_CALLBACKRESULTPROTO_GENERATEDLINEARCONSTRAINT']._serialized_end=2295
  _globals['_CALLBACKREGISTRATIONPROTO']._serialized_start=2298
  _globals['_CALLBACKREGISTRATIONPROTO']._serialized_end=2617
# @@protoc_insertion_point(module_scope)
