# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/solvers/glpk.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/solvers/glpk.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#ortools/math_opt/solvers/glpk.proto\x12\x1coperations_research.math_opt\"i\n\x13GlpkParametersProto\x12-\n compute_unbound_rays_if_possible\x18\x01 \x01(\x08H\x00\x88\x01\x01\x42#\n!_compute_unbound_rays_if_possibleb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.solvers.glpk_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_GLPKPARAMETERSPROTO']._serialized_start=69
  _globals['_GLPKPARAMETERSPROTO']._serialized_end=174
# @@protoc_insertion_point(module_scope)
