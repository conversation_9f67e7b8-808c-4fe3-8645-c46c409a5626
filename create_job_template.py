"""
Sc<PERSON>t to create an Excel template for job uploads
"""
import pandas as pd
from datetime import datetime, timedelta
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

def create_job_upload_template():
    """Create an Excel template for uploading jobs"""
    
    # Create sample data structure
    today = datetime.now().date()
    
    # Sample jobs data
    sample_jobs = [
        {
            'Job_ID': 'JOB001',
            'Job_Name': 'Website Redesign',
            'Description': 'Complete redesign of company website',
            'Received_Date': today.strftime('%Y-%m-%d'),
            'Due_Date': (today + timedelta(days=3)).strftime('%Y-%m-%d'),
            'Priority': 'High',
            'Red_Team_Hours': 4,
            'Red_Team_Description': 'UI/UX Design and mockups',
            'Blue_Team_Hours': 6,
            'Blue_Team_Description': 'Frontend development and testing',
            'Green_Team_Hours': 3,
            'Green_Team_Description': 'Backend integration and deployment'
        },
        {
            'Job_ID': 'JOB002',
            'Job_Name': 'Database Migration',
            'Description': 'Migrate legacy database to new system',
            'Received_Date': today.strftime('%Y-%m-%d'),
            'Due_Date': (today + timedelta(days=2)).strftime('%Y-%m-%d'),
            'Priority': 'Medium',
            'Red_Team_Hours': 2,
            'Red_Team_Description': 'Data analysis and planning',
            'Blue_Team_Hours': 4,
            'Blue_Team_Description': 'Database setup and configuration',
            'Green_Team_Hours': 5,
            'Green_Team_Description': 'Data migration and validation'
        },
        {
            'Job_ID': 'JOB003',
            'Job_Name': 'Mobile App Update',
            'Description': 'Add new features to mobile application',
            'Received_Date': today.strftime('%Y-%m-%d'),
            'Due_Date': (today + timedelta(days=3)).strftime('%Y-%m-%d'),
            'Priority': 'Low',
            'Red_Team_Hours': 3,
            'Red_Team_Description': 'Feature design and wireframes',
            'Blue_Team_Hours': 2,
            'Blue_Team_Description': 'Mobile UI implementation',
            'Green_Team_Hours': 4,
            'Green_Team_Description': 'API development and testing'
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(sample_jobs)
    
    # Create Excel workbook
    wb = openpyxl.Workbook()
    
    # Remove default sheet and create our sheets
    wb.remove(wb.active)
    
    # Create Jobs sheet
    jobs_sheet = wb.create_sheet("Jobs")
    
    # Add data to Jobs sheet
    for r in dataframe_to_rows(df, index=False, header=True):
        jobs_sheet.append(r)
    
    # Style the Jobs sheet
    style_jobs_sheet(jobs_sheet)
    
    # Create Instructions sheet
    instructions_sheet = wb.create_sheet("Instructions")
    create_instructions_sheet(instructions_sheet)
    
    # Create Team Info sheet
    team_info_sheet = wb.create_sheet("Team_Info")
    create_team_info_sheet(team_info_sheet)
    
    # Create Validation sheet for dropdowns
    validation_sheet = wb.create_sheet("Validation")
    create_validation_sheet(validation_sheet)
    
    # Add data validation to Jobs sheet
    add_data_validation(jobs_sheet, validation_sheet)
    
    # Save the workbook
    wb.save("Job_Upload_Template.xlsx")
    print("Excel template 'Job_Upload_Template.xlsx' created successfully!")

def style_jobs_sheet(sheet):
    """Apply styling to the Jobs sheet"""
    
    # Header styling
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Apply header styling
    for cell in sheet[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Auto-adjust column widths
    for column in sheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        sheet.column_dimensions[column_letter].width = adjusted_width
    
    # Add borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in sheet.iter_rows():
        for cell in row:
            cell.border = thin_border

def create_instructions_sheet(sheet):
    """Create instructions sheet with usage guidelines"""
    
    instructions = [
        ["Job Upload Template - Instructions", ""],
        ["", ""],
        ["Overview:", ""],
        ["This template is used to upload job information for the team scheduling system.", ""],
        ["", ""],
        ["Required Fields:", ""],
        ["• Job_ID: Unique identifier for the job (e.g., JOB001)", ""],
        ["• Job_Name: Descriptive name for the job", ""],
        ["• Description: Detailed description of the job requirements", ""],
        ["• Received_Date: Date job was received (YYYY-MM-DD format)", ""],
        ["• Due_Date: Date job must be completed (YYYY-MM-DD format)", ""],
        ["• Priority: High, Medium, or Low", ""],
        ["• Red_Team_Hours: Hours required from Red team (1-8)", ""],
        ["• Red_Team_Description: Description of Red team tasks", ""],
        ["• Blue_Team_Hours: Hours required from Blue team (1-8)", ""],
        ["• Blue_Team_Description: Description of Blue team tasks", ""],
        ["• Green_Team_Hours: Hours required from Green team (1-8)", ""],
        ["• Green_Team_Description: Description of Green team tasks", ""],
        ["", ""],
        ["Team Information:", ""],
        ["• Red Team: John, Adam (Design & Planning)", ""],
        ["• Blue Team: Bob, Fred, Ted (Development & Implementation)", ""],
        ["• Green Team: Jane, Bea (Testing & Deployment)", ""],
        ["", ""],
        ["Constraints:", ""],
        ["• Each employee works maximum 8 hours per day", ""],
        ["• Working hours: 9:00 AM to 5:00 PM", ""],
        ["• Tasks must be completed within the same day", ""],
        ["• Jobs can span multiple days", ""],
        ["• All jobs must be completed before their due date", ""],
        ["", ""],
        ["Notes:", ""],
        ["• Use the dropdown menus for Priority field", ""],
        ["• Dates must be in YYYY-MM-DD format", ""],
        ["• Hours must be between 1 and 8 for each team", ""],
        ["• Each job must have tasks for all three teams", ""],
        ["• Job_ID must be unique across all jobs", ""]
    ]
    
    for row_idx, (col1, col2) in enumerate(instructions, 1):
        sheet.cell(row=row_idx, column=1, value=col1)
        sheet.cell(row=row_idx, column=2, value=col2)
    
    # Style the instructions
    sheet.cell(row=1, column=1).font = Font(bold=True, size=16)
    sheet.column_dimensions['A'].width = 50
    sheet.column_dimensions['B'].width = 20

def create_team_info_sheet(sheet):
    """Create team information sheet"""
    
    team_info = [
        ["Team", "Employees", "Specialization", "Capacity (hours/day)"],
        ["Red", "John, Adam", "Design & Planning", "16"],
        ["Blue", "Bob, Fred, Ted", "Development & Implementation", "24"],
        ["Green", "Jane, Bea", "Testing & Deployment", "16"],
        ["", "", "", ""],
        ["Total Daily Capacity", "", "", "56 hours"],
        ["", "", "", ""],
        ["Employee Details:", "", "", ""],
        ["John", "Red Team", "Senior Designer", "8"],
        ["Adam", "Red Team", "Project Planner", "8"],
        ["Bob", "Blue Team", "Lead Developer", "8"],
        ["Fred", "Blue Team", "Frontend Developer", "8"],
        ["Ted", "Blue Team", "Backend Developer", "8"],
        ["Jane", "Green Team", "QA Tester", "8"],
        ["Bea", "Green Team", "DevOps Engineer", "8"]
    ]
    
    for row_idx, row_data in enumerate(team_info, 1):
        for col_idx, value in enumerate(row_data, 1):
            sheet.cell(row=row_idx, column=col_idx, value=value)
    
    # Style the header
    for cell in sheet[1]:
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    # Auto-adjust column widths
    for column in sheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = max_length + 2
        sheet.column_dimensions[column_letter].width = adjusted_width

def create_validation_sheet(sheet):
    """Create validation data for dropdowns"""
    
    # Priority options
    priorities = ["High", "Medium", "Low"]
    
    for idx, priority in enumerate(priorities, 1):
        sheet.cell(row=idx, column=1, value=priority)
    
    # Name the range for validation
    sheet.title = "Validation"

def add_data_validation(jobs_sheet, validation_sheet):
    """Add data validation to the Jobs sheet"""
    
    from openpyxl.worksheet.datavalidation import DataValidation
    
    # Priority validation
    priority_validation = DataValidation(
        type="list",
        formula1="Validation!$A$1:$A$3",
        showDropDown=True
    )
    priority_validation.error = "Please select from the dropdown list"
    priority_validation.errorTitle = "Invalid Priority"
    
    # Apply to Priority column (column F, starting from row 2)
    jobs_sheet.add_data_validation(priority_validation)
    priority_validation.add("F2:F1000")  # Apply to many rows for future entries

if __name__ == "__main__":
    create_job_upload_template()
