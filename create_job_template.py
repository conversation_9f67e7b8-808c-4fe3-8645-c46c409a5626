"""
<PERSON><PERSON><PERSON> to create an Excel template for job uploads
"""
import pandas as pd
from datetime import datetime, timedelta
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

def create_job_upload_template():
    """Create an Excel template for dental implant manufacturing jobs"""

    # Create sample data structure
    today = datetime.now().date()

    # Define job types for dental implant manufacturing
    job_types = {
        'Standard_Implant': {
            'description': 'Standard titanium dental implant',
            'steps': [
                ('Red', 2, 'Design review and CAD modeling'),
                ('Blue', 4, 'CNC machining and surface treatment'),
                ('Green', 2, 'Quality control and sterilization')
            ],
            'lead_time_days': 3
        },
        'Custom_Implant': {
            'description': 'Custom patient-specific implant',
            'steps': [
                ('Red', 4, 'Patient scan analysis and custom design'),
                ('Blue', 6, 'Precision manufacturing and coating'),
                ('Green', 3, 'Dimensional inspection and packaging')
            ],
            'lead_time_days': 5
        },
        'Abutment_Set': {
            'description': 'Implant abutment and crown set',
            'steps': [
                ('Red', 3, 'Prosthetic design and material selection'),
                ('Blue', 3, 'Abutment fabrication and crown creation'),
                ('Green', 2, 'Final assembly and quality verification')
            ],
            'lead_time_days': 4
        }
    }

    # Generate 20 dental implant manufacturing jobs
    sample_jobs = []
    job_counter = 1

    # Distribute jobs across the three types
    jobs_per_type = [7, 7, 6]  # 20 total jobs
    type_names = list(job_types.keys())

    for type_idx, (job_type, count) in enumerate(zip(type_names, jobs_per_type)):
        type_info = job_types[job_type]

        for i in range(count):
            # Create patient/case identifiers
            patient_id = f"P{job_counter:03d}"
            case_variations = {
                'Standard_Implant': ['Molar replacement', 'Premolar replacement', 'Incisor replacement', 'Canine replacement'],
                'Custom_Implant': ['Complex case', 'Bone graft case', 'Immediate placement', 'Revision case'],
                'Abutment_Set': ['Anterior crown', 'Posterior crown', 'Bridge component', 'Multi-unit set']
            }

            variation = case_variations[job_type][i % len(case_variations[job_type])]

            # Calculate due date with some variation
            base_days = type_info['lead_time_days']
            due_days = base_days + (i % 3)  # Add 0-2 days variation

            # Priority based on case type and urgency
            if 'Immediate' in variation or 'Complex' in variation:
                priority = 'High'
            elif 'Revision' in variation or 'Bridge' in variation:
                priority = 'Medium'
            else:
                priority = 'Low'

            job = {
                'Job_ID': f"DI{job_counter:03d}",
                'Job_Name': f"{job_type.replace('_', ' ')} - {patient_id}",
                'Job_Type': job_type,
                'Description': f"{type_info['description']} - {variation} for patient {patient_id}",
                'Patient_ID': patient_id,
                'Received_Date': today.strftime('%Y-%m-%d'),
                'Due_Date': (today + timedelta(days=due_days)).strftime('%Y-%m-%d'),
                'Priority': priority,
                'Step_Sequence': ' → '.join([step[0] for step in type_info['steps']])
            }

            # Add team-specific information
            for step_idx, (team, hours, description) in enumerate(type_info['steps']):
                team_key = f"{team}_Team"
                job[f"{team_key}_Hours"] = hours
                job[f"{team_key}_Description"] = f"Step {step_idx + 1}: {description}"
                job[f"{team_key}_Step_Order"] = step_idx + 1

            sample_jobs.append(job)
            job_counter += 1
    
    # Create DataFrame
    df = pd.DataFrame(sample_jobs)
    
    # Create Excel workbook
    wb = openpyxl.Workbook()
    
    # Remove default sheet and create our sheets
    wb.remove(wb.active)
    
    # Create Jobs sheet
    jobs_sheet = wb.create_sheet("Jobs")
    
    # Add data to Jobs sheet
    for r in dataframe_to_rows(df, index=False, header=True):
        jobs_sheet.append(r)
    
    # Style the Jobs sheet
    style_jobs_sheet(jobs_sheet)
    
    # Create Instructions sheet
    instructions_sheet = wb.create_sheet("Instructions")
    create_instructions_sheet(instructions_sheet)
    
    # Create Team Info sheet
    team_info_sheet = wb.create_sheet("Team_Info")
    create_team_info_sheet(team_info_sheet)
    
    # Create Validation sheet for dropdowns
    validation_sheet = wb.create_sheet("Validation")
    create_validation_sheet(validation_sheet)
    
    # Add data validation to Jobs sheet
    add_data_validation(jobs_sheet, validation_sheet)
    
    # Save the workbook
    wb.save("Job_Upload_Template.xlsx")
    print("Excel template 'Job_Upload_Template.xlsx' created successfully!")

def style_jobs_sheet(sheet):
    """Apply styling to the Jobs sheet"""
    
    # Header styling
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_alignment = Alignment(horizontal="center", vertical="center")
    
    # Apply header styling
    for cell in sheet[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # Auto-adjust column widths
    for column in sheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        sheet.column_dimensions[column_letter].width = adjusted_width
    
    # Add borders
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in sheet.iter_rows():
        for cell in row:
            cell.border = thin_border

def create_instructions_sheet(sheet):
    """Create instructions sheet with usage guidelines"""

    instructions = [
        ["Dental Implant Manufacturing - Job Upload Template", ""],
        ["", ""],
        ["Overview:", ""],
        ["This template manages dental implant manufacturing jobs across three", ""],
        ["specialized teams with sequential workflow requirements.", ""],
        ["", ""],
        ["Job Types:", ""],
        ["• Standard_Implant: Basic titanium implants (Red→Blue→Green, 3-4 days)", ""],
        ["• Custom_Implant: Patient-specific implants (Red→Blue→Green, 5-6 days)", ""],
        ["• Abutment_Set: Implant abutments and crowns (Red→Blue→Green, 4-5 days)", ""],
        ["", ""],
        ["Required Fields:", ""],
        ["• Job_ID: Unique identifier (e.g., DI001)", ""],
        ["• Job_Name: Descriptive name including patient ID", ""],
        ["• Job_Type: Standard_Implant, Custom_Implant, or Abutment_Set", ""],
        ["• Description: Detailed case description", ""],
        ["• Patient_ID: Patient identifier (e.g., P001)", ""],
        ["• Received_Date: Date order received (YYYY-MM-DD)", ""],
        ["• Due_Date: Required completion date (YYYY-MM-DD)", ""],
        ["• Priority: High (urgent), Medium (standard), Low (routine)", ""],
        ["• Step_Sequence: Shows required team sequence (Red→Blue→Green)", ""],
        ["• Team Hours & Descriptions: Hours and tasks for each team step", ""],
        ["• Step_Order: Sequential order (1, 2, 3)", ""],
        ["", ""],
        ["Team Specializations:", ""],
        ["• Red Team: John, Adam (Design & CAD Engineering)", ""],
        ["  - Patient scan analysis, CAD modeling, prosthetic design", ""],
        ["• Blue Team: Bob, Fred, Ted (Manufacturing & Production)", ""],
        ["  - CNC machining, surface treatment, fabrication, coating", ""],
        ["• Green Team: Jane, Bea (Quality Control & Finishing)", ""],
        ["  - Inspection, sterilization, packaging, final assembly", ""],
        ["", ""],
        ["Workflow Constraints:", ""],
        ["• Sequential Processing: Red team must complete before Blue team", ""],
        ["• Blue team must complete before Green team", ""],
        ["• Each employee works maximum 8 hours per day", ""],
        ["• Working hours: 9:00 AM to 5:00 PM", ""],
        ["• Individual steps must complete within the same day", ""],
        ["• Jobs can span multiple days between steps", ""],
        ["• All jobs must meet patient delivery deadlines", ""],
        ["", ""],
        ["Quality Standards:", ""],
        ["• All implants must meet FDA Class II medical device standards", ""],
        ["• Custom implants require additional design validation", ""],
        ["• Sterilization and packaging per ISO 13485 requirements", ""],
        ["", ""],
        ["Notes:", ""],
        ["• Priority affects scheduling order within each team", ""],
        ["• Patient_ID links to clinical records system", ""],
        ["• Step sequences are predefined by job type", ""],
        ["• Hours reflect typical processing times per case", ""],
        ["• Emergency cases may require expedited processing", ""]
    ]
    
    for row_idx, (col1, col2) in enumerate(instructions, 1):
        sheet.cell(row=row_idx, column=1, value=col1)
        sheet.cell(row=row_idx, column=2, value=col2)
    
    # Style the instructions
    sheet.cell(row=1, column=1).font = Font(bold=True, size=16)
    sheet.column_dimensions['A'].width = 50
    sheet.column_dimensions['B'].width = 20

def create_team_info_sheet(sheet):
    """Create team information sheet for dental implant manufacturing"""

    team_info = [
        ["Team", "Employees", "Specialization", "Capacity (hours/day)", "Equipment"],
        ["Red", "John, Adam", "Design & CAD Engineering", "16", "CAD Workstations"],
        ["Blue", "Bob, Fred, Ted", "Manufacturing & Production", "24", "CNC Machines, Coating"],
        ["Green", "Jane, Bea", "Quality Control & Finishing", "16", "Inspection, Sterilization"],
        ["", "", "", "", ""],
        ["Total Daily Capacity", "", "", "56 hours", ""],
        ["", "", "", "", ""],
        ["Detailed Team Responsibilities:", "", "", "", ""],
        ["", "", "", "", ""],
        ["RED TEAM - Design & CAD Engineering:", "", "", "", ""],
        ["John", "Senior CAD Engineer", "Patient scan analysis, custom design", "8", "CAD/CAM Software"],
        ["Adam", "Design Specialist", "Prosthetic design, material selection", "8", "3D Modeling Tools"],
        ["", "", "", "", ""],
        ["BLUE TEAM - Manufacturing & Production:", "", "", "", ""],
        ["Bob", "CNC Machinist", "Precision machining, surface treatment", "8", "5-Axis CNC"],
        ["Fred", "Manufacturing Tech", "Implant fabrication, coating application", "8", "Coating Chamber"],
        ["Ted", "Production Specialist", "Abutment creation, assembly", "8", "Precision Tools"],
        ["", "", "", "", ""],
        ["GREEN TEAM - Quality Control & Finishing:", "", "", "", ""],
        ["Jane", "Quality Inspector", "Dimensional inspection, compliance", "8", "CMM, Microscopes"],
        ["Bea", "Sterilization Tech", "Sterilization, packaging, documentation", "8", "Autoclave, Clean Room"],
        ["", "", "", "", ""],
        ["Workflow Sequence:", "", "", "", ""],
        ["Step 1", "Red Team", "Design and engineering phase", "", ""],
        ["Step 2", "Blue Team", "Manufacturing and production phase", "", ""],
        ["Step 3", "Green Team", "Quality control and finishing phase", "", ""],
        ["", "", "", "", ""],
        ["Job Type Processing Times:", "", "", "", ""],
        ["Standard Implant", "Red: 2h, Blue: 4h, Green: 2h", "Total: 8 hours", "3-4 days", ""],
        ["Custom Implant", "Red: 4h, Blue: 6h, Green: 3h", "Total: 13 hours", "5-6 days", ""],
        ["Abutment Set", "Red: 3h, Blue: 3h, Green: 2h", "Total: 8 hours", "4-5 days", ""]
    ]
    
    for row_idx, row_data in enumerate(team_info, 1):
        for col_idx, value in enumerate(row_data, 1):
            sheet.cell(row=row_idx, column=col_idx, value=value)
    
    # Style the header
    for cell in sheet[1]:
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
    
    # Auto-adjust column widths
    for column in sheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = max_length + 2
        sheet.column_dimensions[column_letter].width = adjusted_width

def create_validation_sheet(sheet):
    """Create validation data for dropdowns"""

    # Priority options (Column A)
    priorities = ["High", "Medium", "Low"]
    for idx, priority in enumerate(priorities, 1):
        sheet.cell(row=idx, column=1, value=priority)

    # Job Type options (Column B)
    job_types = ["Standard_Implant", "Custom_Implant", "Abutment_Set"]
    for idx, job_type in enumerate(job_types, 1):
        sheet.cell(row=idx, column=2, value=job_type)

    # Add headers for clarity
    sheet.cell(row=1, column=3, value="Priority_Options")
    sheet.cell(row=1, column=4, value="Job_Type_Options")

    # Name the range for validation
    sheet.title = "Validation"

def add_data_validation(jobs_sheet, validation_sheet):
    """Add data validation to the Jobs sheet"""

    from openpyxl.worksheet.datavalidation import DataValidation

    # Find column indices for validation
    header_row = list(jobs_sheet[1])
    priority_col = None
    job_type_col = None

    for idx, cell in enumerate(header_row, 1):
        if cell.value == "Priority":
            priority_col = idx
        elif cell.value == "Job_Type":
            job_type_col = idx

    # Priority validation
    if priority_col:
        priority_validation = DataValidation(
            type="list",
            formula1="Validation!$A$1:$A$3",
            showDropDown=True
        )
        priority_validation.error = "Please select: High, Medium, or Low"
        priority_validation.errorTitle = "Invalid Priority"

        jobs_sheet.add_data_validation(priority_validation)
        priority_validation.add(f"{chr(64 + priority_col)}2:{chr(64 + priority_col)}1000")

    # Job Type validation
    if job_type_col:
        job_type_validation = DataValidation(
            type="list",
            formula1="Validation!$B$1:$B$3",
            showDropDown=True
        )
        job_type_validation.error = "Please select: Standard_Implant, Custom_Implant, or Abutment_Set"
        job_type_validation.errorTitle = "Invalid Job Type"

        jobs_sheet.add_data_validation(job_type_validation)
        job_type_validation.add(f"{chr(64 + job_type_col)}2:{chr(64 + job_type_col)}1000")

if __name__ == "__main__":
    create_job_upload_template()
