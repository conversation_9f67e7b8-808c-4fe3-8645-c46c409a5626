import numpy
from typing import ClassVar

class SimpleLinearSumAssignment:
    class Status:
        __members__: ClassVar[dict] = ...  # read-only
        INFEASIBLE: ClassVar[SimpleLinearSumAssignment.Status] = ...
        OPTIMAL: ClassVar[SimpleLinearSumAssignment.Status] = ...
        POSSIBLE_OVERFLOW: ClassVar[SimpleLinearSumAssignment.Status] = ...
        __entries: ClassVar[dict] = ...
        def __init__(self, value: int) -> None: ...
        def __eq__(self, other: object) -> bool: ...
        def __hash__(self) -> int: ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...
        def __ne__(self, other: object) -> bool: ...
        @property
        def name(self) -> str: ...
        @property
        def value(self) -> int: ...
    INFEASIBLE: ClassVar[SimpleLinearSumAssignment.Status] = ...
    OPTIMAL: ClassVar[SimpleLinearSumAssignment.Status] = ...
    POSSIBLE_OVERFLOW: ClassVar[SimpleLinearSumAssignment.Status] = ...
    def __init__(self) -> None: ...
    def add_arc_with_cost(self, left_node: int, right_node: int, cost: int) -> int: ...
    def add_arcs_with_cost(self, arg0: numpy.ndarray[numpy.int32], arg1: numpy.ndarray[numpy.int32], arg2: numpy.ndarray[numpy.int64]) -> object: ...
    def assignment_cost(self, left_node: int) -> int: ...
    def cost(self, arc: int) -> int: ...
    def left_node(self, arc: int) -> int: ...
    def num_arcs(self) -> int: ...
    def num_nodes(self) -> int: ...
    def optimal_cost(self) -> int: ...
    def right_mate(self, left_node: int) -> int: ...
    def right_node(self, arc: int) -> int: ...
    def solve(self, *args, **kwargs): ...
