# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
dental_scheduler_env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Generated output files
gantt_by_employee.html
gantt_by_job.html
job_priority_dashboard.html
team_workload.html
schedule_dashboard.html
jobs_summary.txt

# Generated launcher scripts
run_gui.bat
run_cli.bat
run_gui.sh
run_cli.sh

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log

# Temporary files
*.tmp
*.temp
