# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/pdlp/solvers.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/pdlp/solvers.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.glop import parameters_pb2 as ortools_dot_glop_dot_parameters__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1aortools/pdlp/solvers.proto\x12\x18operations_research.pdlp\x1a\x1dortools/glop/parameters.proto\"\xa0\x08\n\x13TerminationCriteria\x12U\n\x0foptimality_norm\x18\x01 \x01(\x0e\x32(.operations_research.pdlp.OptimalityNorm:\x12OPTIMALITY_NORM_L2\x12l\n\x1asimple_optimality_criteria\x18\t \x01(\x0b\x32\x46.operations_research.pdlp.TerminationCriteria.SimpleOptimalityCriteriaH\x00\x12p\n\x1c\x64\x65tailed_optimality_criteria\x18\n \x01(\x0b\x32H.operations_research.pdlp.TerminationCriteria.DetailedOptimalityCriteriaH\x00\x12\'\n\x14\x65ps_optimal_absolute\x18\x02 \x01(\x01:\x05\x31\x65-06B\x02\x18\x01\x12\'\n\x14\x65ps_optimal_relative\x18\x03 \x01(\x01:\x05\x31\x65-06B\x02\x18\x01\x12$\n\x15\x65ps_primal_infeasible\x18\x04 \x01(\x01:\x05\x31\x65-08\x12\"\n\x13\x65ps_dual_infeasible\x18\x05 \x01(\x01:\x05\x31\x65-08\x12\x1b\n\x0etime_sec_limit\x18\x06 \x01(\x01:\x03inf\x12#\n\x0fiteration_limit\x18\x07 \x01(\x05:\n2147483647\x12\"\n\x15kkt_matrix_pass_limit\x18\x08 \x01(\x01:\x03inf\x1a\x64\n\x18SimpleOptimalityCriteria\x12#\n\x14\x65ps_optimal_absolute\x18\x01 \x01(\x01:\x05\x31\x65-06\x12#\n\x14\x65ps_optimal_relative\x18\x02 \x01(\x01:\x05\x31\x65-06\x1a\xd2\x02\n\x1a\x44\x65tailedOptimalityCriteria\x12\x33\n$eps_optimal_primal_residual_absolute\x18\x01 \x01(\x01:\x05\x31\x65-06\x12\x33\n$eps_optimal_primal_residual_relative\x18\x02 \x01(\x01:\x05\x31\x65-06\x12\x31\n\"eps_optimal_dual_residual_absolute\x18\x03 \x01(\x01:\x05\x31\x65-06\x12\x31\n\"eps_optimal_dual_residual_relative\x18\x04 \x01(\x01:\x05\x31\x65-06\x12\x31\n\"eps_optimal_objective_gap_absolute\x18\x05 \x01(\x01:\x05\x31\x65-06\x12\x31\n\"eps_optimal_objective_gap_relative\x18\x06 \x01(\x01:\x05\x31\x65-06B\x15\n\x13optimality_criteria\"m\n\x18\x41\x64\x61ptiveLinesearchParams\x12)\n\x1cstep_size_reduction_exponent\x18\x01 \x01(\x01:\x03\x30.3\x12&\n\x19step_size_growth_exponent\x18\x02 \x01(\x01:\x03\x30.6\"\x90\x01\n\x12MalitskyPockParams\x12)\n\x1cstep_size_downscaling_factor\x18\x01 \x01(\x01:\x03\x30.7\x12+\n\x1dlinesearch_contraction_factor\x18\x02 \x01(\x01:\x04\x30.99\x12\"\n\x17step_size_interpolation\x18\x03 \x01(\x01:\x01\x31\"\xb0\x10\n\x1ePrimalDualHybridGradientParams\x12K\n\x14termination_criteria\x18\x01 \x01(\x0b\x32-.operations_research.pdlp.TerminationCriteria\x12\x16\n\x0bnum_threads\x18\x02 \x01(\x05:\x01\x31\x12\x15\n\nnum_shards\x18\x1b \x01(\x05:\x01\x30\x12\x61\n\x0escheduler_type\x18  \x01(\x0e\x32\'.operations_research.pdlp.SchedulerType: SCHEDULER_TYPE_GOOGLE_THREADPOOL\x12\x1e\n\x16record_iteration_stats\x18\x03 \x01(\x08\x12\x1a\n\x0fverbosity_level\x18\x1a \x01(\x05:\x01\x30\x12\x1f\n\x14log_interval_seconds\x18\x1f \x01(\x01:\x01\x30\x12%\n\x19major_iteration_frequency\x18\x04 \x01(\x05:\x02\x36\x34\x12\'\n\x1btermination_check_frequency\x18\x05 \x01(\x05:\x02\x36\x34\x12v\n\x10restart_strategy\x18\x06 \x01(\x0e\x32H.operations_research.pdlp.PrimalDualHybridGradientParams.RestartStrategy:\x12\x41\x44\x41PTIVE_HEURISTIC\x12+\n\x1eprimal_weight_update_smoothing\x18\x07 \x01(\x01:\x03\x30.5\x12\x1d\n\x15initial_primal_weight\x18\x08 \x01(\x01\x12\x62\n\x10presolve_options\x18\x10 \x01(\x0b\x32H.operations_research.pdlp.PrimalDualHybridGradientParams.PresolveOptions\x12 \n\x15l_inf_ruiz_iterations\x18\t \x01(\x05:\x01\x35\x12\x1f\n\x11l2_norm_rescaling\x18\n \x01(\x08:\x04true\x12-\n sufficient_reduction_for_restart\x18\x0b \x01(\x01:\x03\x30.1\x12,\n\x1fnecessary_reduction_for_restart\x18\x11 \x01(\x01:\x03\x30.9\x12z\n\x0flinesearch_rule\x18\x0c \x01(\x0e\x32G.operations_research.pdlp.PrimalDualHybridGradientParams.LinesearchRule:\x18\x41\x44\x41PTIVE_LINESEARCH_RULE\x12Z\n\x1e\x61\x64\x61ptive_linesearch_parameters\x18\x12 \x01(\x0b\x32\x32.operations_research.pdlp.AdaptiveLinesearchParams\x12N\n\x18malitsky_pock_parameters\x18\x13 \x01(\x0b\x32,.operations_research.pdlp.MalitskyPockParams\x12$\n\x19initial_step_size_scaling\x18\x19 \x01(\x01:\x01\x31\x12#\n\x17random_projection_seeds\x18\x1c \x03(\x05\x42\x02\x10\x01\x12\x30\n#infinite_constraint_bound_threshold\x18\x16 \x01(\x01:\x03inf\x12H\n:handle_some_primal_gradients_on_finite_bounds_as_residuals\x18\x1d \x01(\x08:\x04true\x12\x32\n#use_diagonal_qp_trust_region_solver\x18\x17 \x01(\x08:\x05\x66\x61lse\x12\x38\n)diagonal_qp_trust_region_solver_tolerance\x18\x18 \x01(\x01:\x05\x31\x65-08\x12(\n\x19use_feasibility_polishing\x18\x1e \x01(\x08:\x05\x66\x61lse\x12?\n0apply_feasibility_polishing_after_limits_reached\x18! \x01(\x08:\x05\x66\x61lse\x12\x43\n4apply_feasibility_polishing_if_solver_is_interrupted\x18\" \x01(\x08:\x05\x66\x61lse\x1a\x66\n\x0fPresolveOptions\x12\x10\n\x08use_glop\x18\x01 \x01(\x08\x12\x41\n\x0fglop_parameters\x18\x02 \x01(\x0b\x32(.operations_research.glop.GlopParameters\"\x94\x01\n\x0fRestartStrategy\x12 \n\x1cRESTART_STRATEGY_UNSPECIFIED\x10\x00\x12\x0f\n\x0bNO_RESTARTS\x10\x01\x12\x19\n\x15\x45VERY_MAJOR_ITERATION\x10\x02\x12\x16\n\x12\x41\x44\x41PTIVE_HEURISTIC\x10\x03\x12\x1b\n\x17\x41\x44\x41PTIVE_DISTANCE_BASED\x10\x04\"\x8f\x01\n\x0eLinesearchRule\x12\x1f\n\x1bLINESEARCH_RULE_UNSPECIFIED\x10\x00\x12\x1c\n\x18\x41\x44\x41PTIVE_LINESEARCH_RULE\x10\x01\x12!\n\x1dMALITSKY_POCK_LINESEARCH_RULE\x10\x02\x12\x1b\n\x17\x43ONSTANT_STEP_SIZE_RULE\x10\x03J\x04\x08\r\x10\x0eJ\x04\x08\x0e\x10\x0fJ\x04\x08\x0f\x10\x10J\x04\x08\x14\x10\x15J\x04\x08\x15\x10\x16*\x8d\x01\n\x0eOptimalityNorm\x12\x1f\n\x1bOPTIMALITY_NORM_UNSPECIFIED\x10\x00\x12\x19\n\x15OPTIMALITY_NORM_L_INF\x10\x01\x12\x16\n\x12OPTIMALITY_NORM_L2\x10\x02\x12\'\n#OPTIMALITY_NORM_L_INF_COMPONENTWISE\x10\x03*z\n\rSchedulerType\x12\x1e\n\x1aSCHEDULER_TYPE_UNSPECIFIED\x10\x00\x12$\n SCHEDULER_TYPE_GOOGLE_THREADPOOL\x10\x01\x12#\n\x1fSCHEDULER_TYPE_EIGEN_THREADPOOL\x10\x03\x42\x31\n\x17\x63om.google.ortools.pdlpP\x01\xaa\x02\x13Google.OrTools.PDLP')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.pdlp.solvers_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\027com.google.ortools.pdlpP\001\252\002\023Google.OrTools.PDLP'
  _globals['_TERMINATIONCRITERIA'].fields_by_name['eps_optimal_absolute']._loaded_options = None
  _globals['_TERMINATIONCRITERIA'].fields_by_name['eps_optimal_absolute']._serialized_options = b'\030\001'
  _globals['_TERMINATIONCRITERIA'].fields_by_name['eps_optimal_relative']._loaded_options = None
  _globals['_TERMINATIONCRITERIA'].fields_by_name['eps_optimal_relative']._serialized_options = b'\030\001'
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS'].fields_by_name['random_projection_seeds']._loaded_options = None
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS'].fields_by_name['random_projection_seeds']._serialized_options = b'\020\001'
  _globals['_OPTIMALITYNORM']._serialized_start=3504
  _globals['_OPTIMALITYNORM']._serialized_end=3645
  _globals['_SCHEDULERTYPE']._serialized_start=3647
  _globals['_SCHEDULERTYPE']._serialized_end=3769
  _globals['_TERMINATIONCRITERIA']._serialized_start=88
  _globals['_TERMINATIONCRITERIA']._serialized_end=1144
  _globals['_TERMINATIONCRITERIA_SIMPLEOPTIMALITYCRITERIA']._serialized_start=680
  _globals['_TERMINATIONCRITERIA_SIMPLEOPTIMALITYCRITERIA']._serialized_end=780
  _globals['_TERMINATIONCRITERIA_DETAILEDOPTIMALITYCRITERIA']._serialized_start=783
  _globals['_TERMINATIONCRITERIA_DETAILEDOPTIMALITYCRITERIA']._serialized_end=1121
  _globals['_ADAPTIVELINESEARCHPARAMS']._serialized_start=1146
  _globals['_ADAPTIVELINESEARCHPARAMS']._serialized_end=1255
  _globals['_MALITSKYPOCKPARAMS']._serialized_start=1258
  _globals['_MALITSKYPOCKPARAMS']._serialized_end=1402
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS']._serialized_start=1405
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS']._serialized_end=3501
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS_PRESOLVEOPTIONS']._serialized_start=3072
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS_PRESOLVEOPTIONS']._serialized_end=3174
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS_RESTARTSTRATEGY']._serialized_start=3177
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS_RESTARTSTRATEGY']._serialized_end=3325
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS_LINESEARCHRULE']._serialized_start=3328
  _globals['_PRIMALDUALHYBRIDGRADIENTPARAMS_LINESEARCHRULE']._serialized_end=3471
# @@protoc_insertion_point(module_scope)
