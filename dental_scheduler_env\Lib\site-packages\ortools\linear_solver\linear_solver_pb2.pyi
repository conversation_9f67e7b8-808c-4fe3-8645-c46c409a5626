"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Linear Programming Protocol Buffers.

The protocol buffers below make it possible to store and transfer the
representation of Linear and Mixed-Integer Programs.

A Linear Program (LP) is a mathematical optimization model with a linear
objective function, and linear equality and inequality constraints.
The goal is to achieve the best outcome (such as maximum profit or lowest
cost) by modeling the real-world problem at hand using linear functions.
In a Mixed Integer Program (MIP), some variables may also be constrained to
take integer values.

Check ./linear_solver.h and Wikipedia for more detail:
  http://en.wikipedia.org/wiki/Linear_programming
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import ortools.util.optional_boolean_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _MPSolverResponseStatus:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _MPSolverResponseStatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_MPSolverResponseStatus.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    MPSOLVER_OPTIMAL: _MPSolverResponseStatus.ValueType  # 0
    """The solver found the proven optimal solution. This is what should be
    returned in most cases.

    WARNING: for historical reason, the value is zero, which means that this
    value can't have any subcategories.
    """
    MPSOLVER_FEASIBLE: _MPSolverResponseStatus.ValueType  # 1
    """The solver had enough time to find some solution that satisfies all
    constraints, but it did not prove optimality (which means it may or may
    not have reached the optimal).

    This can happen for large LP models (Linear Programming), and is a frequent
    response for time-limited MIPs (Mixed Integer Programming). In the MIP
    case, the difference between the solution 'objective_value' and
    'best_objective_bound' fields of the MPSolutionResponse will give an
    indication of how far this solution is from the optimal one.
    """
    MPSOLVER_INFEASIBLE: _MPSolverResponseStatus.ValueType  # 2
    """The model does not have any solution, according to the solver (which
    "proved" it, with the caveat that numerical proofs aren't actual proofs),
    or based on trivial considerations (eg. a variable whose lower bound is
    strictly greater than its upper bound).
    """
    MPSOLVER_UNBOUNDED: _MPSolverResponseStatus.ValueType  # 3
    """There exist solutions that make the magnitude of the objective value
    as large as wanted (i.e. -infinity (resp. +infinity) for a minimization
    (resp. maximization) problem.
    """
    MPSOLVER_ABNORMAL: _MPSolverResponseStatus.ValueType  # 4
    """An error (most probably numerical) occurred.
    One likely cause for such errors is a large numerical range among variable
    coefficients (eg. 1e-16, 1e20), in which case one should try to shrink it.
    """
    MPSOLVER_NOT_SOLVED: _MPSolverResponseStatus.ValueType  # 6
    """The solver did not have a chance to diagnose the model in one of the
    categories above.
    """
    MPSOLVER_MODEL_IS_VALID: _MPSolverResponseStatus.ValueType  # 97
    """Like "NOT_SOLVED", but typically used by model validation functions
    returning a "model status", to enhance readability of the client code.
    """
    MPSOLVER_CANCELLED_BY_USER: _MPSolverResponseStatus.ValueType  # 98
    """The solve was interrupted by the user, and the solver didn't have time to
    return a proper status.
    """
    MPSOLVER_UNKNOWN_STATUS: _MPSolverResponseStatus.ValueType  # 99
    """Special value: the solver status could not be properly translated and is
    unknown.
    """
    MPSOLVER_MODEL_INVALID: _MPSolverResponseStatus.ValueType  # 5
    """Model errors. These are always deterministic and repeatable.
    They should be accompanied with a string description of the error.
    """
    MPSOLVER_MODEL_INVALID_SOLUTION_HINT: _MPSolverResponseStatus.ValueType  # 84
    """Something is wrong with the fields "solution_hint_var_index" and/or
    "solution_hint_var_value".
    """
    MPSOLVER_MODEL_INVALID_SOLVER_PARAMETERS: _MPSolverResponseStatus.ValueType  # 85
    """Something is wrong with the solver_specific_parameters request field."""
    MPSOLVER_SOLVER_TYPE_UNAVAILABLE: _MPSolverResponseStatus.ValueType  # 7
    """Implementation error: the requested solver implementation is not
    available (see MPModelRequest.solver_type).
    The linear solver binary was probably not linked with the required library,
    eg //ortools/linear_solver:linear_solver_scip for SCIP.
    """
    MPSOLVER_INCOMPATIBLE_OPTIONS: _MPSolverResponseStatus.ValueType  # 113
    """Some of the selected options were incompatible, e.g. a cancellable solve
    was requested via SolverClient::SolveMipRemotely() with an underlying
    solver that doesn't support cancellation. status_str should contain a
    description of the issue.
    """

class MPSolverResponseStatus(_MPSolverResponseStatus, metaclass=_MPSolverResponseStatusEnumTypeWrapper):
    """Status returned by the solver. They follow a hierarchical nomenclature, to
    allow us to add more enum values in the future. Clients should use
    InCategory() to match these enums, with the following C++ pseudo-code:

    bool InCategory(MPSolverResponseStatus status, MPSolverResponseStatus cat) {
      if (cat == MPSOLVER_OPTIMAL) return status == MPSOLVER_OPTIMAL;
      while (status > cat) status >>= 4;
      return status == cat;
    }
    Normal responses -- the model was valid, and the solver ran.
    These statuses should be "somewhat" repeatable, modulo the fact that the
    solver's time limit makes it undeterministic, and could change a FEASIBLE
    model to an OPTIMAL and vice-versa (the others, except NOT_SOLVED, should
    normally be deterministic). Also, the solver libraries can be buggy.
    """

MPSOLVER_OPTIMAL: MPSolverResponseStatus.ValueType  # 0
"""The solver found the proven optimal solution. This is what should be
returned in most cases.

WARNING: for historical reason, the value is zero, which means that this
value can't have any subcategories.
"""
MPSOLVER_FEASIBLE: MPSolverResponseStatus.ValueType  # 1
"""The solver had enough time to find some solution that satisfies all
constraints, but it did not prove optimality (which means it may or may
not have reached the optimal).

This can happen for large LP models (Linear Programming), and is a frequent
response for time-limited MIPs (Mixed Integer Programming). In the MIP
case, the difference between the solution 'objective_value' and
'best_objective_bound' fields of the MPSolutionResponse will give an
indication of how far this solution is from the optimal one.
"""
MPSOLVER_INFEASIBLE: MPSolverResponseStatus.ValueType  # 2
"""The model does not have any solution, according to the solver (which
"proved" it, with the caveat that numerical proofs aren't actual proofs),
or based on trivial considerations (eg. a variable whose lower bound is
strictly greater than its upper bound).
"""
MPSOLVER_UNBOUNDED: MPSolverResponseStatus.ValueType  # 3
"""There exist solutions that make the magnitude of the objective value
as large as wanted (i.e. -infinity (resp. +infinity) for a minimization
(resp. maximization) problem.
"""
MPSOLVER_ABNORMAL: MPSolverResponseStatus.ValueType  # 4
"""An error (most probably numerical) occurred.
One likely cause for such errors is a large numerical range among variable
coefficients (eg. 1e-16, 1e20), in which case one should try to shrink it.
"""
MPSOLVER_NOT_SOLVED: MPSolverResponseStatus.ValueType  # 6
"""The solver did not have a chance to diagnose the model in one of the
categories above.
"""
MPSOLVER_MODEL_IS_VALID: MPSolverResponseStatus.ValueType  # 97
"""Like "NOT_SOLVED", but typically used by model validation functions
returning a "model status", to enhance readability of the client code.
"""
MPSOLVER_CANCELLED_BY_USER: MPSolverResponseStatus.ValueType  # 98
"""The solve was interrupted by the user, and the solver didn't have time to
return a proper status.
"""
MPSOLVER_UNKNOWN_STATUS: MPSolverResponseStatus.ValueType  # 99
"""Special value: the solver status could not be properly translated and is
unknown.
"""
MPSOLVER_MODEL_INVALID: MPSolverResponseStatus.ValueType  # 5
"""Model errors. These are always deterministic and repeatable.
They should be accompanied with a string description of the error.
"""
MPSOLVER_MODEL_INVALID_SOLUTION_HINT: MPSolverResponseStatus.ValueType  # 84
"""Something is wrong with the fields "solution_hint_var_index" and/or
"solution_hint_var_value".
"""
MPSOLVER_MODEL_INVALID_SOLVER_PARAMETERS: MPSolverResponseStatus.ValueType  # 85
"""Something is wrong with the solver_specific_parameters request field."""
MPSOLVER_SOLVER_TYPE_UNAVAILABLE: MPSolverResponseStatus.ValueType  # 7
"""Implementation error: the requested solver implementation is not
available (see MPModelRequest.solver_type).
The linear solver binary was probably not linked with the required library,
eg //ortools/linear_solver:linear_solver_scip for SCIP.
"""
MPSOLVER_INCOMPATIBLE_OPTIONS: MPSolverResponseStatus.ValueType  # 113
"""Some of the selected options were incompatible, e.g. a cancellable solve
was requested via SolverClient::SolveMipRemotely() with an underlying
solver that doesn't support cancellation. status_str should contain a
description of the issue.
"""
global___MPSolverResponseStatus = MPSolverResponseStatus

@typing.final
class MPVariableProto(google.protobuf.message.Message):
    """A variable is always constrained in the form:
       lower_bound <= x <= upper_bound
    where lower_bound and upper_bound:
    - Can form a singleton: x = constant = lower_bound = upper_bound.
    - Can form a finite interval: lower_bound <= x <= upper_bound. (x is boxed.)
    - Can form a semi-infinite interval.
        - lower_bound = -infinity: x <= upper_bound.
        - upper_bound = +infinity: x >= lower_bound.
    - Can form the infinite interval: lower_bound = -infinity and
      upper_bound = +infinity, x is free.
    MPVariableProto furthermore stores:
     - The coefficient of the variable in the objective.
     - Whether the variable is integer.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOWER_BOUND_FIELD_NUMBER: builtins.int
    UPPER_BOUND_FIELD_NUMBER: builtins.int
    OBJECTIVE_COEFFICIENT_FIELD_NUMBER: builtins.int
    IS_INTEGER_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    BRANCHING_PRIORITY_FIELD_NUMBER: builtins.int
    lower_bound: builtins.float
    """lower_bound must be <= upper_bound."""
    upper_bound: builtins.float
    objective_coefficient: builtins.float
    """The coefficient of the variable in the objective. Must be finite."""
    is_integer: builtins.bool
    """True if the variable is constrained to be integer.
    Ignored if MPModelProto::solver_type is *LINEAR_PROGRAMMING*.
    """
    name: builtins.str
    """The name of the variable."""
    branching_priority: builtins.int
    def __init__(
        self,
        *,
        lower_bound: builtins.float | None = ...,
        upper_bound: builtins.float | None = ...,
        objective_coefficient: builtins.float | None = ...,
        is_integer: builtins.bool | None = ...,
        name: builtins.str | None = ...,
        branching_priority: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["branching_priority", b"branching_priority", "is_integer", b"is_integer", "lower_bound", b"lower_bound", "name", b"name", "objective_coefficient", b"objective_coefficient", "upper_bound", b"upper_bound"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["branching_priority", b"branching_priority", "is_integer", b"is_integer", "lower_bound", b"lower_bound", "name", b"name", "objective_coefficient", b"objective_coefficient", "upper_bound", b"upper_bound"]) -> None: ...

global___MPVariableProto = MPVariableProto

@typing.final
class MPConstraintProto(google.protobuf.message.Message):
    """A linear constraint is always of the form:
    lower_bound <= sum of linear term elements <= upper_bound,
    where lower_bound and upper_bound:
    - Can form a singleton: lower_bound == upper_bound. The constraint is an
      equation.
    - Can form a finite interval [lower_bound, upper_bound]. The constraint is
      both lower- and upper-bounded, i.e. "boxed".
    - Can form a semi-infinite interval. lower_bound = -infinity: the constraint
      is upper-bounded. upper_bound = +infinity: the constraint is lower-bounded.
    - Can form the infinite interval: lower_bound = -infinity and
      upper_bound = +infinity. The constraint is free.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_INDEX_FIELD_NUMBER: builtins.int
    COEFFICIENT_FIELD_NUMBER: builtins.int
    LOWER_BOUND_FIELD_NUMBER: builtins.int
    UPPER_BOUND_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    IS_LAZY_FIELD_NUMBER: builtins.int
    lower_bound: builtins.float
    """lower_bound must be <= upper_bound."""
    upper_bound: builtins.float
    name: builtins.str
    """The name of the constraint."""
    is_lazy: builtins.bool
    """[Advanced usage: do not use this if you don't know what you're doing.]
    A lazy constraint is handled differently by the core solving engine, but
    it does not change the result. It may or may not impact the performance.
    For more info see: http://tinyurl.com/lazy-constraints.
    """
    @property
    def var_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """var_index[i] is the variable index (w.r.t. to "variable" field of
        MPModelProto) of the i-th linear term involved in this constraint, and
        coefficient[i] is its coefficient. Only the terms with non-zero
        coefficients need to appear. var_index may not contain duplicates.
        """

    @property
    def coefficient(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """Must be finite."""

    def __init__(
        self,
        *,
        var_index: collections.abc.Iterable[builtins.int] | None = ...,
        coefficient: collections.abc.Iterable[builtins.float] | None = ...,
        lower_bound: builtins.float | None = ...,
        upper_bound: builtins.float | None = ...,
        name: builtins.str | None = ...,
        is_lazy: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["is_lazy", b"is_lazy", "lower_bound", b"lower_bound", "name", b"name", "upper_bound", b"upper_bound"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["coefficient", b"coefficient", "is_lazy", b"is_lazy", "lower_bound", b"lower_bound", "name", b"name", "upper_bound", b"upper_bound", "var_index", b"var_index"]) -> None: ...

global___MPConstraintProto = MPConstraintProto

@typing.final
class MPGeneralConstraintProto(google.protobuf.message.Message):
    """General constraints. See each individual proto type for more information."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    NAME_FIELD_NUMBER: builtins.int
    INDICATOR_CONSTRAINT_FIELD_NUMBER: builtins.int
    SOS_CONSTRAINT_FIELD_NUMBER: builtins.int
    QUADRATIC_CONSTRAINT_FIELD_NUMBER: builtins.int
    ABS_CONSTRAINT_FIELD_NUMBER: builtins.int
    AND_CONSTRAINT_FIELD_NUMBER: builtins.int
    OR_CONSTRAINT_FIELD_NUMBER: builtins.int
    MIN_CONSTRAINT_FIELD_NUMBER: builtins.int
    MAX_CONSTRAINT_FIELD_NUMBER: builtins.int
    name: builtins.str
    """The name of the constraint."""
    @property
    def indicator_constraint(self) -> global___MPIndicatorConstraint: ...
    @property
    def sos_constraint(self) -> global___MPSosConstraint: ...
    @property
    def quadratic_constraint(self) -> global___MPQuadraticConstraint: ...
    @property
    def abs_constraint(self) -> global___MPAbsConstraint: ...
    @property
    def and_constraint(self) -> global___MPArrayConstraint:
        """All variables in "and" constraints must be Boolean.
        resultant_var = and(var_1, var_2... var_n)
        """

    @property
    def or_constraint(self) -> global___MPArrayConstraint:
        """All variables in "or" constraints must be Boolean.
        resultant_var = or(var_1, var_2... var_n)
        """

    @property
    def min_constraint(self) -> global___MPArrayWithConstantConstraint:
        """resultant_var = min(var_1, var_2, ..., constant)"""

    @property
    def max_constraint(self) -> global___MPArrayWithConstantConstraint:
        """resultant_var = max(var_1, var_2, ..., constant)"""

    def __init__(
        self,
        *,
        name: builtins.str | None = ...,
        indicator_constraint: global___MPIndicatorConstraint | None = ...,
        sos_constraint: global___MPSosConstraint | None = ...,
        quadratic_constraint: global___MPQuadraticConstraint | None = ...,
        abs_constraint: global___MPAbsConstraint | None = ...,
        and_constraint: global___MPArrayConstraint | None = ...,
        or_constraint: global___MPArrayConstraint | None = ...,
        min_constraint: global___MPArrayWithConstantConstraint | None = ...,
        max_constraint: global___MPArrayWithConstantConstraint | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["abs_constraint", b"abs_constraint", "and_constraint", b"and_constraint", "general_constraint", b"general_constraint", "indicator_constraint", b"indicator_constraint", "max_constraint", b"max_constraint", "min_constraint", b"min_constraint", "name", b"name", "or_constraint", b"or_constraint", "quadratic_constraint", b"quadratic_constraint", "sos_constraint", b"sos_constraint"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["abs_constraint", b"abs_constraint", "and_constraint", b"and_constraint", "general_constraint", b"general_constraint", "indicator_constraint", b"indicator_constraint", "max_constraint", b"max_constraint", "min_constraint", b"min_constraint", "name", b"name", "or_constraint", b"or_constraint", "quadratic_constraint", b"quadratic_constraint", "sos_constraint", b"sos_constraint"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["general_constraint", b"general_constraint"]) -> typing.Literal["indicator_constraint", "sos_constraint", "quadratic_constraint", "abs_constraint", "and_constraint", "or_constraint", "min_constraint", "max_constraint"] | None: ...

global___MPGeneralConstraintProto = MPGeneralConstraintProto

@typing.final
class MPIndicatorConstraint(google.protobuf.message.Message):
    """Indicator constraints encode the activation or deactivation of linear
    constraints given the value of one Boolean variable in the model. For
    example:
        y = 0 => 2 * x1 + 3 * x2 >= 42
    The 2 * x1 + 3 * x2 >= 42 constraint is only active if the variable y is
    equal to 0.
    As of 2019/04, only SCIP, CP-SAT and Gurobi support this constraint type.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_INDEX_FIELD_NUMBER: builtins.int
    VAR_VALUE_FIELD_NUMBER: builtins.int
    CONSTRAINT_FIELD_NUMBER: builtins.int
    var_index: builtins.int
    """Variable index (w.r.t. the "variable" field of MPModelProto) of the Boolean
    variable used as indicator.
    """
    var_value: builtins.int
    """Value the above variable should take. Must be 0 or 1."""
    @property
    def constraint(self) -> global___MPConstraintProto:
        """The constraint activated by the indicator variable."""

    def __init__(
        self,
        *,
        var_index: builtins.int | None = ...,
        var_value: builtins.int | None = ...,
        constraint: global___MPConstraintProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["constraint", b"constraint", "var_index", b"var_index", "var_value", b"var_value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["constraint", b"constraint", "var_index", b"var_index", "var_value", b"var_value"]) -> None: ...

global___MPIndicatorConstraint = MPIndicatorConstraint

@typing.final
class MPSosConstraint(google.protobuf.message.Message):
    """Special Ordered Set (SOS) constraints of type 1 or 2.
    See https://en.wikipedia.org/wiki/Special_ordered_set
    As of 2019/04, only SCIP and Gurobi support this constraint type.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Type:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _TypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[MPSosConstraint._Type.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        SOS1_DEFAULT: MPSosConstraint._Type.ValueType  # 0
        """At most one variable in `var_index` must be non-zero."""
        SOS2: MPSosConstraint._Type.ValueType  # 1
        """At most two consecutive variables from `var_index` can be non-zero (i.e.
        for some i, var_index[i] and var_index[i+1]). See
        https://en.wikipedia.org/wiki/Special_ordered_set#Types_of_SOS
        """

    class Type(_Type, metaclass=_TypeEnumTypeWrapper): ...
    SOS1_DEFAULT: MPSosConstraint.Type.ValueType  # 0
    """At most one variable in `var_index` must be non-zero."""
    SOS2: MPSosConstraint.Type.ValueType  # 1
    """At most two consecutive variables from `var_index` can be non-zero (i.e.
    for some i, var_index[i] and var_index[i+1]). See
    https://en.wikipedia.org/wiki/Special_ordered_set#Types_of_SOS
    """

    TYPE_FIELD_NUMBER: builtins.int
    VAR_INDEX_FIELD_NUMBER: builtins.int
    WEIGHT_FIELD_NUMBER: builtins.int
    type: global___MPSosConstraint.Type.ValueType
    @property
    def var_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Variable index (w.r.t. the "variable" field of MPModelProto) of the
        variables in the SOS.
        """

    @property
    def weight(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """Optional: SOS weights. If non-empty, must be of the same size as
        "var_index", and strictly increasing. If empty and required by the
        underlying solver, the 1..n sequence will be given as weights.
        SUBTLE: The weights can help the solver make branch-and-bound decisions
        that fit the underlying optimization model: after each LP relaxation, it
        will compute the "average weight" of the SOS variables, weighted by value
        (this is confusing: here we're using the values as weights), and the binary
        branch decision will be: is the non-zero variable above or below that?
        (weights are strictly monotonous, so the "cutoff" average weight
        corresponds to a "cutoff" index in the var_index sequence).
        """

    def __init__(
        self,
        *,
        type: global___MPSosConstraint.Type.ValueType | None = ...,
        var_index: collections.abc.Iterable[builtins.int] | None = ...,
        weight: collections.abc.Iterable[builtins.float] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["type", b"type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["type", b"type", "var_index", b"var_index", "weight", b"weight"]) -> None: ...

global___MPSosConstraint = MPSosConstraint

@typing.final
class MPQuadraticConstraint(google.protobuf.message.Message):
    """Quadratic constraints of the form lb <= sum a_i x_i + sum b_ij x_i x_j <= ub,
    where a, b, lb and ub are constants, and x are the model's variables.
    Quadratic matrices that are Positive Semi-Definite, Second-Order Cones or
    rotated Second-Order Cones are always accepted. Other forms may or may not be
    accepted depending on the underlying solver used.
    See https://scip.zib.de/doc/html/cons__quadratic_8h.php and
    https://www.gurobi.com/documentation/9.0/refman/constraints.html#subsubsection:QuadraticConstraints
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_INDEX_FIELD_NUMBER: builtins.int
    COEFFICIENT_FIELD_NUMBER: builtins.int
    QVAR1_INDEX_FIELD_NUMBER: builtins.int
    QVAR2_INDEX_FIELD_NUMBER: builtins.int
    QCOEFFICIENT_FIELD_NUMBER: builtins.int
    LOWER_BOUND_FIELD_NUMBER: builtins.int
    UPPER_BOUND_FIELD_NUMBER: builtins.int
    lower_bound: builtins.float
    """lower_bound must be <= upper_bound."""
    upper_bound: builtins.float
    @property
    def var_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Sparse representation of linear terms in the quadratic constraint, where
        term i is var_index[i] * coefficient[i].
        `var_index` are variable indices w.r.t the "variable" field in
        MPModelProto, and should be unique.
        """

    @property
    def coefficient(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """Must be finite."""

    @property
    def qvar1_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Sparse representation of quadratic terms in the quadratic constraint, where
        term i is qvar1_index[i] * qvar2_index[i] * qcoefficient[i].
        `qvar1_index` and `qvar2_index` are variable indices w.r.t the "variable"
        field in MPModelProto.
        `qvar1_index`, `qvar2_index` and `coefficients` must have the same size.
        If the same unordered pair (qvar1_index, qvar2_index) appears several
        times, the sum of all of the associated coefficients will be applied.
        """

    @property
    def qvar2_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def qcoefficient(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """Must be finite."""

    def __init__(
        self,
        *,
        var_index: collections.abc.Iterable[builtins.int] | None = ...,
        coefficient: collections.abc.Iterable[builtins.float] | None = ...,
        qvar1_index: collections.abc.Iterable[builtins.int] | None = ...,
        qvar2_index: collections.abc.Iterable[builtins.int] | None = ...,
        qcoefficient: collections.abc.Iterable[builtins.float] | None = ...,
        lower_bound: builtins.float | None = ...,
        upper_bound: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lower_bound", b"lower_bound", "upper_bound", b"upper_bound"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["coefficient", b"coefficient", "lower_bound", b"lower_bound", "qcoefficient", b"qcoefficient", "qvar1_index", b"qvar1_index", "qvar2_index", b"qvar2_index", "upper_bound", b"upper_bound", "var_index", b"var_index"]) -> None: ...

global___MPQuadraticConstraint = MPQuadraticConstraint

@typing.final
class MPAbsConstraint(google.protobuf.message.Message):
    """Sets a variable's value to the absolute value of another variable."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_INDEX_FIELD_NUMBER: builtins.int
    RESULTANT_VAR_INDEX_FIELD_NUMBER: builtins.int
    var_index: builtins.int
    """Variable indices are relative to the "variable" field in MPModelProto.
    resultant_var = abs(var)
    """
    resultant_var_index: builtins.int
    def __init__(
        self,
        *,
        var_index: builtins.int | None = ...,
        resultant_var_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["resultant_var_index", b"resultant_var_index", "var_index", b"var_index"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["resultant_var_index", b"resultant_var_index", "var_index", b"var_index"]) -> None: ...

global___MPAbsConstraint = MPAbsConstraint

@typing.final
class MPArrayConstraint(google.protobuf.message.Message):
    """Sets a variable's value equal to a function on a set of variables."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_INDEX_FIELD_NUMBER: builtins.int
    RESULTANT_VAR_INDEX_FIELD_NUMBER: builtins.int
    resultant_var_index: builtins.int
    @property
    def var_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Variable indices are relative to the "variable" field in MPModelProto."""

    def __init__(
        self,
        *,
        var_index: collections.abc.Iterable[builtins.int] | None = ...,
        resultant_var_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["resultant_var_index", b"resultant_var_index"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["resultant_var_index", b"resultant_var_index", "var_index", b"var_index"]) -> None: ...

global___MPArrayConstraint = MPArrayConstraint

@typing.final
class MPArrayWithConstantConstraint(google.protobuf.message.Message):
    """Sets a variable's value equal to a function on a set of variables and,
    optionally, a constant.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_INDEX_FIELD_NUMBER: builtins.int
    CONSTANT_FIELD_NUMBER: builtins.int
    RESULTANT_VAR_INDEX_FIELD_NUMBER: builtins.int
    constant: builtins.float
    resultant_var_index: builtins.int
    @property
    def var_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Variable indices are relative to the "variable" field in MPModelProto.
        resultant_var = f(var_1, var_2, ..., constant)
        """

    def __init__(
        self,
        *,
        var_index: collections.abc.Iterable[builtins.int] | None = ...,
        constant: builtins.float | None = ...,
        resultant_var_index: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["constant", b"constant", "resultant_var_index", b"resultant_var_index"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["constant", b"constant", "resultant_var_index", b"resultant_var_index", "var_index", b"var_index"]) -> None: ...

global___MPArrayWithConstantConstraint = MPArrayWithConstantConstraint

@typing.final
class MPQuadraticObjective(google.protobuf.message.Message):
    """Quadratic part of a model's objective. Added with other objectives (such as
    linear), this creates the model's objective function to be optimized.
    Note: the linear part of the objective currently needs to be specified in the
    MPVariableProto.objective_coefficient fields. If you'd rather have a
    dedicated linear array here, talk to or-core-team@
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    QVAR1_INDEX_FIELD_NUMBER: builtins.int
    QVAR2_INDEX_FIELD_NUMBER: builtins.int
    COEFFICIENT_FIELD_NUMBER: builtins.int
    @property
    def qvar1_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Sparse representation of quadratic terms in the objective function, where
        term i is qvar1_index[i] * qvar2_index[i] * coefficient[i].
        `qvar1_index` and `qvar2_index` are variable indices w.r.t the "variable"
        field in MPModelProto.
        `qvar1_index`, `qvar2_index` and `coefficients` must have the same size.
        If the same unordered pair (qvar1_index, qvar2_index) appears several
        times, the sum of all of the associated coefficients will be applied.
        """

    @property
    def qvar2_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def coefficient(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """Must be finite."""

    def __init__(
        self,
        *,
        qvar1_index: collections.abc.Iterable[builtins.int] | None = ...,
        qvar2_index: collections.abc.Iterable[builtins.int] | None = ...,
        coefficient: collections.abc.Iterable[builtins.float] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["coefficient", b"coefficient", "qvar1_index", b"qvar1_index", "qvar2_index", b"qvar2_index"]) -> None: ...

global___MPQuadraticObjective = MPQuadraticObjective

@typing.final
class PartialVariableAssignment(google.protobuf.message.Message):
    """This message encodes a partial (or full) assignment of the variables of a
    MPModelProto problem. The indices in var_index should be unique and valid
    variable indices of the associated problem.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VAR_INDEX_FIELD_NUMBER: builtins.int
    VAR_VALUE_FIELD_NUMBER: builtins.int
    @property
    def var_index(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]: ...
    @property
    def var_value(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]: ...
    def __init__(
        self,
        *,
        var_index: collections.abc.Iterable[builtins.int] | None = ...,
        var_value: collections.abc.Iterable[builtins.float] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["var_index", b"var_index", "var_value", b"var_value"]) -> None: ...

global___PartialVariableAssignment = PartialVariableAssignment

@typing.final
class MPModelProto(google.protobuf.message.Message):
    """MPModelProto contains all the information for a Linear Programming model."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class Annotation(google.protobuf.message.Message):
        """Annotations can be freely added by users who want to attach arbitrary
        payload to the model's variables or constraints.
        """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        class _TargetType:
            ValueType = typing.NewType("ValueType", builtins.int)
            V: typing_extensions.TypeAlias = ValueType

        class _TargetTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[MPModelProto.Annotation._TargetType.ValueType], builtins.type):
            DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
            VARIABLE_DEFAULT: MPModelProto.Annotation._TargetType.ValueType  # 0
            CONSTRAINT: MPModelProto.Annotation._TargetType.ValueType  # 1
            GENERAL_CONSTRAINT: MPModelProto.Annotation._TargetType.ValueType  # 2

        class TargetType(_TargetType, metaclass=_TargetTypeEnumTypeWrapper):
            """The target of an Annotation is a single entity (e.g. a variable).
            Several Annotations may apply to the same entity.
            """

        VARIABLE_DEFAULT: MPModelProto.Annotation.TargetType.ValueType  # 0
        CONSTRAINT: MPModelProto.Annotation.TargetType.ValueType  # 1
        GENERAL_CONSTRAINT: MPModelProto.Annotation.TargetType.ValueType  # 2

        TARGET_TYPE_FIELD_NUMBER: builtins.int
        TARGET_INDEX_FIELD_NUMBER: builtins.int
        TARGET_NAME_FIELD_NUMBER: builtins.int
        PAYLOAD_KEY_FIELD_NUMBER: builtins.int
        PAYLOAD_VALUE_FIELD_NUMBER: builtins.int
        target_type: global___MPModelProto.Annotation.TargetType.ValueType
        target_index: builtins.int
        """If both `target_index` and `target_name` are set, they must point to the
        same entity.
        Index in the MPModelProto.
        """
        target_name: builtins.str
        """Alternate to index. Assumes uniqueness."""
        payload_key: builtins.str
        """The payload is a (key, value) string pair. Depending on the use cases,
        one of the two may be omitted.
        """
        payload_value: builtins.str
        def __init__(
            self,
            *,
            target_type: global___MPModelProto.Annotation.TargetType.ValueType | None = ...,
            target_index: builtins.int | None = ...,
            target_name: builtins.str | None = ...,
            payload_key: builtins.str | None = ...,
            payload_value: builtins.str | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["payload_key", b"payload_key", "payload_value", b"payload_value", "target_index", b"target_index", "target_name", b"target_name", "target_type", b"target_type"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["payload_key", b"payload_key", "payload_value", b"payload_value", "target_index", b"target_index", "target_name", b"target_name", "target_type", b"target_type"]) -> None: ...

    VARIABLE_FIELD_NUMBER: builtins.int
    CONSTRAINT_FIELD_NUMBER: builtins.int
    GENERAL_CONSTRAINT_FIELD_NUMBER: builtins.int
    MAXIMIZE_FIELD_NUMBER: builtins.int
    OBJECTIVE_OFFSET_FIELD_NUMBER: builtins.int
    QUADRATIC_OBJECTIVE_FIELD_NUMBER: builtins.int
    NAME_FIELD_NUMBER: builtins.int
    SOLUTION_HINT_FIELD_NUMBER: builtins.int
    ANNOTATION_FIELD_NUMBER: builtins.int
    maximize: builtins.bool
    """True if the problem is a maximization problem. Minimize by default."""
    objective_offset: builtins.float
    """Offset for the objective function. Must be finite."""
    name: builtins.str
    """Name of the model."""
    @property
    def variable(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MPVariableProto]:
        """All the variables appearing in the model."""

    @property
    def constraint(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MPConstraintProto]:
        """All the constraints appearing in the model."""

    @property
    def general_constraint(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MPGeneralConstraintProto]:
        """All the general constraints appearing in the model. Note that not all
        solvers support all types of general constraints.
        """

    @property
    def quadratic_objective(self) -> global___MPQuadraticObjective:
        """Optionally, a quadratic objective.
        As of 2019/06, only SCIP and Gurobi support quadratic objectives.
        """

    @property
    def solution_hint(self) -> global___PartialVariableAssignment:
        """Solution hint.

        If a feasible or almost-feasible solution to the problem is already known,
        it may be helpful to pass it to the solver so that it can be used. A solver
        that supports this feature will try to use this information to create its
        initial feasible solution.

        Note that it may not always be faster to give a hint like this to the
        solver. There is also no guarantee that the solver will use this hint or
        try to return a solution "close" to this assignment in case of multiple
        optimal solutions.
        """

    @property
    def annotation(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MPModelProto.Annotation]: ...
    def __init__(
        self,
        *,
        variable: collections.abc.Iterable[global___MPVariableProto] | None = ...,
        constraint: collections.abc.Iterable[global___MPConstraintProto] | None = ...,
        general_constraint: collections.abc.Iterable[global___MPGeneralConstraintProto] | None = ...,
        maximize: builtins.bool | None = ...,
        objective_offset: builtins.float | None = ...,
        quadratic_objective: global___MPQuadraticObjective | None = ...,
        name: builtins.str | None = ...,
        solution_hint: global___PartialVariableAssignment | None = ...,
        annotation: collections.abc.Iterable[global___MPModelProto.Annotation] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["maximize", b"maximize", "name", b"name", "objective_offset", b"objective_offset", "quadratic_objective", b"quadratic_objective", "solution_hint", b"solution_hint"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["annotation", b"annotation", "constraint", b"constraint", "general_constraint", b"general_constraint", "maximize", b"maximize", "name", b"name", "objective_offset", b"objective_offset", "quadratic_objective", b"quadratic_objective", "solution_hint", b"solution_hint", "variable", b"variable"]) -> None: ...

global___MPModelProto = MPModelProto

@typing.final
class OptionalDouble(google.protobuf.message.Message):
    """To support 'unspecified' double value in proto3, the simplest is to wrap
    any double value in a nested message (has_XXX works for message fields).
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VALUE_FIELD_NUMBER: builtins.int
    value: builtins.float
    def __init__(
        self,
        *,
        value: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["value", b"value"]) -> None: ...

global___OptionalDouble = OptionalDouble

@typing.final
class MPSolverCommonParameters(google.protobuf.message.Message):
    """MPSolverCommonParameters holds advanced usage parameters that apply to any of
    the solvers we support.
    All of the fields in this proto can have a value of unspecified. In this
    case each inner solver will use their own safe defaults.
    Some values won't be supported by some solvers. The behavior in that case is
    not defined yet.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _LPAlgorithmValues:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _LPAlgorithmValuesEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[MPSolverCommonParameters._LPAlgorithmValues.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        LP_ALGO_UNSPECIFIED: MPSolverCommonParameters._LPAlgorithmValues.ValueType  # 0
        LP_ALGO_DUAL: MPSolverCommonParameters._LPAlgorithmValues.ValueType  # 1
        """Dual simplex."""
        LP_ALGO_PRIMAL: MPSolverCommonParameters._LPAlgorithmValues.ValueType  # 2
        """Primal simplex."""
        LP_ALGO_BARRIER: MPSolverCommonParameters._LPAlgorithmValues.ValueType  # 3
        """Barrier algorithm."""

    class LPAlgorithmValues(_LPAlgorithmValues, metaclass=_LPAlgorithmValuesEnumTypeWrapper): ...
    LP_ALGO_UNSPECIFIED: MPSolverCommonParameters.LPAlgorithmValues.ValueType  # 0
    LP_ALGO_DUAL: MPSolverCommonParameters.LPAlgorithmValues.ValueType  # 1
    """Dual simplex."""
    LP_ALGO_PRIMAL: MPSolverCommonParameters.LPAlgorithmValues.ValueType  # 2
    """Primal simplex."""
    LP_ALGO_BARRIER: MPSolverCommonParameters.LPAlgorithmValues.ValueType  # 3
    """Barrier algorithm."""

    RELATIVE_MIP_GAP_FIELD_NUMBER: builtins.int
    PRIMAL_TOLERANCE_FIELD_NUMBER: builtins.int
    DUAL_TOLERANCE_FIELD_NUMBER: builtins.int
    LP_ALGORITHM_FIELD_NUMBER: builtins.int
    PRESOLVE_FIELD_NUMBER: builtins.int
    SCALING_FIELD_NUMBER: builtins.int
    lp_algorithm: global___MPSolverCommonParameters.LPAlgorithmValues.ValueType
    """Algorithm to solve linear programs.
    Ask or-core-team@ if you want to know what this does exactly.
    """
    presolve: ortools.util.optional_boolean_pb2.OptionalBoolean.ValueType
    """Gurobi and SCIP enable presolve by default.
    Ask or-core-team@ for other solvers.
    """
    scaling: ortools.util.optional_boolean_pb2.OptionalBoolean.ValueType
    """Enable automatic scaling of matrix coefficients and objective. Available
    for Gurobi and GLOP.
    Ask or-core-team@ if you want more details.
    """
    @property
    def relative_mip_gap(self) -> global___OptionalDouble:
        """The solver stops if the relative MIP gap reaches this value or below.
        The relative MIP gap is an upper bound of the relative distance to the
        optimum, and it is defined as:

          abs(best_bound - incumbent) / abs(incumbent) [Gurobi]
          abs(best_bound - incumbent) / min(abs(best_bound), abs(incumbent)) [SCIP]

        where "incumbent" is the objective value of the best solution found so far
        (i.e., lowest when minimizing, highest when maximizing), and "best_bound"
        is the tightest bound of the objective determined so far (i.e., highest
        when minimizing, and lowest when maximizing). The MIP Gap is sensitive to
        objective offset. If the denominator is 0 the MIP Gap is INFINITY for SCIP
        and Gurobi. Of note, "incumbent" and "best bound" are called "primal bound"
        and "dual bound" in SCIP, respectively.
        Ask or-core-team@ for other solvers.
        """

    @property
    def primal_tolerance(self) -> global___OptionalDouble:
        """Tolerance for primal feasibility of basic solutions: this is the maximum
        allowed error in constraint satisfiability.
        For SCIP this includes integrality constraints. For Gurobi it does not, you
        need to set the custom parameter IntFeasTol.
        """

    @property
    def dual_tolerance(self) -> global___OptionalDouble:
        """Tolerance for dual feasibility.
        For SCIP and Gurobi this is the feasibility tolerance for reduced costs in
        LP solution: reduced costs must all be smaller than this value in the
        improving direction in order for a model to be declared optimal.
        Not supported for other solvers.
        """

    def __init__(
        self,
        *,
        relative_mip_gap: global___OptionalDouble | None = ...,
        primal_tolerance: global___OptionalDouble | None = ...,
        dual_tolerance: global___OptionalDouble | None = ...,
        lp_algorithm: global___MPSolverCommonParameters.LPAlgorithmValues.ValueType | None = ...,
        presolve: ortools.util.optional_boolean_pb2.OptionalBoolean.ValueType | None = ...,
        scaling: ortools.util.optional_boolean_pb2.OptionalBoolean.ValueType | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["dual_tolerance", b"dual_tolerance", "lp_algorithm", b"lp_algorithm", "presolve", b"presolve", "primal_tolerance", b"primal_tolerance", "relative_mip_gap", b"relative_mip_gap", "scaling", b"scaling"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["dual_tolerance", b"dual_tolerance", "lp_algorithm", b"lp_algorithm", "presolve", b"presolve", "primal_tolerance", b"primal_tolerance", "relative_mip_gap", b"relative_mip_gap", "scaling", b"scaling"]) -> None: ...

global___MPSolverCommonParameters = MPSolverCommonParameters

@typing.final
class MPModelDeltaProto(google.protobuf.message.Message):
    """Encodes a full MPModelProto by way of referencing to a "baseline"
    MPModelProto stored in a file, and a "delta" to apply to this model.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class VariableOverridesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> global___MPVariableProto: ...
        def __init__(
            self,
            *,
            key: builtins.int | None = ...,
            value: global___MPVariableProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class ConstraintOverridesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> global___MPConstraintProto: ...
        def __init__(
            self,
            *,
            key: builtins.int | None = ...,
            value: global___MPConstraintProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    BASELINE_MODEL_FILE_PATH_FIELD_NUMBER: builtins.int
    VARIABLE_OVERRIDES_FIELD_NUMBER: builtins.int
    CONSTRAINT_OVERRIDES_FIELD_NUMBER: builtins.int
    baseline_model_file_path: builtins.str
    @property
    def variable_overrides(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, global___MPVariableProto]:
        """The variable protos listed here will override (via MergeFrom()) the ones
        in the baseline model: you only need to specify the fields that change.
        To add a new variable, add it with a new variable index (variable indices
        still need to span a dense integer interval).
        You can't "delete" a variable but you can "neutralize" it by fixing its
        value, setting its objective coefficient to zero, and by nullifying all
        the terms involving it in the constraints.
        """

    @property
    def constraint_overrides(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, global___MPConstraintProto]:
        """Constraints can be changed (or added) in the same way as variables, see
        above. It's mostly like applying MergeFrom(), except that:
        - the "var_index" and "coefficient" fields will be overridden like a map:
          if a key pre-exists, we overwrite its value, otherwise we add it.
        - if you set the lower bound to -inf and the upper bound to +inf, thus
          effectively neutralizing the constraint, the solver will implicitly
          remove all of the constraint's terms.
        """

    def __init__(
        self,
        *,
        baseline_model_file_path: builtins.str | None = ...,
        variable_overrides: collections.abc.Mapping[builtins.int, global___MPVariableProto] | None = ...,
        constraint_overrides: collections.abc.Mapping[builtins.int, global___MPConstraintProto] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["baseline_model_file_path", b"baseline_model_file_path"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["baseline_model_file_path", b"baseline_model_file_path", "constraint_overrides", b"constraint_overrides", "variable_overrides", b"variable_overrides"]) -> None: ...

global___MPModelDeltaProto = MPModelDeltaProto

@typing.final
class MPModelRequest(google.protobuf.message.Message):
    """Next id: 18."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _SolverType:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _SolverTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[MPModelRequest._SolverType.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        CLP_LINEAR_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 0
        GLOP_LINEAR_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 2
        """Recommended default for LP models."""
        GLPK_LINEAR_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 1
        GUROBI_LINEAR_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 6
        """Commercial, needs a valid license."""
        XPRESS_LINEAR_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 101
        """Commercial, needs a valid license. NOLINT"""
        CPLEX_LINEAR_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 10
        """Commercial, needs a valid license. NOLINT"""
        HIGHS_LINEAR_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 15
        SCIP_MIXED_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 3
        """Recommended default for MIP models."""
        GLPK_MIXED_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 4
        CBC_MIXED_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 5
        GUROBI_MIXED_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 7
        """Commercial, needs a valid license."""
        XPRESS_MIXED_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 102
        """Commercial, needs a valid license. NOLINT"""
        CPLEX_MIXED_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 11
        """Commercial, needs a valid license. NOLINT"""
        HIGHS_MIXED_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 16
        BOP_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 12
        SAT_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 14
        """WARNING: This solver will currently interpret all variables as integer,
        so any solution you get will be valid, but the optimal might be far away
        for the real one (when you authorise non-integer value for continuous
        variables).
        Recommended for pure integer problems.
        """
        PDLP_LINEAR_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 8
        """In-house linear programming solver based on the primal-dual hybrid
        gradient method. Sometimes faster than Glop for medium-size problems and
        scales to much larger problems than Glop.
        """
        KNAPSACK_MIXED_INTEGER_PROGRAMMING: MPModelRequest._SolverType.ValueType  # 13

    class SolverType(_SolverType, metaclass=_SolverTypeEnumTypeWrapper):
        """The solver type, which will select a specific implementation, and will also
        impact the interpretation of the model (i.e. are we solving the problem
        as a mixed integer program or are we relaxing it as a continuous linear
        program?).
        This must remain consistent with MPSolver::OptimizationProblemType.
        """

    CLP_LINEAR_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 0
    GLOP_LINEAR_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 2
    """Recommended default for LP models."""
    GLPK_LINEAR_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 1
    GUROBI_LINEAR_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 6
    """Commercial, needs a valid license."""
    XPRESS_LINEAR_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 101
    """Commercial, needs a valid license. NOLINT"""
    CPLEX_LINEAR_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 10
    """Commercial, needs a valid license. NOLINT"""
    HIGHS_LINEAR_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 15
    SCIP_MIXED_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 3
    """Recommended default for MIP models."""
    GLPK_MIXED_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 4
    CBC_MIXED_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 5
    GUROBI_MIXED_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 7
    """Commercial, needs a valid license."""
    XPRESS_MIXED_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 102
    """Commercial, needs a valid license. NOLINT"""
    CPLEX_MIXED_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 11
    """Commercial, needs a valid license. NOLINT"""
    HIGHS_MIXED_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 16
    BOP_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 12
    SAT_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 14
    """WARNING: This solver will currently interpret all variables as integer,
    so any solution you get will be valid, but the optimal might be far away
    for the real one (when you authorise non-integer value for continuous
    variables).
    Recommended for pure integer problems.
    """
    PDLP_LINEAR_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 8
    """In-house linear programming solver based on the primal-dual hybrid
    gradient method. Sometimes faster than Glop for medium-size problems and
    scales to much larger problems than Glop.
    """
    KNAPSACK_MIXED_INTEGER_PROGRAMMING: MPModelRequest.SolverType.ValueType  # 13

    MODEL_FIELD_NUMBER: builtins.int
    SOLVER_TYPE_FIELD_NUMBER: builtins.int
    SOLVER_TIME_LIMIT_SECONDS_FIELD_NUMBER: builtins.int
    ENABLE_INTERNAL_SOLVER_OUTPUT_FIELD_NUMBER: builtins.int
    SOLVER_SPECIFIC_PARAMETERS_FIELD_NUMBER: builtins.int
    IGNORE_SOLVER_SPECIFIC_PARAMETERS_FAILURE_FIELD_NUMBER: builtins.int
    MODEL_DELTA_FIELD_NUMBER: builtins.int
    POPULATE_ADDITIONAL_SOLUTIONS_UP_TO_FIELD_NUMBER: builtins.int
    solver_type: global___MPModelRequest.SolverType.ValueType
    solver_time_limit_seconds: builtins.float
    """Maximum time to be spent by the solver to solve 'model'. If the server is
    busy and the RPC's deadline_left is less than this, it will immediately
    give up and return an error, without even trying to solve.

    The client can use this to have a guarantee on how much time the
    solver will spend on the problem (unless it finds and proves
    an optimal solution more quickly).

    If not specified, the time limit on the solver is the RPC's deadline_left.
    """
    enable_internal_solver_output: builtins.bool
    """If this is set, then EnableOutput() will be set on the internal MPSolver
    that solves the model.
    WARNING: if you set this on a request to prod servers, it will be rejected
    and yield the RPC Application Error code MPSOLVER_SOLVER_TYPE_UNAVAILABLE.
    """
    solver_specific_parameters: builtins.str
    """Advanced usage. Solver-specific parameters in the solver's own format,
    different for each solver. For example, if you use SCIP and you want to
    stop the solve earlier than the time limit if it reached a solution that is
    at most 1% away from the optimal, you can set this to "limits/gap=0.01".

    Note however that there is no "security" mechanism in place so it is up to
    the client to make sure that the given options don't make the solve
    non thread safe or use up too much memory for instance.

    If the option format is not understood by the solver, the request will be
    rejected and yield an RPC Application error with code
    MPSOLVER_MODEL_INVALID_SOLVER_PARAMETERS, unless you have set
    ignore_solver_specific_parameters_failure=true (in which case they are
    simply ignored).
    """
    ignore_solver_specific_parameters_failure: builtins.bool
    populate_additional_solutions_up_to: builtins.int
    """Controls the recovery of additional solutions, if any, saved by the
    underlying solver back in the MPSolutionResponse.additional_solutions.
    The repeated field will be length
       min(populate_addition_solutions_up_to,
           #additional_solutions_available_in_underlying_solver)
    These additional solutions may have a worse objective than the main
    solution returned in the response.
    """
    @property
    def model(self) -> global___MPModelProto:
        """The model to be optimized by the server."""

    @property
    def model_delta(self) -> global___MPModelDeltaProto:
        """Advanced usage: model "delta". If used, "model" must be unset. See the
        definition of MPModelDeltaProto.
        """

    def __init__(
        self,
        *,
        model: global___MPModelProto | None = ...,
        solver_type: global___MPModelRequest.SolverType.ValueType | None = ...,
        solver_time_limit_seconds: builtins.float | None = ...,
        enable_internal_solver_output: builtins.bool | None = ...,
        solver_specific_parameters: builtins.str | None = ...,
        ignore_solver_specific_parameters_failure: builtins.bool | None = ...,
        model_delta: global___MPModelDeltaProto | None = ...,
        populate_additional_solutions_up_to: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["enable_internal_solver_output", b"enable_internal_solver_output", "ignore_solver_specific_parameters_failure", b"ignore_solver_specific_parameters_failure", "model", b"model", "model_delta", b"model_delta", "populate_additional_solutions_up_to", b"populate_additional_solutions_up_to", "solver_specific_parameters", b"solver_specific_parameters", "solver_time_limit_seconds", b"solver_time_limit_seconds", "solver_type", b"solver_type"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["enable_internal_solver_output", b"enable_internal_solver_output", "ignore_solver_specific_parameters_failure", b"ignore_solver_specific_parameters_failure", "model", b"model", "model_delta", b"model_delta", "populate_additional_solutions_up_to", b"populate_additional_solutions_up_to", "solver_specific_parameters", b"solver_specific_parameters", "solver_time_limit_seconds", b"solver_time_limit_seconds", "solver_type", b"solver_type"]) -> None: ...

global___MPModelRequest = MPModelRequest

@typing.final
class MPSolution(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OBJECTIVE_VALUE_FIELD_NUMBER: builtins.int
    VARIABLE_VALUE_FIELD_NUMBER: builtins.int
    objective_value: builtins.float
    @property
    def variable_value(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]: ...
    def __init__(
        self,
        *,
        objective_value: builtins.float | None = ...,
        variable_value: collections.abc.Iterable[builtins.float] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["objective_value", b"objective_value"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["objective_value", b"objective_value", "variable_value", b"variable_value"]) -> None: ...

global___MPSolution = MPSolution

@typing.final
class MPSolveInfo(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SOLVE_WALL_TIME_SECONDS_FIELD_NUMBER: builtins.int
    SOLVE_USER_TIME_SECONDS_FIELD_NUMBER: builtins.int
    solve_wall_time_seconds: builtins.float
    """How much wall time (resp. user time) elapsed during the Solve() of the
    underlying solver library. "wall" time and "user" time are to be
    interpreted like for the "time" command in bash (see "help time").
    In particular, "user time" is CPU time and can be greater than wall time
    when using several threads.
    """
    solve_user_time_seconds: builtins.float
    def __init__(
        self,
        *,
        solve_wall_time_seconds: builtins.float | None = ...,
        solve_user_time_seconds: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["solve_user_time_seconds", b"solve_user_time_seconds", "solve_wall_time_seconds", b"solve_wall_time_seconds"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["solve_user_time_seconds", b"solve_user_time_seconds", "solve_wall_time_seconds", b"solve_wall_time_seconds"]) -> None: ...

global___MPSolveInfo = MPSolveInfo

@typing.final
class MPSolutionResponse(google.protobuf.message.Message):
    """Next id: 12."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STATUS_FIELD_NUMBER: builtins.int
    STATUS_STR_FIELD_NUMBER: builtins.int
    OBJECTIVE_VALUE_FIELD_NUMBER: builtins.int
    BEST_OBJECTIVE_BOUND_FIELD_NUMBER: builtins.int
    VARIABLE_VALUE_FIELD_NUMBER: builtins.int
    SOLVE_INFO_FIELD_NUMBER: builtins.int
    SOLVER_SPECIFIC_INFO_FIELD_NUMBER: builtins.int
    DUAL_VALUE_FIELD_NUMBER: builtins.int
    REDUCED_COST_FIELD_NUMBER: builtins.int
    ADDITIONAL_SOLUTIONS_FIELD_NUMBER: builtins.int
    status: global___MPSolverResponseStatus.ValueType
    """Result of the optimization."""
    status_str: builtins.str
    """Human-readable string giving more details about the status. For example,
    when the status is MPSOLVER_INVALID_MODE, this can hold a description of
    why the model is invalid.
    This isn't always filled: don't depend on its value or even its presence.
    """
    objective_value: builtins.float
    """Objective value corresponding to the "variable_value" below, taking into
    account the source "objective_offset" and "objective_coefficient".
    This is set iff 'status' is OPTIMAL or FEASIBLE.
    """
    best_objective_bound: builtins.float
    """This field is only filled for MIP problems. For a minimization problem,
    this is a lower bound on the optimal objective value. For a maximization
    problem, it is an upper bound. It is only filled if the status is OPTIMAL
    or FEASIBLE. In the former case, best_objective_bound should be equal to
    objective_value (modulo numerical errors).
    """
    solver_specific_info: builtins.bytes
    """Opaque solver-specific information.
    For the PDLP solver, this is a serialized pdlp::SolveLog proto.
    """
    @property
    def variable_value(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """Variable values in the same order as the MPModelProto::variable field.
        This is a dense representation. These are set iff 'status' is OPTIMAL or
        FEASIBLE.
        """

    @property
    def solve_info(self) -> global___MPSolveInfo:
        """Contains extra information about the solve, populated if the underlying
        solver (and its interface) supports it. As of 2021/07/19 this is supported
        by SCIP and Gurobi proto solves.
        """

    @property
    def dual_value(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """[Advanced usage.]
        Values of the dual variables values in the same order as the
        MPModelProto::constraint field. This is a dense representation.
        These are not set if the problem was solved with a MIP solver (even if
        it is actually a linear program).
        These are set iff 'status' is OPTIMAL or FEASIBLE.
        """

    @property
    def reduced_cost(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.float]:
        """[Advanced usage.]
        Values of the reduced cost of the variables in the same order as the
        MPModelProto::variable. This is a dense representation.
        These are not set if the problem was solved with a MIP solver (even if it
        is actually a linear program).
        These are set iff 'status' is OPTIMAL or FEASIBLE.
        """

    @property
    def additional_solutions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___MPSolution]:
        """[Advanced usage.]
        If `MPModelRequest.populate_additional_solutions_up_to` > 0, up to that
        number of additional solutions may be populated here, if available. These
        additional solutions are different than the main solution described by the
        above fields `objective_value` and `variable_value`.
        """

    def __init__(
        self,
        *,
        status: global___MPSolverResponseStatus.ValueType | None = ...,
        status_str: builtins.str | None = ...,
        objective_value: builtins.float | None = ...,
        best_objective_bound: builtins.float | None = ...,
        variable_value: collections.abc.Iterable[builtins.float] | None = ...,
        solve_info: global___MPSolveInfo | None = ...,
        solver_specific_info: builtins.bytes | None = ...,
        dual_value: collections.abc.Iterable[builtins.float] | None = ...,
        reduced_cost: collections.abc.Iterable[builtins.float] | None = ...,
        additional_solutions: collections.abc.Iterable[global___MPSolution] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["best_objective_bound", b"best_objective_bound", "objective_value", b"objective_value", "solve_info", b"solve_info", "solver_specific_info", b"solver_specific_info", "status", b"status", "status_str", b"status_str"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["additional_solutions", b"additional_solutions", "best_objective_bound", b"best_objective_bound", "dual_value", b"dual_value", "objective_value", b"objective_value", "reduced_cost", b"reduced_cost", "solve_info", b"solve_info", "solver_specific_info", b"solver_specific_info", "status", b"status", "status_str", b"status_str", "variable_value", b"variable_value"]) -> None: ...

global___MPSolutionResponse = MPSolutionResponse
