# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/constraint_solver/routing_parameters.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/constraint_solver/routing_parameters.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from ortools.constraint_solver import routing_enums_pb2 as ortools_dot_constraint__solver_dot_routing__enums__pb2
from ortools.constraint_solver import routing_ils_pb2 as ortools_dot_constraint__solver_dot_routing__ils__pb2
from ortools.constraint_solver import solver_parameters_pb2 as ortools_dot_constraint__solver_dot_solver__parameters__pb2
from ortools.sat import sat_parameters_pb2 as ortools_dot_sat_dot_sat__parameters__pb2
from ortools.util import optional_boolean_pb2 as ortools_dot_util_dot_optional__boolean__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n2ortools/constraint_solver/routing_parameters.proto\x12\x13operations_research\x1a\x1egoogle/protobuf/duration.proto\x1a-ortools/constraint_solver/routing_enums.proto\x1a+ortools/constraint_solver/routing_ils.proto\x1a\x31ortools/constraint_solver/solver_parameters.proto\x1a ortools/sat/sat_parameters.proto\x1a#ortools/util/optional_boolean.proto\"\xd7\x37\n\x17RoutingSearchParameters\x12Q\n\x17\x66irst_solution_strategy\x18\x01 \x01(\x0e\x32\x30.operations_research.FirstSolutionStrategy.Value\x12.\n&use_unfiltered_first_solution_strategy\x18\x02 \x01(\x08\x12\x1f\n\x17savings_neighbors_ratio\x18\x0e \x01(\x01\x12&\n\x1esavings_max_memory_usage_bytes\x18\x17 \x01(\x01\x12 \n\x18savings_add_reverse_arcs\x18\x0f \x01(\x08\x12\x1f\n\x17savings_arc_coefficient\x18\x12 \x01(\x01\x12/\n\'cheapest_insertion_farthest_seeds_ratio\x18\x10 \x01(\x01\x12\x39\n1cheapest_insertion_first_solution_neighbors_ratio\x18\x15 \x01(\x01\x12\x37\n/cheapest_insertion_first_solution_min_neighbors\x18, \x01(\x05\x12\x36\n.cheapest_insertion_ls_operator_neighbors_ratio\x18\x1f \x01(\x01\x12\x34\n,cheapest_insertion_ls_operator_min_neighbors\x18- \x01(\x05\x12P\nHcheapest_insertion_first_solution_use_neighbors_ratio_for_initialization\x18. \x01(\x08\x12\x32\n*cheapest_insertion_add_unperformed_entries\x18( \x01(\x08\x12}\n1local_cheapest_insertion_pickup_delivery_strategy\x18\x31 \x01(\x0e\x32\x42.operations_research.RoutingSearchParameters.PairInsertionStrategy\x12\x82\x01\n6local_cheapest_cost_insertion_pickup_delivery_strategy\x18\x37 \x01(\x0e\x32\x42.operations_research.RoutingSearchParameters.PairInsertionStrategy\x12z\n+local_cheapest_insertion_sorting_properties\x18\x43 \x03(\x0e\x32\x45.operations_research.RoutingSearchParameters.InsertionSortingProperty\x12)\n!christofides_use_minimum_matching\x18\x1e \x01(\x08\x12*\n\"first_solution_optimization_period\x18; \x01(\x05\x12m\n\x16local_search_operators\x18\x03 \x01(\x0b\x32M.operations_research.RoutingSearchParameters.LocalSearchNeighborhoodOperators\x12#\n\x1bls_operator_neighbors_ratio\x18\x35 \x01(\x01\x12!\n\x19ls_operator_min_neighbors\x18\x36 \x01(\x05\x12\x34\n,use_multi_armed_bandit_concatenate_operators\x18) \x01(\x08\x12?\n7multi_armed_bandit_compound_operator_memory_coefficient\x18* \x01(\x01\x12\x44\n<multi_armed_bandit_compound_operator_exploration_coefficient\x18+ \x01(\x01\x12\"\n\x1amax_swap_active_chain_size\x18\x42 \x01(\x05\x12\x35\n-relocate_expensive_chain_num_arcs_to_consider\x18\x14 \x01(\x05\x12:\n2heuristic_expensive_chain_lns_num_arcs_to_consider\x18  \x01(\x05\x12+\n#heuristic_close_nodes_lns_num_nodes\x18# \x01(\x05\x12W\n\x1alocal_search_metaheuristic\x18\x04 \x01(\x0e\x32\x33.operations_research.LocalSearchMetaheuristic.Value\x12X\n\x1blocal_search_metaheuristics\x18? \x03(\x0e\x32\x33.operations_research.LocalSearchMetaheuristic.Value\x12\x38\n0num_max_local_optima_before_metaheuristic_switch\x18@ \x01(\x05\x12.\n&guided_local_search_lambda_coefficient\x18\x05 \x01(\x01\x12@\n8guided_local_search_reset_penalties_on_new_best_solution\x18\x33 \x01(\x08\x12\x39\n1guided_local_search_penalize_with_vehicle_classes\x18= \x01(\x08\x12\x43\n;use_guided_local_search_penalties_in_local_search_operators\x18> \x01(\x08\x12\x1e\n\x16use_depth_first_search\x18\x06 \x01(\x08\x12\x34\n\x06use_cp\x18\x1c \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x38\n\nuse_cp_sat\x18\x1b \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x44\n\x16use_generalized_cp_sat\x18/ \x01(\x0e\x32$.operations_research.OptionalBoolean\x12>\n\x0esat_parameters\x18\x30 \x01(\x0b\x32&.operations_research.sat.SatParameters\x12,\n$report_intermediate_cp_sat_solutions\x18\x38 \x01(\x08\x12)\n!fallback_to_cp_sat_size_threshold\x18\x34 \x01(\x05\x12\x63\n\x1c\x63ontinuous_scheduling_solver\x18! \x01(\x0e\x32=.operations_research.RoutingSearchParameters.SchedulingSolver\x12\x66\n\x1fmixed_integer_scheduling_solver\x18\" \x01(\x0e\x32=.operations_research.RoutingSearchParameters.SchedulingSolver\x12\x43\n6disable_scheduling_beware_this_may_degrade_performance\x18\x32 \x01(\x08H\x00\x88\x01\x01\x12\x19\n\x11optimization_step\x18\x07 \x01(\x01\x12&\n\x1enumber_of_solutions_to_collect\x18\x11 \x01(\x05\x12\x16\n\x0esolution_limit\x18\x08 \x01(\x03\x12-\n\ntime_limit\x18\t \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x31\n\x0elns_time_limit\x18\n \x01(\x0b\x32\x19.google.protobuf.Duration\x12%\n\x1dsecondary_ls_time_limit_ratio\x18\x39 \x01(\x01\x12s\n\x1cimprovement_limit_parameters\x18% \x01(\x0b\x32M.operations_research.RoutingSearchParameters.ImprovementSearchLimitParameters\x12\x1c\n\x14use_full_propagation\x18\x0b \x01(\x08\x12\x12\n\nlog_search\x18\r \x01(\x08\x12\x1f\n\x17log_cost_scaling_factor\x18\x16 \x01(\x01\x12\x17\n\x0flog_cost_offset\x18\x1d \x01(\x01\x12\x0f\n\x07log_tag\x18$ \x01(\t\x12!\n\x19use_iterated_local_search\x18: \x01(\x08\x12\\\n iterated_local_search_parameters\x18< \x01(\x0b\x32\x32.operations_research.IteratedLocalSearchParameters\x1a\xda\x15\n LocalSearchNeighborhoodOperators\x12:\n\x0cuse_relocate\x18\x01 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12?\n\x11use_relocate_pair\x18\x02 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x45\n\x17use_light_relocate_pair\x18\x18 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x44\n\x16use_relocate_neighbors\x18\x03 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x42\n\x14use_relocate_subtrip\x18\x19 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12:\n\x0cuse_exchange\x18\x04 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12?\n\x11use_exchange_pair\x18\x16 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x42\n\x14use_exchange_subtrip\x18\x1a \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x37\n\tuse_cross\x18\x05 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12@\n\x12use_cross_exchange\x18\x06 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12J\n\x1cuse_relocate_expensive_chain\x18\x17 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x39\n\x0buse_two_opt\x18\x07 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x38\n\nuse_or_opt\x18\x08 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12?\n\x11use_lin_kernighan\x18\t \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x39\n\x0buse_tsp_opt\x18\n \x01(\x0e\x32$.operations_research.OptionalBoolean\x12=\n\x0fuse_make_active\x18\x0b \x01(\x0e\x32$.operations_research.OptionalBoolean\x12J\n\x1cuse_relocate_and_make_active\x18\x15 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12J\n\x1cuse_exchange_and_make_active\x18% \x01(\x0e\x32$.operations_research.OptionalBoolean\x12Z\n,use_exchange_path_start_ends_and_make_active\x18& \x01(\x0e\x32$.operations_research.OptionalBoolean\x12?\n\x11use_make_inactive\x18\x0c \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x45\n\x17use_make_chain_inactive\x18\r \x01(\x0e\x32$.operations_research.OptionalBoolean\x12=\n\x0fuse_swap_active\x18\x0e \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x43\n\x15use_swap_active_chain\x18# \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x46\n\x18use_extended_swap_active\x18\x0f \x01(\x0e\x32$.operations_research.OptionalBoolean\x12K\n\x1duse_shortest_path_swap_active\x18\" \x01(\x0e\x32$.operations_research.OptionalBoolean\x12G\n\x19use_shortest_path_two_opt\x18$ \x01(\x0e\x32$.operations_research.OptionalBoolean\x12G\n\x19use_node_pair_swap_active\x18\x14 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12:\n\x0cuse_path_lns\x18\x10 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12?\n\x11use_full_path_lns\x18\x11 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12\x39\n\x0buse_tsp_lns\x18\x12 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12>\n\x10use_inactive_lns\x18\x13 \x01(\x0e\x32$.operations_research.OptionalBoolean\x12T\n&use_global_cheapest_insertion_path_lns\x18\x1b \x01(\x0e\x32$.operations_research.OptionalBoolean\x12S\n%use_local_cheapest_insertion_path_lns\x18\x1c \x01(\x0e\x32$.operations_research.OptionalBoolean\x12l\n>use_relocate_path_global_cheapest_insertion_insert_unperformed\x18! \x01(\x0e\x32$.operations_research.OptionalBoolean\x12_\n1use_global_cheapest_insertion_expensive_chain_lns\x18\x1d \x01(\x0e\x32$.operations_research.OptionalBoolean\x12^\n0use_local_cheapest_insertion_expensive_chain_lns\x18\x1e \x01(\x0e\x32$.operations_research.OptionalBoolean\x12[\n-use_global_cheapest_insertion_close_nodes_lns\x18\x1f \x01(\x0e\x32$.operations_research.OptionalBoolean\x12Z\n,use_local_cheapest_insertion_close_nodes_lns\x18  \x01(\x0e\x32$.operations_research.OptionalBoolean\x1au\n ImprovementSearchLimitParameters\x12$\n\x1cimprovement_rate_coefficient\x18& \x01(\x01\x12+\n#improvement_rate_solutions_distance\x18\' \x01(\x05\"\x92\x01\n\x15PairInsertionStrategy\x12\r\n\tAUTOMATIC\x10\x00\x12\"\n\x1e\x42\x45ST_PICKUP_THEN_BEST_DELIVERY\x10\x01\x12\x1d\n\x19\x42\x45ST_PICKUP_DELIVERY_PAIR\x10\x02\x12\'\n#BEST_PICKUP_DELIVERY_PAIR_MULTITOUR\x10\x03\"\xc7\x03\n\x18InsertionSortingProperty\x12 \n\x1cSORTING_PROPERTY_UNSPECIFIED\x10\x00\x12%\n!SORTING_PROPERTY_ALLOWED_VEHICLES\x10\x01\x12\x1c\n\x18SORTING_PROPERTY_PENALTY\x10\x02\x12\x38\n4SORTING_PROPERTY_PENALTY_OVER_ALLOWED_VEHICLES_RATIO\x10\x03\x12?\n;SORTING_PROPERTY_HIGHEST_AVG_ARC_COST_TO_VEHICLE_START_ENDS\x10\x04\x12>\n:SORTING_PROPERTY_LOWEST_AVG_ARC_COST_TO_VEHICLE_START_ENDS\x10\x05\x12>\n:SORTING_PROPERTY_LOWEST_MIN_ARC_COST_TO_VEHICLE_START_ENDS\x10\x06\x12,\n(SORTING_PROPERTY_HIGHEST_DIMENSION_USAGE\x10\x07\x12\x1b\n\x17SORTING_PROPERTY_RANDOM\x10\x08\"T\n\x10SchedulingSolver\x12\x14\n\x10SCHEDULING_UNSET\x10\x00\x12\x13\n\x0fSCHEDULING_GLOP\x10\x01\x12\x15\n\x11SCHEDULING_CP_SAT\x10\x02\x42\x39\n7_disable_scheduling_beware_this_may_degrade_performanceJ\x04\x08\x13\x10\x14J\x04\x08\x41\x10\x42\"\xa8\x01\n\x16RoutingModelParameters\x12J\n\x11solver_parameters\x18\x01 \x01(\x0b\x32/.operations_research.ConstraintSolverParameters\x12!\n\x19reduce_vehicle_cost_model\x18\x02 \x01(\x08\x12\x1f\n\x17max_callback_cache_size\x18\x03 \x01(\x05\x42I\n#com.google.ortools.constraintsolverP\x01\xaa\x02\x1fGoogle.OrTools.ConstraintSolverb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.constraint_solver.routing_parameters_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.google.ortools.constraintsolverP\001\252\002\037Google.OrTools.ConstraintSolver'
  _globals['_ROUTINGSEARCHPARAMETERS']._serialized_start=322
  _globals['_ROUTINGSEARCHPARAMETERS']._serialized_end=7449
  _globals['_ROUTINGSEARCHPARAMETERS_LOCALSEARCHNEIGHBORHOODOPERATORS']._serialized_start=3788
  _globals['_ROUTINGSEARCHPARAMETERS_LOCALSEARCHNEIGHBORHOODOPERATORS']._serialized_end=6566
  _globals['_ROUTINGSEARCHPARAMETERS_IMPROVEMENTSEARCHLIMITPARAMETERS']._serialized_start=6568
  _globals['_ROUTINGSEARCHPARAMETERS_IMPROVEMENTSEARCHLIMITPARAMETERS']._serialized_end=6685
  _globals['_ROUTINGSEARCHPARAMETERS_PAIRINSERTIONSTRATEGY']._serialized_start=6688
  _globals['_ROUTINGSEARCHPARAMETERS_PAIRINSERTIONSTRATEGY']._serialized_end=6834
  _globals['_ROUTINGSEARCHPARAMETERS_INSERTIONSORTINGPROPERTY']._serialized_start=6837
  _globals['_ROUTINGSEARCHPARAMETERS_INSERTIONSORTINGPROPERTY']._serialized_end=7292
  _globals['_ROUTINGSEARCHPARAMETERS_SCHEDULINGSOLVER']._serialized_start=7294
  _globals['_ROUTINGSEARCHPARAMETERS_SCHEDULINGSOLVER']._serialized_end=7378
  _globals['_ROUTINGMODELPARAMETERS']._serialized_start=7452
  _globals['_ROUTINGMODELPARAMETERS']._serialized_end=7620
# @@protoc_insertion_point(module_scope)
