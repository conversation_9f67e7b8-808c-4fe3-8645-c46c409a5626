from typing import ClassVar

KNAPSACK_64ITEMS_SOLVER: SolverType
KNAPSACK_BRUTE_FORCE_SOLVER: SolverType
KNAPSACK_DIVIDE_AND_CONQUER_SOLVER: SolverType
KNAPSACK_DYNAMIC_PROGRAMMING_SOLVER: SolverType
KNAPSACK_MULT<PERSON><PERSON>ENSION_BRANCH_AND_BOUND_SOLVER: SolverType
KNAPSACK_MULTIDIMENSION_CBC_MIP_SOLVER: SolverType
KNAPSACK_MULTIDIMENSION_CP_SAT_SOLVER: SolverType
KNAPSACK_MULTIDIMENSION_SCIP_MIP_SOLVER: SolverType

class KnapsackSolver:
    def __init__(self, arg0, arg1: str) -> None: ...
    def best_solution_contains(self, item_id: int) -> bool: ...
    def init(self, profits: list[int], weights: list[list[int]], capacities: list[int]) -> None: ...
    def is_solution_optimal(self) -> bool: ...
    def set_time_limit(self, time_limit_seconds: float) -> None: ...
    def set_use_reduction(self, use_reduction: bool) -> None: ...
    def solve(self) -> int: ...

class SolverType:
    __members__: ClassVar[dict] = ...  # read-only
    KNAPSACK_64ITEMS_SOLVER: ClassVar[SolverType] = ...
    KNAPSACK_BRUTE_FORCE_SOLVER: ClassVar[SolverType] = ...
    KNAPSACK_DIVIDE_AND_CONQUER_SOLVER: ClassVar[SolverType] = ...
    KNAPSACK_DYNAMIC_PROGRAMMING_SOLVER: ClassVar[SolverType] = ...
    KNAPSACK_MULTIDIMENSION_BRANCH_AND_BOUND_SOLVER: ClassVar[SolverType] = ...
    KNAPSACK_MULTIDIMENSION_CBC_MIP_SOLVER: ClassVar[SolverType] = ...
    KNAPSACK_MULTIDIMENSION_CP_SAT_SOLVER: ClassVar[SolverType] = ...
    KNAPSACK_MULTIDIMENSION_SCIP_MIP_SOLVER: ClassVar[SolverType] = ...
    __entries: ClassVar[dict] = ...
    def __init__(self, value: int) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __index__(self) -> int: ...
    def __int__(self) -> int: ...
    def __ne__(self, other: object) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def value(self) -> int: ...
