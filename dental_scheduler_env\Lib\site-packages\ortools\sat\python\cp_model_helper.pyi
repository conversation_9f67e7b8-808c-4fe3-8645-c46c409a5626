import ortools.util.python.sorted_interval_list
from typing import Any, Callable, overload

class BaseIntVar(Literal):
    @overload
    def __init__(self, arg0: int) -> None: ...
    @overload
    def __init__(self, arg0: int, arg1: bool) -> None: ...
    def Index(self) -> int: ...
    def Not(self) -> Literal: ...
    def negated(self) -> Literal: ...
    def __invert__(self) -> Literal: ...
    @property
    def index(self) -> int: ...
    @property
    def is_boolean(self) -> bool: ...

class BoundedLinearExpression:
    @overload
    def __init__(self, arg0: LinearExpr, arg1: ortools.util.python.sorted_interval_list.Domain) -> None: ...
    @overload
    def __init__(self, arg0: LinearExpr, arg1: LinearExpr, arg2: ortools.util.python.sorted_interval_list.Domain) -> None: ...
    def __bool__(self) -> bool: ...
    @property
    def bounds(self) -> ortools.util.python.sorted_interval_list.Domain: ...
    @property
    def coeffs(self) -> list[int]: ...
    @property
    def offset(self) -> int: ...
    @property
    def ok(self) -> bool: ...
    @property
    def vars(self) -> list[BaseIntVar]: ...

class CpSatHelper:
    def __init__(self, *args, **kwargs) -> None: ...
    @staticmethod
    def model_stats(model_proto) -> str: ...
    @staticmethod
    def solver_response_stats(response) -> str: ...
    @staticmethod
    def validate_model(model_proto) -> str: ...
    @staticmethod
    def variable_domain(variable_proto) -> ortools.util.python.sorted_interval_list.Domain: ...
    @staticmethod
    def write_model_to_file(model_proto, filename: str) -> bool: ...

class FlatFloatExpr(LinearExpr):
    def __init__(self, arg0: LinearExpr) -> None: ...
    @property
    def coeffs(self) -> list[float]: ...
    @property
    def offset(self) -> float: ...
    @property
    def vars(self): ...

class FlatIntExpr(LinearExpr):
    def __init__(self, arg0: LinearExpr) -> None: ...
    @property
    def coeffs(self) -> list[int]: ...
    @property
    def offset(self) -> int: ...
    @property
    def ok(self) -> bool: ...
    @property
    def vars(self): ...

class FloatAffine(LinearExpr):
    def __init__(self, arg0: LinearExpr, arg1: float, arg2: float) -> None: ...
    @property
    def coefficient(self) -> float: ...
    @property
    def expression(self) -> LinearExpr: ...
    @property
    def offset(self) -> float: ...

class IntAffine(LinearExpr):
    def __init__(self, arg0: LinearExpr, arg1: int, arg2: int) -> None: ...
    @property
    def coefficient(self) -> int: ...
    @property
    def expression(self) -> LinearExpr: ...
    @property
    def offset(self) -> int: ...

class LinearExpr:
    def __init__(self, *args, **kwargs) -> None: ...
    @staticmethod
    def Sum(*args) -> LinearExpr: ...
    @overload
    @staticmethod
    def Term(expr: LinearExpr, coeff: int) -> LinearExpr: ...
    @overload
    @staticmethod
    def Term(expr: LinearExpr, coeff: float) -> LinearExpr: ...
    @staticmethod
    def WeightedSum(expressions: Sequence, coefficients: Sequence) -> LinearExpr: ...
    @overload
    @staticmethod
    def affine(expr: LinearExpr, coeff: int, offset: int) -> LinearExpr: ...
    @overload
    @staticmethod
    def affine(expr: LinearExpr, coeff: float, offset: float) -> LinearExpr: ...
    @overload
    @staticmethod
    def constant(value: int) -> LinearExpr: ...
    @overload
    @staticmethod
    def constant(value: float) -> LinearExpr: ...
    def is_integer(self) -> bool: ...
    @overload
    @staticmethod
    def sum(*args) -> LinearExpr: ...
    @overload
    @staticmethod
    def sum(expressions) -> Any: ...
    @overload
    @staticmethod
    def term(expr: LinearExpr, coeff: int) -> LinearExpr: ...
    @overload
    @staticmethod
    def term(expr: LinearExpr, coeff: float) -> LinearExpr: ...
    @staticmethod
    def weighted_sum(expressions: Sequence, coefficients: Sequence) -> LinearExpr: ...
    def __abs__(self) -> None: ...
    @overload
    def __add__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __add__(self, cst: int) -> LinearExpr: ...
    @overload
    def __add__(self, cst: float) -> LinearExpr: ...
    def __and__(self, arg0: object) -> None: ...
    def __bool__(self) -> None: ...
    def __div__(self, arg0: object) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __ge__(self, other: object) -> bool: ...
    def __gt__(self, other: object) -> bool: ...
    def __le__(self, other: object) -> bool: ...
    def __lshift__(self, arg0: object) -> None: ...
    def __lt__(self, other: object) -> bool: ...
    def __mod__(self, arg0: object) -> None: ...
    @overload
    def __mul__(self, cst: int) -> LinearExpr: ...
    @overload
    def __mul__(self, cst: float) -> LinearExpr: ...
    def __ne__(self, other: object) -> bool: ...
    def __neg__(self) -> LinearExpr: ...
    def __or__(self, arg0: object) -> None: ...
    def __pow__(self, arg0: object) -> None: ...
    @overload
    def __radd__(self, cst: int) -> LinearExpr: ...
    @overload
    def __radd__(self, cst: float) -> LinearExpr: ...
    @overload
    def __rmul__(self, cst: int) -> LinearExpr: ...
    @overload
    def __rmul__(self, cst: float) -> LinearExpr: ...
    def __rshift__(self, arg0: object) -> None: ...
    @overload
    def __rsub__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __rsub__(self, cst: int) -> LinearExpr: ...
    @overload
    def __rsub__(self, cst: float) -> LinearExpr: ...
    @overload
    def __sub__(self, h: LinearExpr) -> LinearExpr: ...
    @overload
    def __sub__(self, cst: int) -> LinearExpr: ...
    @overload
    def __sub__(self, cst: float) -> LinearExpr: ...
    def __truediv__(self, arg0: object) -> None: ...
    def __xor__(self, arg0: object) -> None: ...

class Literal(LinearExpr):
    def __init__(self, *args, **kwargs) -> None: ...
    def Index(self) -> int: ...
    def Not(self) -> Literal: ...
    @overload
    def negated(self) -> Literal: ...
    @overload
    def negated(self) -> Any: ...
    def __bool__(self) -> None: ...
    def __hash__(self) -> int: ...
    def __invert__(self) -> Literal: ...
    @property
    def index(self) -> int: ...

class NotBooleanVariable(Literal):
    def __init__(self, *args, **kwargs) -> None: ...
    def Not(self) -> Literal: ...
    def negated(self) -> Literal: ...
    def __invert__(self) -> Literal: ...
    @property
    def index(self) -> int: ...

class ResponseWrapper:
    def __init__(self, *args, **kwargs) -> None: ...
    def best_objective_bound(self) -> float: ...
    @overload
    def boolean_value(self, lit) -> bool: ...
    @overload
    def boolean_value(self, lit: bool) -> bool: ...
    def deterministic_time(self) -> float: ...
    @overload
    def float_value(self, expr) -> float: ...
    @overload
    def float_value(self, value: float) -> float: ...
    def num_binary_propagations(self) -> int: ...
    def num_booleans(self) -> int: ...
    def num_branches(self) -> int: ...
    def num_conflicts(self) -> int: ...
    def num_integer_propagations(self) -> int: ...
    def num_restarts(self) -> int: ...
    def objective_value(self) -> float: ...
    def response(self, *args, **kwargs): ...
    def response_stats(self) -> str: ...
    def solution_info(self) -> str: ...
    def status(self, *args, **kwargs): ...
    def sufficient_assumptions_for_infeasibility(self) -> list[int]: ...
    def user_time(self) -> float: ...
    @overload
    def value(self, expr) -> int: ...
    @overload
    def value(self, value: int) -> int: ...
    def wall_time(self) -> float: ...

class SolutionCallback:
    def __init__(self) -> None: ...
    def BestObjectiveBound(self) -> float: ...
    @overload
    def BooleanValue(self, arg0) -> bool: ...
    @overload
    def BooleanValue(self, arg0: bool) -> bool: ...
    def DeterministicTime(self) -> float: ...
    @overload
    def FloatValue(self, arg0) -> float: ...
    @overload
    def FloatValue(self, arg0: float) -> float: ...
    def HasResponse(self) -> bool: ...
    def NumBinaryPropagations(self) -> int: ...
    def NumBooleans(self) -> int: ...
    def NumBranches(self) -> int: ...
    def NumConflicts(self) -> int: ...
    def NumIntegerPropagations(self) -> int: ...
    def ObjectiveValue(self) -> float: ...
    def OnSolutionCallback(self) -> None: ...
    def Response(self, *args, **kwargs): ...
    def SolutionBooleanValue(self, index: int) -> bool: ...
    def SolutionIntegerValue(self, index: int) -> int: ...
    def StopSearch(self) -> None: ...
    def UserTime(self) -> float: ...
    @overload
    def Value(self, arg0) -> int: ...
    @overload
    def Value(self, arg0: int) -> int: ...
    def WallTime(self) -> float: ...

class SolveWrapper:
    def __init__(self) -> None: ...
    def add_best_bound_callback(self, best_bound_callback: Callable[[float], None]) -> None: ...
    def add_log_callback(self, log_callback: Callable[[str], None]) -> None: ...
    def add_solution_callback(self, callback: SolutionCallback) -> None: ...
    def clear_solution_callback(self, arg0: SolutionCallback) -> None: ...
    def set_parameters(self, parameters) -> None: ...
    def solve(self, *args, **kwargs): ...
    def solve_and_return_response_wrapper(self, arg0) -> ResponseWrapper: ...
    def stop_search(self) -> None: ...

class SumArray(LinearExpr):
    def __init__(self, arg0: list[LinearExpr], arg1: int, arg2: float) -> None: ...
    @overload
    def __add__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __add__(self, arg0: int) -> LinearExpr: ...
    @overload
    def __add__(self, other: float) -> LinearExpr: ...
    @overload
    def __iadd__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __iadd__(self, arg0: int) -> LinearExpr: ...
    @overload
    def __iadd__(self, other: float) -> LinearExpr: ...
    @overload
    def __isub__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __isub__(self, arg0: int) -> LinearExpr: ...
    @overload
    def __isub__(self, other: float) -> LinearExpr: ...
    @overload
    def __radd__(self, cst: int) -> LinearExpr: ...
    @overload
    def __radd__(self, cst: float) -> LinearExpr: ...
    @overload
    def __sub__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __sub__(self, cst: int) -> LinearExpr: ...
    @overload
    def __sub__(self, cst: float) -> LinearExpr: ...
    @property
    def double_offset(self) -> float: ...
    @property
    def int_offset(self) -> int: ...
    @property
    def num_exprs(self) -> int: ...
