"""
Schedule Visualization System for Dental Implant Manufacturing
Creates Gantt charts and other visualizations for job scheduling
"""
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import numpy as np
import json

class ScheduleVisualizer:
    def __init__(self, jobs_data, schedule_data, start_date=None):
        """
        Initialize the visualizer with job and schedule data
        
        Args:
            jobs_data: List of job dictionaries from job_loader
            schedule_data: Schedule data from the solver
            start_date: Starting date for the schedule (default: today)
        """
        self.jobs_data = jobs_data
        self.schedule_data = schedule_data
        self.start_date = start_date or datetime.now().date()
        
        # Color schemes for different visualizations
        self.team_colors = {
            'red': '#FF6B6B',
            'blue': '#4ECDC4', 
            'green': '#45B7D1'
        }
        
        self.job_type_colors = {
            'Standard_Implant': '#FFD93D',
            'Custom_Implant': '#6BCF7F',
            'Abutment_Set': '#FF8C42'
        }
        
        self.priority_colors = {
            'High': '#FF4757',
            'Medium': '#FFA502',
            'Low': '#2ED573'
        }
        
        # Employee assignments
        self.employees = {
            'red': ['John', 'Adam'],
            'blue': ['Bob', 'Fred', 'Ted'],
            'green': ['Jane', 'Bea']
        }

    def create_gantt_by_employee(self, save_path="gantt_by_employee.html"):
        """Create a Gantt chart showing schedule by employee"""
        
        fig = go.Figure()
        
        # Prepare data for Gantt chart
        gantt_data = []
        
        for day, day_schedule in self.schedule_data.items():
            current_date = self.start_date + timedelta(days=day)
            
            for emp_id, tasks in day_schedule.items():
                employee_name = self.get_employee_name(emp_id)
                
                for task in tasks:
                    start_time = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=task['start_hour'])
                    end_time = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=task['end_hour'])
                    
                    gantt_data.append({
                        'Employee': employee_name,
                        'Start': start_time,
                        'Finish': end_time,
                        'Job': task['job_id'],
                        'Job_Name': task['job_name'],
                        'Team': task['team'],
                        'Duration': task['duration']
                    })
        
        # Create Gantt chart
        df = pd.DataFrame(gantt_data)
        
        if not df.empty:
            fig = px.timeline(df, 
                            x_start="Start", 
                            x_end="Finish", 
                            y="Employee",
                            color="Team",
                            color_discrete_map=self.team_colors,
                            hover_data=["Job", "Job_Name", "Duration"],
                            title="Dental Implant Manufacturing Schedule - By Employee")
            
            fig.update_layout(
                xaxis_title="Time",
                yaxis_title="Employee",
                height=600,
                showlegend=True
            )
            
            fig.write_html(save_path)
            print(f"Employee Gantt chart saved to {save_path}")
        
        return fig

    def create_gantt_by_job(self, save_path="gantt_by_job.html"):
        """Create a Gantt chart showing schedule by job"""
        
        # Prepare data for job-based Gantt
        job_gantt_data = []
        
        for day, day_schedule in self.schedule_data.items():
            current_date = self.start_date + timedelta(days=day)
            
            for emp_id, tasks in day_schedule.items():
                employee_name = self.get_employee_name(emp_id)
                
                for task in tasks:
                    start_time = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=task['start_hour'])
                    end_time = datetime.combine(current_date, datetime.min.time()) + timedelta(hours=task['end_hour'])
                    
                    # Get job type for coloring
                    job_type = self.get_job_type(task['job_id'])
                    
                    job_gantt_data.append({
                        'Job': f"{task['job_id']} - {task['job_name']}",
                        'Start': start_time,
                        'Finish': end_time,
                        'Employee': employee_name,
                        'Team': task['team'],
                        'Job_Type': job_type,
                        'Task_Step': f"Step {task['task'] + 1}",
                        'Duration': task['duration']
                    })
        
        df = pd.DataFrame(job_gantt_data)
        
        if not df.empty:
            fig = px.timeline(df,
                            x_start="Start",
                            x_end="Finish", 
                            y="Job",
                            color="Job_Type",
                            color_discrete_map=self.job_type_colors,
                            hover_data=["Employee", "Team", "Task_Step", "Duration"],
                            title="Dental Implant Manufacturing Schedule - By Job")
            
            fig.update_layout(
                xaxis_title="Time",
                yaxis_title="Job",
                height=800,
                showlegend=True
            )
            
            fig.write_html(save_path)
            print(f"Job Gantt chart saved to {save_path}")
        
        return fig

    def create_team_workload_chart(self, save_path="team_workload.html"):
        """Create a chart showing workload distribution by team"""
        
        # Calculate daily workload by team
        team_workload = {}
        
        for day, day_schedule in self.schedule_data.items():
            date_str = (self.start_date + timedelta(days=day)).strftime('%Y-%m-%d')
            team_workload[date_str] = {'red': 0, 'blue': 0, 'green': 0}
            
            for emp_id, tasks in day_schedule.items():
                for task in tasks:
                    team = task['team']
                    team_workload[date_str][team] += task['duration']
        
        # Convert to DataFrame
        df_workload = pd.DataFrame(team_workload).T
        df_workload.index = pd.to_datetime(df_workload.index)
        
        # Create stacked bar chart
        fig = go.Figure()
        
        for team in ['red', 'blue', 'green']:
            fig.add_trace(go.Bar(
                name=f"{team.title()} Team",
                x=df_workload.index,
                y=df_workload[team],
                marker_color=self.team_colors[team]
            ))
        
        fig.update_layout(
            barmode='group',
            title="Daily Team Workload Distribution",
            xaxis_title="Date",
            yaxis_title="Hours",
            height=500
        )
        
        fig.write_html(save_path)
        print(f"Team workload chart saved to {save_path}")
        
        return fig

    def create_job_priority_dashboard(self, save_path="job_priority_dashboard.html"):
        """Create a dashboard showing job priorities and completion status"""
        
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=("Job Priority Distribution", "Job Type Distribution", 
                          "Completion Timeline", "Team Utilization"),
            specs=[[{"type": "pie"}, {"type": "pie"}],
                   [{"type": "scatter"}, {"type": "bar"}]]
        )
        
        # Priority distribution
        priority_counts = {}
        job_type_counts = {}
        
        for job in self.jobs_data:
            priority = job.get('priority', 'Unknown')
            job_type = job.get('job_type', 'Unknown')
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
            job_type_counts[job_type] = job_type_counts.get(job_type, 0) + 1
        
        # Priority pie chart
        fig.add_trace(go.Pie(
            labels=list(priority_counts.keys()),
            values=list(priority_counts.values()),
            marker_colors=[self.priority_colors.get(p, '#999999') for p in priority_counts.keys()],
            name="Priority"
        ), row=1, col=1)
        
        # Job type pie chart
        fig.add_trace(go.Pie(
            labels=list(job_type_counts.keys()),
            values=list(job_type_counts.values()),
            marker_colors=[self.job_type_colors.get(jt, '#999999') for jt in job_type_counts.keys()],
            name="Job Type"
        ), row=1, col=2)
        
        # Completion timeline (placeholder - would need completion data from solver)
        completion_dates = []
        job_names = []
        
        for job in self.jobs_data:
            completion_dates.append(job['completion_date'])
            job_names.append(job['job_id'])
        
        fig.add_trace(go.Scatter(
            x=completion_dates,
            y=job_names,
            mode='markers',
            marker=dict(size=10),
            name="Due Dates"
        ), row=2, col=1)
        
        # Team utilization
        total_hours_by_team = {'red': 0, 'blue': 0, 'green': 0}
        
        for day_schedule in self.schedule_data.values():
            for tasks in day_schedule.values():
                for task in tasks:
                    total_hours_by_team[task['team']] += task['duration']
        
        fig.add_trace(go.Bar(
            x=list(total_hours_by_team.keys()),
            y=list(total_hours_by_team.values()),
            marker_color=[self.team_colors[team] for team in total_hours_by_team.keys()],
            name="Total Hours"
        ), row=2, col=2)
        
        fig.update_layout(
            title_text="Dental Implant Manufacturing Dashboard",
            height=800,
            showlegend=False
        )
        
        fig.write_html(save_path)
        print(f"Priority dashboard saved to {save_path}")
        
        return fig

    def get_employee_name(self, emp_id):
        """Get employee name from ID"""
        all_employees = []
        for team_employees in self.employees.values():
            all_employees.extend(team_employees)
        
        if emp_id < len(all_employees):
            return all_employees[emp_id]
        return f"Employee_{emp_id}"

    def get_job_type(self, job_id):
        """Get job type from job ID"""
        for job in self.jobs_data:
            if job['job_id'] == job_id:
                return job.get('job_type', 'Unknown')
        return 'Unknown'

    def create_all_visualizations(self):
        """Create all visualizations and save them"""
        print("Creating schedule visualizations...")
        
        self.create_gantt_by_employee()
        self.create_gantt_by_job()
        self.create_team_workload_chart()
        self.create_job_priority_dashboard()
        
        print("\nAll visualizations created successfully!")
        print("Files generated:")
        print("- gantt_by_employee.html")
        print("- gantt_by_job.html") 
        print("- team_workload.html")
        print("- job_priority_dashboard.html")

def create_schedule_visualizations(jobs_data, schedule_data, start_date=None):
    """
    Convenience function to create all visualizations
    
    Args:
        jobs_data: List of job dictionaries
        schedule_data: Schedule data from solver
        start_date: Starting date for schedule
    """
    visualizer = ScheduleVisualizer(jobs_data, schedule_data, start_date)
    visualizer.create_all_visualizations()
    return visualizer
