"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import ortools.math_opt.model_parameters_pb2
import ortools.math_opt.model_pb2
import ortools.math_opt.parameters_pb2
import ortools.math_opt.result_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class SolverResourcesProto(google.protobuf.message.Message):
    """This message is used to specify some hints on the resources a remote solve is
    expected to use. These parameters are hints and may be ignored by the remote
    server (in particular in case of solve in a local subprocess, for example).

    When using SolveService.Solve and SolveService.ComputeInfeasibleSubsystem,
    these hints are mostly optional as some defaults will be computed based on
    the other parameters.

    When using SolveService.StreamSolve these hints are used to dimension the
    resources available during the execution of every action; thus it is
    recommended to set them.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CPU_FIELD_NUMBER: builtins.int
    RAM_FIELD_NUMBER: builtins.int
    cpu: builtins.float
    """The number of solver threads that are expected to actually execute in
    parallel. Must be finite and >0.0.

    For example a value of 3.0 means that if the solver has 5 threads that can
    execute we expect at least 3 of these threads to be scheduled in parallel
    for any given time slice of the operating system scheduler.

    A fractional value indicates that we don't expect the operating system to
    constantly schedule the solver's work. For example with 0.5 we would expect
    the solver's threads to be scheduled half the time.

    This parameter is usually used in conjunction with
    SolveParametersProto.threads. For some solvers like Gurobi it makes sense
    to use SolverResourcesProto.cpu = SolveParametersProto.threads. For other
    solvers like CP-SAT, it may makes sense to use a value lower than the
    number of threads as not all threads may be ready to be scheduled at the
    same time. It is better to consult each solver documentation to set this
    parameter.

    Note that if the SolveParametersProto.threads is not set then this
    parameter should also be left unset.
    """
    ram: builtins.float
    """The limit of RAM for the solve in bytes. Must be finite and >=1.0 (even
    though it should in practice be much larger).
    """
    def __init__(
        self,
        *,
        cpu: builtins.float | None = ...,
        ram: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_cpu", b"_cpu", "_ram", b"_ram", "cpu", b"cpu", "ram", b"ram"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_cpu", b"_cpu", "_ram", b"_ram", "cpu", b"cpu", "ram", b"ram"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_cpu", b"_cpu"]) -> typing.Literal["cpu"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_ram", b"_ram"]) -> typing.Literal["ram"] | None: ...

global___SolverResourcesProto = SolverResourcesProto

@typing.final
class SolveRequest(google.protobuf.message.Message):
    """Request for a unary remote solve in MathOpt."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SOLVER_TYPE_FIELD_NUMBER: builtins.int
    MODEL_FIELD_NUMBER: builtins.int
    RESOURCES_FIELD_NUMBER: builtins.int
    INITIALIZER_FIELD_NUMBER: builtins.int
    PARAMETERS_FIELD_NUMBER: builtins.int
    MODEL_PARAMETERS_FIELD_NUMBER: builtins.int
    solver_type: ortools.math_opt.parameters_pb2.SolverTypeProto.ValueType
    """Solver type to numerically solve the problem. Note that if a solver does
    not support a specific feautre in the model, the optimization procedure
    won't be successful.
    """
    @property
    def model(self) -> ortools.math_opt.model_pb2.ModelProto:
        """A mathematical representation of the optimization problem to solve."""

    @property
    def resources(self) -> global___SolverResourcesProto:
        """Hints on resources requested for the solve."""

    @property
    def initializer(self) -> ortools.math_opt.parameters_pb2.SolverInitializerProto: ...
    @property
    def parameters(self) -> ortools.math_opt.parameters_pb2.SolveParametersProto:
        """Parameters to control a single solve. The enable_output parameter is
        handled specifically. For solvers that support messages callbacks, setting
        it to true will have the server register a message callback. The resulting
        messages will be returned in SolveResponse.messages. For other
        solvers, setting enable_output to true will result in an error.
        """

    @property
    def model_parameters(self) -> ortools.math_opt.model_parameters_pb2.ModelSolveParametersProto:
        """Parameters to control a single solve that are specific to the input model
        (see SolveParametersProto for model independent parameters).
        """

    def __init__(
        self,
        *,
        solver_type: ortools.math_opt.parameters_pb2.SolverTypeProto.ValueType = ...,
        model: ortools.math_opt.model_pb2.ModelProto | None = ...,
        resources: global___SolverResourcesProto | None = ...,
        initializer: ortools.math_opt.parameters_pb2.SolverInitializerProto | None = ...,
        parameters: ortools.math_opt.parameters_pb2.SolveParametersProto | None = ...,
        model_parameters: ortools.math_opt.model_parameters_pb2.ModelSolveParametersProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["initializer", b"initializer", "model", b"model", "model_parameters", b"model_parameters", "parameters", b"parameters", "resources", b"resources"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["initializer", b"initializer", "model", b"model", "model_parameters", b"model_parameters", "parameters", b"parameters", "resources", b"resources", "solver_type", b"solver_type"]) -> None: ...

global___SolveRequest = SolveRequest

@typing.final
class SolveResponse(google.protobuf.message.Message):
    """Response for a unary remote solve in MathOpt."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RESULT_FIELD_NUMBER: builtins.int
    STATUS_FIELD_NUMBER: builtins.int
    MESSAGES_FIELD_NUMBER: builtins.int
    @property
    def result(self) -> ortools.math_opt.result_pb2.SolveResultProto:
        """Description of the output of solving the model in the request."""

    @property
    def status(self) -> global___StatusProto:
        """The absl::Status returned by the solver. It should never be OK when set."""

    @property
    def messages(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.str]:
        """If SolveParametersProto.enable_output has been used, this will contain log
        messages for solvers that support message callbacks.
        """

    def __init__(
        self,
        *,
        result: ortools.math_opt.result_pb2.SolveResultProto | None = ...,
        status: global___StatusProto | None = ...,
        messages: collections.abc.Iterable[builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["result", b"result", "status", b"status", "status_or", b"status_or"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["messages", b"messages", "result", b"result", "status", b"status", "status_or", b"status_or"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["status_or", b"status_or"]) -> typing.Literal["result", "status"] | None: ...

global___SolveResponse = SolveResponse

@typing.final
class StatusProto(google.protobuf.message.Message):
    """The streamed version of absl::Status."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    CODE_FIELD_NUMBER: builtins.int
    MESSAGE_FIELD_NUMBER: builtins.int
    code: builtins.int
    """The status code, one of the absl::StatusCode."""
    message: builtins.str
    """The status message."""
    def __init__(
        self,
        *,
        code: builtins.int = ...,
        message: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["code", b"code", "message", b"message"]) -> None: ...

global___StatusProto = StatusProto
