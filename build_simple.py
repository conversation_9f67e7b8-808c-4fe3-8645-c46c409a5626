#!/usr/bin/env python3
"""
Simple build script for creating standalone executables
This creates a distributable folder with all necessary files
"""
import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def print_header(message):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {message}")
    print("=" * 60)

def print_step(step, message):
    """Print a formatted step"""
    print(f"\n[Step {step}] {message}")

def print_success(message):
    """Print success message"""
    print(f"✓ {message}")

def print_error(message):
    """Print error message"""
    print(f"✗ ERROR: {message}")

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"Running: {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print_error(f"Failed to {description.lower()}")
            print(f"Error: {result.stderr}")
            return False
        print_success(f"{description} completed")
        return True
    except Exception as e:
        print_error(f"Failed to {description.lower()}: {e}")
        return False

def clean_build():
    """Clean previous build artifacts"""
    print_step(1, "Cleaning Previous Build")
    
    dirs_to_clean = ["build", "dist"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print_success(f"Cleaned {dir_name}/")
            except Exception as e:
                print_error(f"Could not clean {dir_name}/: {e}")
                return False
    return True

def build_gui_exe():
    """Build GUI executable"""
    print_step(2, "Building GUI Executable")

    command = (
        f"{sys.executable} -m PyInstaller "
        f"--onefile "
        f"--windowed "
        f"--name DentalSchedulerGUI "
        f"--icon icon.ico "
        f"--add-data Job_Upload_Template.xlsx;. "
        f"--add-data README.md;. "
        f"--hidden-import ortools "
        f"--hidden-import ortools.sat "
        f"--hidden-import ortools.sat.python "
        f"--hidden-import ortools.sat.python.cp_model "
        f"dental_scheduler_gui.py"
    )

    return run_command(command, "Building GUI executable")

def build_cli_exe():
    """Build CLI executable"""
    print_step(3, "Building CLI Executable")

    command = (
        f"{sys.executable} -m PyInstaller "
        f"--onefile "
        f"--console "
        f"--name DentalSchedulerCLI "
        f"--icon icon.ico "
        f"--add-data Job_Upload_Template.xlsx;. "
        f"--add-data README.md;. "
        f"--hidden-import ortools "
        f"--hidden-import ortools.sat "
        f"--hidden-import ortools.sat.python "
        f"--hidden-import ortools.sat.python.cp_model "
        f"Main.py"
    )

    return run_command(command, "Building CLI executable")

def create_distribution_package():
    """Create a complete distribution package"""
    print_step(4, "Creating Distribution Package")
    
    # Create distribution directory
    dist_dir = "DentalScheduler_Portable"
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    
    os.makedirs(dist_dir)
    
    # Copy executables
    try:
        if os.path.exists("dist/DentalSchedulerGUI.exe"):
            shutil.copy2("dist/DentalSchedulerGUI.exe", dist_dir)
            print_success("Copied GUI executable")
        
        if os.path.exists("dist/DentalSchedulerCLI.exe"):
            shutil.copy2("dist/DentalSchedulerCLI.exe", dist_dir)
            print_success("Copied CLI executable")
        
        # Copy documentation and templates
        files_to_copy = [
            "Job_Upload_Template.xlsx",
            "README.md",
            "PROJECT_STRUCTURE.md",
            "INSTALLATION_GUIDE.md",
            "LICENSE.txt",
            "requirements.txt"
        ]
        
        for file_name in files_to_copy:
            if os.path.exists(file_name):
                shutil.copy2(file_name, dist_dir)
                print_success(f"Copied {file_name}")
        
        # Create launcher batch files
        gui_launcher = f"""@echo off
title Dental Implant Manufacturing Scheduler
echo Starting Dental Scheduler...
DentalSchedulerGUI.exe
"""
        
        cli_launcher = f"""@echo off
title Dental Implant Manufacturing Scheduler - CLI
echo Starting Dental Scheduler (Command Line)...
DentalSchedulerCLI.exe
pause
"""
        
        with open(f"{dist_dir}/Start_GUI.bat", "w") as f:
            f.write(gui_launcher)
        
        with open(f"{dist_dir}/Start_CLI.bat", "w") as f:
            f.write(cli_launcher)
        
        print_success("Created launcher scripts")
        
        # Create README for distribution
        dist_readme = f"""# Dental Implant Manufacturing Scheduler - Portable Version

## Quick Start
1. Double-click `Start_GUI.bat` to launch the application
2. Or double-click `DentalSchedulerGUI.exe` directly

## Files Included
- `DentalSchedulerGUI.exe` - Main application (GUI)
- `DentalSchedulerCLI.exe` - Command line version
- `Start_GUI.bat` - Easy launcher for GUI
- `Start_CLI.bat` - Easy launcher for CLI
- `Job_Upload_Template.xlsx` - Sample job template
- `README.md` - Complete user guide
- `LICENSE.txt` - Software license

## No Installation Required
This is a portable version that runs without installation.
Just extract to any folder and run!

## System Requirements
- Windows 10 or later (64-bit)
- No Python installation required
- No additional software needed

## Getting Started
1. Use the sample template `Job_Upload_Template.xlsx`
2. Modify it with your dental implant jobs
3. Load it in the application
4. Generate optimized schedules and visualizations

For complete documentation, see README.md
"""
        
        with open(f"{dist_dir}/README_PORTABLE.txt", "w") as f:
            f.write(dist_readme)
        
        print_success("Created distribution README")
        
        return True
        
    except Exception as e:
        print_error(f"Failed to create distribution package: {e}")
        return False

def create_zip_package():
    """Create a ZIP file for easy distribution"""
    print_step(5, "Creating ZIP Package")
    
    try:
        import zipfile
        
        zip_name = "DentalScheduler_Portable.zip"
        if os.path.exists(zip_name):
            os.remove(zip_name)
        
        with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk("DentalScheduler_Portable"):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, ".")
                    zipf.write(file_path, arc_name)
        
        zip_size = os.path.getsize(zip_name)
        print_success(f"Created ZIP package: {zip_name} ({zip_size:,} bytes)")
        return True
        
    except Exception as e:
        print_error(f"Failed to create ZIP package: {e}")
        return False

def print_summary():
    """Print build summary"""
    print_header("Build Summary")
    
    print("\n📁 Distribution Files Created:")
    
    if os.path.exists("DentalScheduler_Portable"):
        print("  📂 DentalScheduler_Portable/ - Portable application folder")
        for item in os.listdir("DentalScheduler_Portable"):
            if item.endswith('.exe'):
                size = os.path.getsize(f"DentalScheduler_Portable/{item}")
                print(f"    • {item} ({size:,} bytes)")
            else:
                print(f"    • {item}")
    
    if os.path.exists("DentalScheduler_Portable.zip"):
        zip_size = os.path.getsize("DentalScheduler_Portable.zip")
        print(f"  📦 DentalScheduler_Portable.zip ({zip_size:,} bytes)")
    
    print("\n🚀 Distribution Options:")
    print("  1. Share the ZIP file for easy download")
    print("  2. Share the entire DentalScheduler_Portable folder")
    print("  3. Users just extract and run - no installation needed!")
    
    print("\n📋 User Instructions:")
    print("  1. Extract DentalScheduler_Portable.zip")
    print("  2. Double-click Start_GUI.bat")
    print("  3. Start scheduling dental implant jobs!")

def main():
    """Main build process"""
    print_header("Dental Scheduler - Portable Build")
    
    start_time = time.time()
    
    # Check if PyInstaller is available
    try:
        subprocess.run([sys.executable, "-m", "PyInstaller", "--version"], 
                      capture_output=True, check=True)
    except:
        print_error("PyInstaller not found. Installing...")
        if not run_command(f"{sys.executable} -m pip install pyinstaller", 
                          "Installing PyInstaller"):
            return False
    
    # Run build steps
    steps = [
        clean_build,
        build_gui_exe,
        build_cli_exe,
        create_distribution_package,
        create_zip_package,
    ]
    
    for i, step in enumerate(steps, 1):
        try:
            if not step():
                print_error(f"Build failed at step {i}")
                return False
        except KeyboardInterrupt:
            print_error("Build cancelled by user")
            return False
        except Exception as e:
            print_error(f"Unexpected error in step {i}: {e}")
            return False
    
    # Calculate build time
    build_time = time.time() - start_time
    minutes = int(build_time // 60)
    seconds = int(build_time % 60)
    
    print_header("Build Completed Successfully! 🎉")
    print(f"Total build time: {minutes}m {seconds}s")
    
    print_summary()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nBuild cancelled by user")
        sys.exit(1)
