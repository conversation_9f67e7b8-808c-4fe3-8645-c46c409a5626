MIT License

Copyright (c) 2025 Dental Manufacturing Solutions

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

DENTAL IMPLANT MANUFACTURING SCHEDULER
=====================================

This software is designed for dental implant manufacturing operations to 
optimize job scheduling across multiple teams and resources.

Features:
- Advanced constraint-based scheduling optimization
- Interactive Gantt chart visualizations  
- Excel-based job template system
- Multi-team workflow management (Design, Manufacturing, Quality Control)
- Real-time capacity planning and resource allocation
- Professional reporting and analytics

For support and documentation, please refer to the included README.md file
or visit our project repository.

Third-Party Libraries:
- OR-Tools (Apache License 2.0)
- Pandas (BSD License)
- Plotly (MIT License)
- CustomTkinter (MIT License)
- OpenPyXL (MIT License)

All third-party libraries retain their original licenses and copyrights.
