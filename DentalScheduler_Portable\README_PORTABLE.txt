# Dental Implant Manufacturing Scheduler - Portable Version

## 🚀 Quick Start
1. Double-click `Start_GUI.bat` to launch the application
2. Or double-click `DentalSchedulerGUI.exe` directly

## 📁 Files Included
- `DentalSchedulerGUI.exe` - Main application (GUI)
- `DentalSchedulerCLI.exe` - Command line version
- `Start_GUI.bat` - Easy launcher for GUI
- `Start_CLI.bat` - Easy launcher for CLI
- `Job_Upload_Template.xlsx` - Sample job template
- `README.md` - Complete user guide

## ✅ No Installation Required
This is a portable version that runs without installation.
Just extract to any folder and run!

## 💻 System Requirements
- Windows 10 or later (64-bit)
- No Python installation required
- No additional software needed

## 🦷 Getting Started
1. Use the sample template `Job_Upload_Template.xlsx`
2. Modify it with your dental implant jobs
3. Load it in the application
4. Generate optimized schedules and visualizations

## 📊 Features
- Advanced constraint-based scheduling optimization
- Interactive Gantt chart visualizations
- Excel-based job template system
- Multi-team workflow management (Design, Manufacturing, Quality Control)
- Real-time capacity planning and resource allocation
- Professional reporting and analytics

## 🔧 Usage
### GUI Application (Recommended)
- Double-click `Start_GUI.bat` or `DentalSchedulerGUI.exe`
- Modern interface with tabs and buttons
- Load Excel files, process jobs, create visualizations
- Launch interactive web dashboard

### Command Line Interface
- Double-click `Start_CLI.bat` or `DentalSchedulerCLI.exe`
- Text-based interface for advanced users
- Automatic processing of Job_Upload_Template.xlsx
- Generates same visualizations as GUI

## 📈 Output Files
The application creates these files when processing jobs:
- `gantt_by_employee.html` - Employee schedule Gantt chart
- `gantt_by_job.html` - Job progress Gantt chart
- `team_workload.html` - Team workload analysis
- `job_priority_dashboard.html` - Executive dashboard
- `schedule_dashboard.html` - Unified web dashboard

## 🆘 Support
For complete documentation, see README.md
For technical issues, check system requirements and file permissions

## 📄 License
MIT License - See README.md for full license text

---
Dental Implant Manufacturing Scheduler v1.0.0
Professional scheduling solution for dental manufacturing operations
