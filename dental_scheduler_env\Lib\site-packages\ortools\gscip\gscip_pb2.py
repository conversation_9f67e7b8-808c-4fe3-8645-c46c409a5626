# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/gscip/gscip.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/gscip/gscip.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x19ortools/gscip/gscip.proto\x12\x13operations_research\"\x92\r\n\x0fGScipParameters\x12?\n\x08\x65mphasis\x18\x01 \x01(\x0e\x32-.operations_research.GScipParameters.Emphasis\x12L\n\nheuristics\x18\x02 \x01(\x0e\x32\x33.operations_research.GScipParameters.MetaParamValueH\x00\x88\x01\x01\x12J\n\x08presolve\x18\x03 \x01(\x0e\x32\x33.operations_research.GScipParameters.MetaParamValueH\x01\x88\x01\x01\x12L\n\nseparating\x18\x04 \x01(\x0e\x32\x33.operations_research.GScipParameters.MetaParamValueH\x02\x88\x01\x01\x12I\n\x0b\x62ool_params\x18\x05 \x03(\x0b\x32\x34.operations_research.GScipParameters.BoolParamsEntry\x12G\n\nint_params\x18\x06 \x03(\x0b\x32\x33.operations_research.GScipParameters.IntParamsEntry\x12I\n\x0blong_params\x18\x07 \x03(\x0b\x32\x34.operations_research.GScipParameters.LongParamsEntry\x12I\n\x0breal_params\x18\x08 \x03(\x0b\x32\x34.operations_research.GScipParameters.RealParamsEntry\x12I\n\x0b\x63har_params\x18\t \x03(\x0b\x32\x34.operations_research.GScipParameters.CharParamsEntry\x12M\n\rstring_params\x18\n \x03(\x0b\x32\x36.operations_research.GScipParameters.StringParamsEntry\x12\x1b\n\x0esilence_output\x18\x0b \x01(\x08H\x03\x88\x01\x01\x12$\n\x1cprint_detailed_solving_stats\x18\x0c \x01(\x08\x12\x18\n\x10print_scip_model\x18\r \x01(\x08\x12\x1c\n\x14search_logs_filename\x18\x0e \x01(\t\x12\'\n\x1f\x64\x65tailed_solving_stats_filename\x18\x0f \x01(\t\x12\x1b\n\x13scip_model_filename\x18\x10 \x01(\t\x12\x1a\n\rnum_solutions\x18\x11 \x01(\x05H\x04\x88\x01\x01\x12\x1c\n\x0fobjective_limit\x18\x12 \x01(\x01H\x05\x88\x01\x01\x1a\x31\n\x0f\x42oolParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\x1a\x30\n\x0eIntParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\x31\n\x0fLongParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\x1a\x31\n\x0fRealParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x01:\x02\x38\x01\x1a\x31\n\x0f\x43harParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x33\n\x11StringParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xac\x01\n\x08\x45mphasis\x12\x14\n\x10\x44\x45\x46\x41ULT_EMPHASIS\x10\x00\x12\x0b\n\x07\x43OUNTER\x10\x01\x12\r\n\tCP_SOLVER\x10\x02\x12\x0c\n\x08\x45\x41SY_CIP\x10\x03\x12\x0f\n\x0b\x46\x45\x41SIBILITY\x10\x04\x12\x0b\n\x07HARD_LP\x10\x05\x12\x0e\n\nOPTIMALITY\x10\x06\x12\x0e\n\nPHASE_FEAS\x10\x07\x12\x11\n\rPHASE_IMPROVE\x10\x08\x12\x0f\n\x0bPHASE_PROOF\x10\t\"Q\n\x0eMetaParamValue\x12\x1c\n\x18\x44\x45\x46\x41ULT_META_PARAM_VALUE\x10\x00\x12\x0e\n\nAGGRESSIVE\x10\x01\x12\x08\n\x04\x46\x41ST\x10\x02\x12\x07\n\x03OFF\x10\x03\x42\r\n\x0b_heuristicsB\x0b\n\t_presolveB\r\n\x0b_separatingB\x11\n\x0f_silence_outputB\x10\n\x0e_num_solutionsB\x12\n\x10_objective_limit\"\xa8\x02\n\x11GScipSolvingStats\x12\x16\n\x0e\x62\x65st_objective\x18\x01 \x01(\x01\x12\x12\n\nbest_bound\x18\x02 \x01(\x01\x12!\n\x19primal_simplex_iterations\x18\x03 \x01(\x03\x12\x1f\n\x17\x64ual_simplex_iterations\x18\x04 \x01(\x03\x12\x1a\n\x12\x62\x61rrier_iterations\x18\n \x01(\x03\x12\x1b\n\x13total_lp_iterations\x18\x05 \x01(\x03\x12\x12\n\nnode_count\x18\x06 \x01(\x03\x12!\n\x19\x66irst_lp_relaxation_bound\x18\x07 \x01(\x01\x12\x17\n\x0froot_node_bound\x18\x08 \x01(\x01\x12\x1a\n\x12\x64\x65terministic_time\x18\t \x01(\x01\"\xcb\x03\n\x0bGScipOutput\x12\x37\n\x06status\x18\x01 \x01(\x0e\x32\'.operations_research.GScipOutput.Status\x12\x15\n\rstatus_detail\x18\x02 \x01(\t\x12\x35\n\x05stats\x18\x03 \x01(\x0b\x32&.operations_research.GScipSolvingStats\"\xb4\x02\n\x06Status\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x12\n\x0eUSER_INTERRUPT\x10\x01\x12\x0e\n\nNODE_LIMIT\x10\x02\x12\x14\n\x10TOTAL_NODE_LIMIT\x10\x03\x12\x14\n\x10STALL_NODE_LIMIT\x10\x04\x12\x0e\n\nTIME_LIMIT\x10\x05\x12\r\n\tMEM_LIMIT\x10\x06\x12\r\n\tGAP_LIMIT\x10\x07\x12\r\n\tSOL_LIMIT\x10\x08\x12\x12\n\x0e\x42\x45ST_SOL_LIMIT\x10\t\x12\x11\n\rRESTART_LIMIT\x10\n\x12\x0b\n\x07OPTIMAL\x10\x0b\x12\x0e\n\nINFEASIBLE\x10\x0c\x12\r\n\tUNBOUNDED\x10\r\x12\x0f\n\x0bINF_OR_UNBD\x10\x0e\x12\r\n\tTERMINATE\x10\x0f\x12\x1d\n\x19INVALID_SOLVER_PARAMETERS\x10\x10\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.gscip.gscip_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_GSCIPPARAMETERS_BOOLPARAMSENTRY']._loaded_options = None
  _globals['_GSCIPPARAMETERS_BOOLPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_GSCIPPARAMETERS_INTPARAMSENTRY']._loaded_options = None
  _globals['_GSCIPPARAMETERS_INTPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_GSCIPPARAMETERS_LONGPARAMSENTRY']._loaded_options = None
  _globals['_GSCIPPARAMETERS_LONGPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_GSCIPPARAMETERS_REALPARAMSENTRY']._loaded_options = None
  _globals['_GSCIPPARAMETERS_REALPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_GSCIPPARAMETERS_CHARPARAMSENTRY']._loaded_options = None
  _globals['_GSCIPPARAMETERS_CHARPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_GSCIPPARAMETERS_STRINGPARAMSENTRY']._loaded_options = None
  _globals['_GSCIPPARAMETERS_STRINGPARAMSENTRY']._serialized_options = b'8\001'
  _globals['_GSCIPPARAMETERS']._serialized_start=51
  _globals['_GSCIPPARAMETERS']._serialized_end=1733
  _globals['_GSCIPPARAMETERS_BOOLPARAMSENTRY']._serialized_start=1070
  _globals['_GSCIPPARAMETERS_BOOLPARAMSENTRY']._serialized_end=1119
  _globals['_GSCIPPARAMETERS_INTPARAMSENTRY']._serialized_start=1121
  _globals['_GSCIPPARAMETERS_INTPARAMSENTRY']._serialized_end=1169
  _globals['_GSCIPPARAMETERS_LONGPARAMSENTRY']._serialized_start=1171
  _globals['_GSCIPPARAMETERS_LONGPARAMSENTRY']._serialized_end=1220
  _globals['_GSCIPPARAMETERS_REALPARAMSENTRY']._serialized_start=1222
  _globals['_GSCIPPARAMETERS_REALPARAMSENTRY']._serialized_end=1271
  _globals['_GSCIPPARAMETERS_CHARPARAMSENTRY']._serialized_start=1273
  _globals['_GSCIPPARAMETERS_CHARPARAMSENTRY']._serialized_end=1322
  _globals['_GSCIPPARAMETERS_STRINGPARAMSENTRY']._serialized_start=1324
  _globals['_GSCIPPARAMETERS_STRINGPARAMSENTRY']._serialized_end=1375
  _globals['_GSCIPPARAMETERS_EMPHASIS']._serialized_start=1378
  _globals['_GSCIPPARAMETERS_EMPHASIS']._serialized_end=1550
  _globals['_GSCIPPARAMETERS_METAPARAMVALUE']._serialized_start=1552
  _globals['_GSCIPPARAMETERS_METAPARAMVALUE']._serialized_end=1633
  _globals['_GSCIPSOLVINGSTATS']._serialized_start=1736
  _globals['_GSCIPSOLVINGSTATS']._serialized_end=2032
  _globals['_GSCIPOUTPUT']._serialized_start=2035
  _globals['_GSCIPOUTPUT']._serialized_end=2494
  _globals['_GSCIPOUTPUT_STATUS']._serialized_start=2186
  _globals['_GSCIPOUTPUT_STATUS']._serialized_end=2494
# @@protoc_insertion_point(module_scope)
