from typing import Class<PERSON><PERSON>, Iterator, overload

class ElementDegreeSolutionGenerator:
    def __init__(self, arg0: SetCoverInvariant) -> None: ...
    @overload
    def next_solution(self) -> bool: ...
    @overload
    def next_solution(self, arg0) -> bool: ...
    @overload
    def next_solution(self, arg0: list[bool]) -> bool: ...
    def set_max_iterations(self, *args, **kwargs): ...

class GreedySolutionGenerator:
    def __init__(self, arg0: SetCoverInvariant) -> None: ...
    @overload
    def next_solution(self) -> bool: ...
    @overload
    def next_solution(self, arg0) -> bool: ...
    @overload
    def next_solution(self, arg0: list[bool]) -> bool: ...
    def set_max_iterations(self, *args, **kwargs): ...

class GuidedLocalSearch:
    def __init__(self, arg0: SetCoverInvariant) -> None: ...
    def initialize(self) -> None: ...
    @overload
    def next_solution(self) -> bool: ...
    @overload
    def next_solution(self, arg0, arg1: int) -> bool: ...
    @overload
    def next_solution(self, arg0: list[bool]) -> bool: ...
    def set_max_iterations(self, *args, **kwargs): ...

class GuidedTabuSearch:
    def __init__(self, arg0: SetCoverInvariant) -> None: ...
    def get_epsilon(self) -> float: ...
    def get_lagrangian_factor(self, factor: float) -> None: ...
    def get_penalty_factor(self) -> float: ...
    def get_tabu_list_size(self) -> int: ...
    def initialize(self) -> None: ...
    @overload
    def next_solution(self) -> bool: ...
    @overload
    def next_solution(self, arg0) -> bool: ...
    @overload
    def next_solution(self, arg0: list[bool]) -> bool: ...
    def set_epsilon(self, r: float) -> None: ...
    def set_lagrangian_factor(self) -> float: ...
    def set_max_iterations(self, *args, **kwargs): ...
    def set_penalty_factor(self, factor: float) -> None: ...
    def set_tabu_list_size(self, size: int) -> None: ...

class LazyElementDegreeSolutionGenerator:
    def __init__(self, arg0: SetCoverInvariant) -> None: ...
    @overload
    def next_solution(self) -> bool: ...
    @overload
    def next_solution(self, arg0) -> bool: ...
    @overload
    def next_solution(self, arg0: list[bool]) -> bool: ...
    def set_max_iterations(self, *args, **kwargs): ...

class RandomSolutionGenerator:
    def __init__(self, arg0: SetCoverInvariant) -> None: ...
    @overload
    def next_solution(self) -> bool: ...
    @overload
    def next_solution(self, arg0) -> bool: ...
    @overload
    def next_solution(self, arg0: list[bool]) -> bool: ...
    def set_max_iterations(self, *args, **kwargs): ...

class SetCoverDecision:
    @overload
    def __init__(self) -> None: ...
    @overload
    def __init__(self, subset: int, value: bool) -> None: ...
    def decision(self) -> bool: ...
    def subset(self) -> int: ...

class SetCoverInvariant:
    model: SetCoverModel
    def __init__(self, arg0: SetCoverModel) -> None: ...
    def check_consistency(self, arg0: consistency_level) -> bool: ...
    def clear(self) -> None: ...
    def clear_removability_information(self) -> None: ...
    def clear_trace(self) -> None: ...
    def compress_trace(self) -> None: ...
    def compute_coverage_in_focus(self, focus) -> list[int]: ...
    def compute_is_redundant(self, subset: int) -> bool: ...
    def cost(self) -> float: ...
    def coverage(self) -> list[int]: ...
    def deselect(self, subset: int, consistency: consistency_level) -> None: ...
    def export_solution_as_proto(self, *args, **kwargs): ...
    def import_solution_from_proto(self, arg0) -> None: ...
    def initialize(self) -> None: ...
    def is_redundant(self) -> list[bool]: ...
    def is_selected(self) -> list[bool]: ...
    def load_solution(self, arg0) -> None: ...
    def newly_non_removable_subsets(self, *args, **kwargs): ...
    def newly_removable_subsets(self, *args, **kwargs): ...
    def num_coverage_le_1_elements(self) -> list[int]: ...
    def num_free_elements(self) -> list[int]: ...
    def num_uncovered_elements(self) -> int: ...
    def recompute(self, arg0: consistency_level) -> None: ...
    def select(self, subset: int, consistency: consistency_level) -> None: ...
    def trace(self) -> list[SetCoverDecision]: ...

class SetCoverModel:
    def __init__(self) -> None: ...
    def ElementRange(self) -> Iterator[int]: ...
    def SubsetRange(self) -> Iterator[int]: ...
    def add_element_to_last_subset(self, element: int) -> None: ...
    def add_element_to_subset(self, subset: int, cost: int) -> None: ...
    def add_empty_subset(self, cost: float) -> None: ...
    def compute_column_deciles(self) -> list[int]: ...
    def compute_column_stats(self) -> SetCoverModelStats: ...
    def compute_cost_stats(self) -> SetCoverModelStats: ...
    def compute_feasibility(self) -> bool: ...
    def compute_row_deciles(self) -> list[int]: ...
    def compute_row_stats(self) -> SetCoverModelStats: ...
    def create_sparse_row_view(self) -> None: ...
    def export_model_as_proto(self, *args, **kwargs): ...
    def import_model_from_proto(self, arg0) -> None: ...
    def reserve_num_elements_in_subset(self, num_elements: int, subset: int) -> None: ...
    def resize_num_subsets(self, num_subsets: int) -> None: ...
    def set_name(self, arg0: str) -> None: ...
    def set_subset_cost(self, subset: int, cost: float) -> None: ...
    def sort_elements_in_subsets(self) -> None: ...
    @property
    def all_subsets(self) -> list[int]: ...
    @property
    def columns(self) -> list[list[int]]: ...
    @property
    def fill_rate(self) -> float: ...
    @property
    def name(self) -> str: ...
    @property
    def num_elements(self) -> int: ...
    @property
    def num_nonzeros(self) -> int: ...
    @property
    def num_subsets(self) -> int: ...
    @property
    def row_view_is_valid(self) -> bool: ...
    @property
    def rows(self) -> list[list[int]]: ...
    @property
    def subset_costs(self) -> list[float]: ...

class SetCoverModelStats:
    max: float
    mean: float
    median: float
    min: float
    stddev: float
    def __init__(self, *args, **kwargs) -> None: ...
    @property
    def to_string(self) -> str: ...
    @property
    def to_verbose_string(self) -> str: ...

class SteepestSearch:
    def __init__(self, arg0: SetCoverInvariant) -> None: ...
    @overload
    def next_solution(self) -> bool: ...
    @overload
    def next_solution(self, arg0) -> bool: ...
    @overload
    def next_solution(self, arg0: list[bool]) -> bool: ...
    def set_max_iterations(self, *args, **kwargs): ...

class TabuList:
    def __init__(self, size: int) -> None: ...
    def add(self, t: int) -> None: ...
    def contains(self, t: int) -> bool: ...
    def init(self, size: int) -> None: ...
    def size(self) -> int: ...

class TrivialSolutionGenerator:
    def __init__(self, arg0: SetCoverInvariant) -> None: ...
    def name(self) -> str: ...
    @overload
    def next_solution(self) -> bool: ...
    @overload
    def next_solution(self, arg0) -> bool: ...
    @overload
    def next_solution(self, arg0: list[bool]) -> bool: ...
    def set_max_iterations(self, *args, **kwargs): ...

class consistency_level:
    __members__: ClassVar[dict] = ...  # read-only
    COST_AND_COVERAGE: ClassVar[consistency_level] = ...
    FREE_AND_UNCOVERED: ClassVar[consistency_level] = ...
    REDUNDANCY: ClassVar[consistency_level] = ...
    __entries: ClassVar[dict] = ...
    def __init__(self, value: int) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __index__(self) -> int: ...
    def __int__(self) -> int: ...
    def __ne__(self, other: object) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def value(self) -> int: ...

@overload
def clear_most_covered_elements(arg0: int, arg1: SetCoverInvariant) -> list[int]: ...
@overload
def clear_most_covered_elements(arg0, arg1: int, arg2: SetCoverInvariant) -> list[int]: ...
@overload
def clear_random_subsets(arg0: int, arg1: SetCoverInvariant) -> list[int]: ...
@overload
def clear_random_subsets(arg0, arg1: int, arg2: SetCoverInvariant) -> list[int]: ...
def read_fimi_dat(arg0: str) -> SetCoverModel: ...
def read_orlib_rail(arg0: str) -> SetCoverModel: ...
def read_orlib_scp(arg0: str) -> SetCoverModel: ...
def read_set_cover_proto(arg0: str, arg1: bool) -> SetCoverModel: ...
def read_set_cover_solution_proto(*args, **kwargs): ...
def read_set_cover_solution_text(*args, **kwargs): ...
def write_orlib_rail(arg0: SetCoverModel, arg1: str) -> None: ...
def write_orlib_scp(arg0: SetCoverModel, arg1: str) -> None: ...
def write_set_cover_proto(arg0: SetCoverModel, arg1: str, arg2: bool) -> None: ...
def write_set_cover_solution_proto(arg0: SetCoverModel, arg1, int, util_intops, bool, std, arg2: str, arg3: bool) -> None: ...
def write_set_cover_solution_text(arg0: SetCoverModel, arg1, int, util_intops, bool, std, arg2: str) -> None: ...
