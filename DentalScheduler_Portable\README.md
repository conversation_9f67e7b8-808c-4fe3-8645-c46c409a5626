# Job Shop Scheduling System with Excel Integration

A team-based job scheduling system that optimizes work allocation across multiple teams while respecting employee constraints and deadlines.

## Overview

This system schedules jobs across three teams:
- **Red Team**: <PERSON>, <PERSON> (Design & Planning)
- **Blue Team**: <PERSON>, <PERSON>, <PERSON> (Development & Implementation) 
- **Green Team**: <PERSON>, <PERSON><PERSON> (Testing & Deployment)

## Features

- **Excel-based job input** - Easy job management through Excel templates
- **Multi-team scheduling** - Each job requires work from all three teams
- **Working hour constraints** - 9 AM to 5 PM, max 8 hours per employee per day
- **Deadline management** - All jobs must complete before their due dates
- **Optimized scheduling** - Minimizes total completion time
- **Detailed reporting** - Day-by-day schedules and job completion status

## Files

### 🖥️ **GUI Application**
- `dental_scheduler_gui.py` - Modern GUI application with dark theme
- `run_gui.py` - Simple launcher for the GUI

### 📊 **Core System**
- `Main.py` - Main scheduling system with Excel integration
- `job_loader.py` - Loads and validates jobs from Excel
- `schedule_visualizer.py` - Creates interactive visualizations
- `create_dashboard.py` - Generates unified web dashboard

### 📋 **Templates & Data**
- `create_job_template.py` - Creates the Excel template for job input
- `Job_Upload_Template.xlsx` - Excel template with sample dental jobs

## Quick Start

### 🖥️ **GUI Application (Recommended)**
```bash
python dental_scheduler_gui.py
```
**Features:**
- 🌙 **Modern Dark Theme**: Professional interface with CustomTkinter
- 📱 **Full Screen**: Optimized for desktop use
- 🎯 **Integrated Workflow**: Load jobs → Process → Visualize in one interface
- 📊 **Built-in Results**: View job summaries and schedule results
- 🌐 **Web Dashboard**: Launch interactive visualizations

### 📋 **Command Line (Advanced)**

#### 1. Create Excel Template
```bash
python create_job_template.py
```
This creates `Job_Upload_Template.xlsx` with sample jobs and instructions.

#### 2. Edit Jobs in Excel
Open `Job_Upload_Template.xlsx` and:
- Modify existing sample jobs or add new ones
- Each job must have tasks for all three teams (Red, Blue, Green)
- Use the dropdown for Priority field
- Ensure dates are in YYYY-MM-DD format
- Hours must be between 1-8 for each team

#### 3. Run Scheduling
```bash
python Main_with_Excel.py
```
This will:
- Load jobs from the Excel file
- Validate the job data
- Generate an optimized schedule
- Display results by day and employee
- Create interactive visualizations

#### 4. View Job Summary
```bash
python job_loader.py
```
This creates `jobs_summary.txt` with detailed job statistics.

## Excel Template Structure

### Jobs Sheet
| Column | Description | Example |
|--------|-------------|---------|
| Job_ID | Unique identifier | JOB001 |
| Job_Name | Descriptive name | Website Redesign |
| Description | Detailed description | Complete redesign of company website |
| Received_Date | Date received (YYYY-MM-DD) | 2025-07-16 |
| Due_Date | Completion deadline (YYYY-MM-DD) | 2025-07-19 |
| Priority | High/Medium/Low | High |
| Red_Team_Hours | Hours for Red team (1-8) | 4 |
| Red_Team_Description | Red team task description | UI/UX Design and mockups |
| Blue_Team_Hours | Hours for Blue team (1-8) | 6 |
| Blue_Team_Description | Blue team task description | Frontend development |
| Green_Team_Hours | Hours for Green team (1-8) | 3 |
| Green_Team_Description | Green team task description | Testing and deployment |

### Other Sheets
- **Instructions** - Detailed usage guidelines
- **Team_Info** - Team composition and capacity information
- **Validation** - Data validation lists for dropdowns

## Scheduling Constraints

1. **Working Hours**: 9:00 AM to 5:00 PM (8 hours per day)
2. **Daily Limits**: No employee works more than 8 hours per day
3. **Task Completion**: Individual tasks must complete within the same day
4. **Job Precedence**: Within each job, tasks follow Red → Blue → Green sequence
5. **Deadlines**: All jobs must complete before their due dates
6. **Team Assignment**: Tasks can only be assigned to appropriate team members

## Sample Output

```
Day 1 (2025-07-16):
--------------------------------------------------------------------------------
  John (red):
    09:00-12:00 JOB003 (Mobile App Update) Task0 (red team) [3h]
    Daily total: 3 hours

  Adam (red):
    09:00-11:00 JOB002 (Database Migration) Task0 (red team) [2h]
    11:00-15:00 JOB001 (Website Redesign) Task0 (red team) [4h]
    Daily total: 6 hours
...

Job Completion Status:
------------------------------------------------------------
  JOB001: Website Redesign
    Completes: 2025-07-18 (Due: 2025-07-19) ✓ On time
  JOB002: Database Migration
    Completes: 2025-07-17 (Due: 2025-07-18) ✓ On time
```

## Advanced Usage

### Custom Excel File
```bash
python Main_with_Excel.py my_jobs.xlsx
```

### Validation Only
```bash
python job_loader.py
```

### Template Recreation
```bash
python create_job_template.py
```

## 🖥️ GUI Features

### **Modern Interface**
- **🌙 Dark Theme**: Professional dark mode interface
- **📱 Full Screen**: Maximized window for optimal workspace
- **🎨 Modern Design**: CustomTkinter for native look and feel
- **📊 Tabbed Interface**: Organized workflow with clear navigation

### **Integrated Workflow**
1. **📋 Job Management**: Load and validate Excel job files
2. **⚙️ Processing**: Run optimization with real-time status updates
3. **📊 Results**: View detailed schedule results and job summaries
4. **🌐 Visualization**: Launch interactive web dashboard

### **Key Benefits**
- ✅ **User-Friendly**: No command-line knowledge required
- ✅ **Real-Time Feedback**: Status updates and progress indicators
- ✅ **Error Handling**: Clear error messages and validation
- ✅ **Integrated Experience**: All features in one application
- ✅ **Professional Output**: Suitable for client presentations

## Requirements

### **Core Dependencies**
- Python 3.7+
- OR-Tools (`pip install ortools`)
- pandas (`pip install pandas`)
- openpyxl (`pip install openpyxl`)

### **GUI Dependencies**
- CustomTkinter (`pip install customtkinter`)
- tkinterweb (`pip install tkinterweb`)

### **Visualization Dependencies**
- matplotlib (`pip install matplotlib`)
- plotly (`pip install plotly`)

## Installation

### **Complete Installation**
```bash
pip install ortools pandas openpyxl customtkinter tkinterweb matplotlib plotly
```

### **Or use requirements file**
```bash
pip install -r requirements.txt
```

## Troubleshooting

### Common Issues

1. **"No solution found"**
   - Check if total work hours exceed team capacity
   - Verify due dates are reasonable (not too tight)
   - Ensure all required fields are filled

2. **Excel loading errors**
   - Verify file exists and is not open in Excel
   - Check date formats (must be YYYY-MM-DD)
   - Ensure hours are integers between 1-8

3. **Validation failures**
   - Check for duplicate Job_IDs
   - Verify all required columns are present
   - Ensure due dates are after received dates

### Capacity Planning

**Daily Capacity:**
- Red Team: 16 hours (John + Adam)
- Blue Team: 24 hours (Bob + Fred + Ted)
- Green Team: 16 hours (Jane + Bea)
- **Total: 56 hours per day**

Plan your jobs accordingly to ensure feasible schedules.

## Visualization Dashboard

The system automatically generates interactive visualizations:

### 📊 **Interactive Dashboard**
- **Unified Interface**: `schedule_dashboard.html` - Single page with all views
- **Responsive Design**: Professional interface with tabbed navigation
- **Real-time Data**: Generated from actual scheduling results

### 📈 **Visualization Types**

#### 1. **Employee Schedule View** (`gantt_by_employee.html`)
- Daily schedules for each employee
- Color-coded by team (Red/Blue/Green)
- Shows job details, duration, and timing
- Identifies workload distribution

#### 2. **Job Progress View** (`gantt_by_job.html`)
- Tracks each job through complete workflow
- Shows sequential Red → Blue → Green process
- Color-coded by job type (Standard/Custom/Abutment)
- Patient ID and job name tracking

#### 3. **Team Workload Analysis** (`team_workload.html`)
- Daily capacity utilization by team
- Identifies bottlenecks and peak periods
- Compares actual vs. available capacity
- Resource optimization insights

#### 4. **Executive Dashboard** (`job_priority_dashboard.html`)
- Job priority distribution (High/Medium/Low)
- Job type breakdown with statistics
- Completion timeline overview
- Team utilization metrics

### 🎯 **Dashboard Features**
- **Interactive Charts**: Hover for details, zoom, pan
- **Professional Styling**: Clean, modern interface
- **Keyboard Navigation**: Use keys 1-4 for quick tab switching
- **Mobile Responsive**: Works on tablets and phones
- **Export Ready**: Print or save charts as images

### 🚀 **Quick Start Visualization**
```bash
python Main.py
# Automatically generates:
# - schedule_dashboard.html (main dashboard)
# - gantt_by_employee.html
# - gantt_by_job.html
# - team_workload.html
# - job_priority_dashboard.html
```

### 💡 **Visualization Benefits**
- **Visual Planning**: See entire schedule at a glance
- **Resource Management**: Identify over/under-utilized employees
- **Progress Tracking**: Monitor job completion status
- **Bottleneck Analysis**: Spot workflow constraints
- **Client Communication**: Professional charts for stakeholders
- **Capacity Planning**: Optimize team assignments

## Python vs. External Services

**Why Python Visualizations?**
- ✅ **Full Control**: Complete customization
- ✅ **No Subscription Fees**: Unlike Monday.com, Asana, etc.
- ✅ **Data Privacy**: All data stays in your system
- ✅ **Integration**: Seamless with scheduling system
- ✅ **Offline Access**: No internet dependency
- ✅ **Extensible**: Easy to add new chart types

**Comparison with Monday.com:**
| Feature | Python Solution | Monday.com |
|---------|----------------|------------|
| Cost | Free | $8-16/user/month |
| Customization | Unlimited | Template-based |
| Data Control | Full | Cloud-hosted |
| Integration | Native | API required |
| Offline Use | Yes | No |

## Support

For issues or questions, check the Instructions sheet in the Excel template or review the validation messages when loading jobs.
