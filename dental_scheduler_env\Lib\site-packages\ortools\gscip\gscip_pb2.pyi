"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class GScipParameters(google.protobuf.message.Message):
    """Contains both the "SCIP parameters" and gSCIP only configuration. For the
    SCIP parameters, the order of application is:
      1. Emphasis
      2. Meta parameters (heuristics, presolve, separating)
      3. Individual SCIP parameters (e.g. an entry in bool_params)
    Note that 1. and 2. both apply a combination of parameters from 3.

    For parameters that are marked as optional, the underlying solver default
    is used if they are not specified.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Emphasis:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _EmphasisEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[GScipParameters._Emphasis.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        DEFAULT_EMPHASIS: GScipParameters._Emphasis.ValueType  # 0
        COUNTER: GScipParameters._Emphasis.ValueType  # 1
        CP_SOLVER: GScipParameters._Emphasis.ValueType  # 2
        EASY_CIP: GScipParameters._Emphasis.ValueType  # 3
        FEASIBILITY: GScipParameters._Emphasis.ValueType  # 4
        HARD_LP: GScipParameters._Emphasis.ValueType  # 5
        OPTIMALITY: GScipParameters._Emphasis.ValueType  # 6
        PHASE_FEAS: GScipParameters._Emphasis.ValueType  # 7
        PHASE_IMPROVE: GScipParameters._Emphasis.ValueType  # 8
        PHASE_PROOF: GScipParameters._Emphasis.ValueType  # 9

    class Emphasis(_Emphasis, metaclass=_EmphasisEnumTypeWrapper):
        """See SCIP documentation for details:
        https://scip.zib.de/doc/html/type__paramset_8h.php#a2e51a867a8ea3ea16f15e7cc935c3f32
        """

    DEFAULT_EMPHASIS: GScipParameters.Emphasis.ValueType  # 0
    COUNTER: GScipParameters.Emphasis.ValueType  # 1
    CP_SOLVER: GScipParameters.Emphasis.ValueType  # 2
    EASY_CIP: GScipParameters.Emphasis.ValueType  # 3
    FEASIBILITY: GScipParameters.Emphasis.ValueType  # 4
    HARD_LP: GScipParameters.Emphasis.ValueType  # 5
    OPTIMALITY: GScipParameters.Emphasis.ValueType  # 6
    PHASE_FEAS: GScipParameters.Emphasis.ValueType  # 7
    PHASE_IMPROVE: GScipParameters.Emphasis.ValueType  # 8
    PHASE_PROOF: GScipParameters.Emphasis.ValueType  # 9

    class _MetaParamValue:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _MetaParamValueEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[GScipParameters._MetaParamValue.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        DEFAULT_META_PARAM_VALUE: GScipParameters._MetaParamValue.ValueType  # 0
        AGGRESSIVE: GScipParameters._MetaParamValue.ValueType  # 1
        FAST: GScipParameters._MetaParamValue.ValueType  # 2
        OFF: GScipParameters._MetaParamValue.ValueType  # 3

    class MetaParamValue(_MetaParamValue, metaclass=_MetaParamValueEnumTypeWrapper):
        """See SCIP documentation for details:
        https://scip.zib.de/doc/html/type__paramset_8h.php#a083067d8e425d0d44e834095e82902ed
        """

    DEFAULT_META_PARAM_VALUE: GScipParameters.MetaParamValue.ValueType  # 0
    AGGRESSIVE: GScipParameters.MetaParamValue.ValueType  # 1
    FAST: GScipParameters.MetaParamValue.ValueType  # 2
    OFF: GScipParameters.MetaParamValue.ValueType  # 3

    @typing.final
    class BoolParamsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.bool
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class IntParamsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.int
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.int = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class LongParamsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.int
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.int = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class RealParamsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.float
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.float = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class CharParamsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class StringParamsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    EMPHASIS_FIELD_NUMBER: builtins.int
    HEURISTICS_FIELD_NUMBER: builtins.int
    PRESOLVE_FIELD_NUMBER: builtins.int
    SEPARATING_FIELD_NUMBER: builtins.int
    BOOL_PARAMS_FIELD_NUMBER: builtins.int
    INT_PARAMS_FIELD_NUMBER: builtins.int
    LONG_PARAMS_FIELD_NUMBER: builtins.int
    REAL_PARAMS_FIELD_NUMBER: builtins.int
    CHAR_PARAMS_FIELD_NUMBER: builtins.int
    STRING_PARAMS_FIELD_NUMBER: builtins.int
    SILENCE_OUTPUT_FIELD_NUMBER: builtins.int
    PRINT_DETAILED_SOLVING_STATS_FIELD_NUMBER: builtins.int
    PRINT_SCIP_MODEL_FIELD_NUMBER: builtins.int
    SEARCH_LOGS_FILENAME_FIELD_NUMBER: builtins.int
    DETAILED_SOLVING_STATS_FILENAME_FIELD_NUMBER: builtins.int
    SCIP_MODEL_FILENAME_FIELD_NUMBER: builtins.int
    NUM_SOLUTIONS_FIELD_NUMBER: builtins.int
    OBJECTIVE_LIMIT_FIELD_NUMBER: builtins.int
    emphasis: global___GScipParameters.Emphasis.ValueType
    heuristics: global___GScipParameters.MetaParamValue.ValueType
    """See SCIPsetHeuristics() for details:
    https://scip.zib.de/doc/html/group__ParameterMethods.php#gaeccb7859066cadd01d0df7aca98e2c7d
    """
    presolve: global___GScipParameters.MetaParamValue.ValueType
    """See SCIPsetPresolving() for details:
    https://scip.zib.de/doc/html/group__ParameterMethods.php#ga8365de8ab5ec5961c005e2d77965b182
    """
    separating: global___GScipParameters.MetaParamValue.ValueType
    """See SCIPsetSeparating() for details:
    https://scip.zib.de/doc/html/group__ParameterMethods.php#gad0c64e3e9b8def72fd8a7d3d9dce7729
    """
    silence_output: builtins.bool
    """///////////////////////////////////////////////////////////////////////////
    gSCIP only parameters
    ///////////////////////////////////////////////////////////////////////////

    Disable all terminal output (override all logging parameters). To control
    only the search logs, see also the SCIP parameter display/verblevel and
    from gscip_parameters.h, SetLogLevel() and SetOutputEnabled().
    """
    print_detailed_solving_stats: builtins.bool
    """Log solver metrics to terminal when finished solving (unless silenced)."""
    print_scip_model: builtins.bool
    """Write out the model in SCIP's text format before solving to the terminal
    (unless silenced).
    """
    search_logs_filename: builtins.str
    """If nonempty, search logs are written here INSTEAD OF out to terminal. See
    also the SCIP parameter display/verblevel and from gscip_parameters.h, the
    functions SetLogLevel() and SetOutputEnabled() for configuring the search
    logs.

    Does not use gfile, can only write to local disk.
    """
    detailed_solving_stats_filename: builtins.str
    """If non-empty, write detailed_solving_stats to a file. Can be set
    independently from print_detailed_solving_stats.

    Does not use gfile, can only write to local disk.
    """
    scip_model_filename: builtins.str
    """If nonempty, out the model in SCIP's text format to a file before solving.
    Can be set independently of print_scip_model.

    Does not use gfile, can only write to local disk.
    """
    num_solutions: builtins.int
    """How many solutions to retrieve from the solution pool (if this many exist).
    At least one solution will always be returned, even if num_solutions < 1.
    """
    objective_limit: builtins.float
    """If set, ignore all solutions worse than objective_limit, for details see
    SCIPsetObjlimit(). Note that the solution pool will still contain solutions
    worse than the limit as SCIP uses these to run improvement heuristics, and
    if you query all solutions at the end of the solve they will be present,
    even if you found no solution that met the limit and returned infeasible.
    """
    @property
    def bool_params(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.bool]:
        """See https://scip.zib.de/doc/html/PARAMETERS.php for a list of all SCIP
        parameters.
        """

    @property
    def int_params(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.int]: ...
    @property
    def long_params(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.int]: ...
    @property
    def real_params(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.float]: ...
    @property
    def char_params(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    @property
    def string_params(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    def __init__(
        self,
        *,
        emphasis: global___GScipParameters.Emphasis.ValueType = ...,
        heuristics: global___GScipParameters.MetaParamValue.ValueType | None = ...,
        presolve: global___GScipParameters.MetaParamValue.ValueType | None = ...,
        separating: global___GScipParameters.MetaParamValue.ValueType | None = ...,
        bool_params: collections.abc.Mapping[builtins.str, builtins.bool] | None = ...,
        int_params: collections.abc.Mapping[builtins.str, builtins.int] | None = ...,
        long_params: collections.abc.Mapping[builtins.str, builtins.int] | None = ...,
        real_params: collections.abc.Mapping[builtins.str, builtins.float] | None = ...,
        char_params: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        string_params: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
        silence_output: builtins.bool | None = ...,
        print_detailed_solving_stats: builtins.bool = ...,
        print_scip_model: builtins.bool = ...,
        search_logs_filename: builtins.str = ...,
        detailed_solving_stats_filename: builtins.str = ...,
        scip_model_filename: builtins.str = ...,
        num_solutions: builtins.int | None = ...,
        objective_limit: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_heuristics", b"_heuristics", "_num_solutions", b"_num_solutions", "_objective_limit", b"_objective_limit", "_presolve", b"_presolve", "_separating", b"_separating", "_silence_output", b"_silence_output", "heuristics", b"heuristics", "num_solutions", b"num_solutions", "objective_limit", b"objective_limit", "presolve", b"presolve", "separating", b"separating", "silence_output", b"silence_output"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_heuristics", b"_heuristics", "_num_solutions", b"_num_solutions", "_objective_limit", b"_objective_limit", "_presolve", b"_presolve", "_separating", b"_separating", "_silence_output", b"_silence_output", "bool_params", b"bool_params", "char_params", b"char_params", "detailed_solving_stats_filename", b"detailed_solving_stats_filename", "emphasis", b"emphasis", "heuristics", b"heuristics", "int_params", b"int_params", "long_params", b"long_params", "num_solutions", b"num_solutions", "objective_limit", b"objective_limit", "presolve", b"presolve", "print_detailed_solving_stats", b"print_detailed_solving_stats", "print_scip_model", b"print_scip_model", "real_params", b"real_params", "scip_model_filename", b"scip_model_filename", "search_logs_filename", b"search_logs_filename", "separating", b"separating", "silence_output", b"silence_output", "string_params", b"string_params"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_heuristics", b"_heuristics"]) -> typing.Literal["heuristics"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_num_solutions", b"_num_solutions"]) -> typing.Literal["num_solutions"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_objective_limit", b"_objective_limit"]) -> typing.Literal["objective_limit"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_presolve", b"_presolve"]) -> typing.Literal["presolve"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_separating", b"_separating"]) -> typing.Literal["separating"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_silence_output", b"_silence_output"]) -> typing.Literal["silence_output"] | None: ...

global___GScipParameters = GScipParameters

@typing.final
class GScipSolvingStats(google.protobuf.message.Message):
    """TODO(user): this should be machine generated by script and contain all of
    https://scip.zib.de/doc/html/group__PublicSolvingStatsMethods.php
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    BEST_OBJECTIVE_FIELD_NUMBER: builtins.int
    BEST_BOUND_FIELD_NUMBER: builtins.int
    PRIMAL_SIMPLEX_ITERATIONS_FIELD_NUMBER: builtins.int
    DUAL_SIMPLEX_ITERATIONS_FIELD_NUMBER: builtins.int
    BARRIER_ITERATIONS_FIELD_NUMBER: builtins.int
    TOTAL_LP_ITERATIONS_FIELD_NUMBER: builtins.int
    NODE_COUNT_FIELD_NUMBER: builtins.int
    FIRST_LP_RELAXATION_BOUND_FIELD_NUMBER: builtins.int
    ROOT_NODE_BOUND_FIELD_NUMBER: builtins.int
    DETERMINISTIC_TIME_FIELD_NUMBER: builtins.int
    best_objective: builtins.float
    """The objective value of the best solution (or the cutoff). If no solution is
    found, returns +inf for minimization and -inf for maximization. Equivalent
    to SCIPgetPrimalbound().
    """
    best_bound: builtins.float
    """The best proven bound on the object (e.g. through the LP relaxation).
    Returns +inf for maximization and -inf for minimization if no bound was
    found. Equivalent to SCIPgetDualBound().
    """
    primal_simplex_iterations: builtins.int
    """nprimallpiterations in SCIP. The number of primal simplex LP iterations."""
    dual_simplex_iterations: builtins.int
    """nduallpiterations in SCIP. The number of dual simplex LP iterations."""
    barrier_iterations: builtins.int
    """nbarrierlpiterations. The number of barrier LP iterations."""
    total_lp_iterations: builtins.int
    """nlp_iterations in SCIP. The total number of LP steps taken. Note that this
    number be at least (and often strictly greater than)
    primal_simplex_iterations + dual simplex iterations + barrier iterations,
    as it includes other types of iterations (e.g. see ndivinglpiterations).
    """
    node_count: builtins.int
    """NTotalNodes in SCIP.
    This is the total number of nodes used in the solve, potentially across
    multiple branch-and-bound trees. Use limits/totalnodes (rather than
    limits/nodes) to control this value.
    """
    first_lp_relaxation_bound: builtins.float
    """FirstLPDualboundRoot in SCIP. The bound obtained from the first LP solve
    at the root node.
    """
    root_node_bound: builtins.float
    """DualboundRoot in SCIP. The bound obtained at the root node, possibly after
    multiple rounds of cuts.
    """
    deterministic_time: builtins.float
    """A deterministic measure of work done during the solve. The units of this
    field are specific to SCIP.
    """
    def __init__(
        self,
        *,
        best_objective: builtins.float = ...,
        best_bound: builtins.float = ...,
        primal_simplex_iterations: builtins.int = ...,
        dual_simplex_iterations: builtins.int = ...,
        barrier_iterations: builtins.int = ...,
        total_lp_iterations: builtins.int = ...,
        node_count: builtins.int = ...,
        first_lp_relaxation_bound: builtins.float = ...,
        root_node_bound: builtins.float = ...,
        deterministic_time: builtins.float = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["barrier_iterations", b"barrier_iterations", "best_bound", b"best_bound", "best_objective", b"best_objective", "deterministic_time", b"deterministic_time", "dual_simplex_iterations", b"dual_simplex_iterations", "first_lp_relaxation_bound", b"first_lp_relaxation_bound", "node_count", b"node_count", "primal_simplex_iterations", b"primal_simplex_iterations", "root_node_bound", b"root_node_bound", "total_lp_iterations", b"total_lp_iterations"]) -> None: ...

global___GScipSolvingStats = GScipSolvingStats

@typing.final
class GScipOutput(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Status:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _StatusEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[GScipOutput._Status.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNKNOWN: GScipOutput._Status.ValueType  # 0
        USER_INTERRUPT: GScipOutput._Status.ValueType  # 1
        NODE_LIMIT: GScipOutput._Status.ValueType  # 2
        TOTAL_NODE_LIMIT: GScipOutput._Status.ValueType  # 3
        STALL_NODE_LIMIT: GScipOutput._Status.ValueType  # 4
        TIME_LIMIT: GScipOutput._Status.ValueType  # 5
        MEM_LIMIT: GScipOutput._Status.ValueType  # 6
        GAP_LIMIT: GScipOutput._Status.ValueType  # 7
        SOL_LIMIT: GScipOutput._Status.ValueType  # 8
        BEST_SOL_LIMIT: GScipOutput._Status.ValueType  # 9
        RESTART_LIMIT: GScipOutput._Status.ValueType  # 10
        OPTIMAL: GScipOutput._Status.ValueType  # 11
        INFEASIBLE: GScipOutput._Status.ValueType  # 12
        UNBOUNDED: GScipOutput._Status.ValueType  # 13
        INF_OR_UNBD: GScipOutput._Status.ValueType  # 14
        TERMINATE: GScipOutput._Status.ValueType  # 15
        INVALID_SOLVER_PARAMETERS: GScipOutput._Status.ValueType  # 16
        """WARNING(rander): we add some extra status values beyond SCIP here"""

    class Status(_Status, metaclass=_StatusEnumTypeWrapper):
        """See https://scip.zib.de/doc/html/type__stat_8h.php"""

    UNKNOWN: GScipOutput.Status.ValueType  # 0
    USER_INTERRUPT: GScipOutput.Status.ValueType  # 1
    NODE_LIMIT: GScipOutput.Status.ValueType  # 2
    TOTAL_NODE_LIMIT: GScipOutput.Status.ValueType  # 3
    STALL_NODE_LIMIT: GScipOutput.Status.ValueType  # 4
    TIME_LIMIT: GScipOutput.Status.ValueType  # 5
    MEM_LIMIT: GScipOutput.Status.ValueType  # 6
    GAP_LIMIT: GScipOutput.Status.ValueType  # 7
    SOL_LIMIT: GScipOutput.Status.ValueType  # 8
    BEST_SOL_LIMIT: GScipOutput.Status.ValueType  # 9
    RESTART_LIMIT: GScipOutput.Status.ValueType  # 10
    OPTIMAL: GScipOutput.Status.ValueType  # 11
    INFEASIBLE: GScipOutput.Status.ValueType  # 12
    UNBOUNDED: GScipOutput.Status.ValueType  # 13
    INF_OR_UNBD: GScipOutput.Status.ValueType  # 14
    TERMINATE: GScipOutput.Status.ValueType  # 15
    INVALID_SOLVER_PARAMETERS: GScipOutput.Status.ValueType  # 16
    """WARNING(rander): we add some extra status values beyond SCIP here"""

    STATUS_FIELD_NUMBER: builtins.int
    STATUS_DETAIL_FIELD_NUMBER: builtins.int
    STATS_FIELD_NUMBER: builtins.int
    status: global___GScipOutput.Status.ValueType
    status_detail: builtins.str
    @property
    def stats(self) -> global___GScipSolvingStats: ...
    def __init__(
        self,
        *,
        status: global___GScipOutput.Status.ValueType = ...,
        status_detail: builtins.str = ...,
        stats: global___GScipSolvingStats | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["stats", b"stats"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["stats", b"stats", "status", b"status", "status_detail", b"status_detail"]) -> None: ...

global___GScipOutput = GScipOutput
