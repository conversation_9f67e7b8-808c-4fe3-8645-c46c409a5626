"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Messages for representing subsets of a models constraints, and for computing
infeasible subsystems of a model. Cf. "Irreducible Inconsistent subsystems"
(IIS), which are useful for debugging/diagnosing model infeasibility.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import ortools.math_opt.result_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class ModelSubsetProto(google.protobuf.message.Message):
    """Represents a subset of the constraints (including variable bounds and
    integrality) of a `ModelProto`.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class Bounds(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        LOWER_FIELD_NUMBER: builtins.int
        UPPER_FIELD_NUMBER: builtins.int
        lower: builtins.bool
        upper: builtins.bool
        def __init__(
            self,
            *,
            lower: builtins.bool = ...,
            upper: builtins.bool = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["lower", b"lower", "upper", b"upper"]) -> None: ...

    @typing.final
    class VariableBoundsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> global___ModelSubsetProto.Bounds: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: global___ModelSubsetProto.Bounds | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class LinearConstraintsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> global___ModelSubsetProto.Bounds: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: global___ModelSubsetProto.Bounds | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class QuadraticConstraintsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> global___ModelSubsetProto.Bounds: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: global___ModelSubsetProto.Bounds | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    VARIABLE_BOUNDS_FIELD_NUMBER: builtins.int
    VARIABLE_INTEGRALITY_FIELD_NUMBER: builtins.int
    LINEAR_CONSTRAINTS_FIELD_NUMBER: builtins.int
    QUADRATIC_CONSTRAINTS_FIELD_NUMBER: builtins.int
    SECOND_ORDER_CONE_CONSTRAINTS_FIELD_NUMBER: builtins.int
    SOS1_CONSTRAINTS_FIELD_NUMBER: builtins.int
    SOS2_CONSTRAINTS_FIELD_NUMBER: builtins.int
    INDICATOR_CONSTRAINTS_FIELD_NUMBER: builtins.int
    @property
    def variable_bounds(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, global___ModelSubsetProto.Bounds]:
        """Keys are variable IDs, and must be in [0, max(int64)). Values indicate
        which of the lower and upper variable bounds are included in the subsystem.
        """

    @property
    def variable_integrality(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Variable IDs. Values must be in [0, max(int64)) and strictly increasing."""

    @property
    def linear_constraints(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, global___ModelSubsetProto.Bounds]:
        """Keys are linear constraint IDs, and must be in [0, max(int64)). Values
        indicate which of the lower and upper bounds on the linear constraint are
        included in the subsystem.
        """

    @property
    def quadratic_constraints(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, global___ModelSubsetProto.Bounds]:
        """Keys are quadratic constraint IDs, and must be in [0, max(int64)). Values
        indicate which of the lower and upper bounds on the quadratic constraint
        are included in the subsystem.
        """

    @property
    def second_order_cone_constraints(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Second-order cone constraint IDs. Values must be in [0, max(int64)) and
        strictly increasing.
        """

    @property
    def sos1_constraints(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """SOS1 constraint IDs. Values must be in [0, max(int64)) and strictly
        increasing.
        """

    @property
    def sos2_constraints(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """SOS2 constraint IDs. Values must be in [0, max(int64)) and strictly
        increasing.
        """

    @property
    def indicator_constraints(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Indicator constraint IDs. Values must be in [0, max(int64)) and strictly
        increasing.
        """

    def __init__(
        self,
        *,
        variable_bounds: collections.abc.Mapping[builtins.int, global___ModelSubsetProto.Bounds] | None = ...,
        variable_integrality: collections.abc.Iterable[builtins.int] | None = ...,
        linear_constraints: collections.abc.Mapping[builtins.int, global___ModelSubsetProto.Bounds] | None = ...,
        quadratic_constraints: collections.abc.Mapping[builtins.int, global___ModelSubsetProto.Bounds] | None = ...,
        second_order_cone_constraints: collections.abc.Iterable[builtins.int] | None = ...,
        sos1_constraints: collections.abc.Iterable[builtins.int] | None = ...,
        sos2_constraints: collections.abc.Iterable[builtins.int] | None = ...,
        indicator_constraints: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["indicator_constraints", b"indicator_constraints", "linear_constraints", b"linear_constraints", "quadratic_constraints", b"quadratic_constraints", "second_order_cone_constraints", b"second_order_cone_constraints", "sos1_constraints", b"sos1_constraints", "sos2_constraints", b"sos2_constraints", "variable_bounds", b"variable_bounds", "variable_integrality", b"variable_integrality"]) -> None: ...

global___ModelSubsetProto = ModelSubsetProto

@typing.final
class ComputeInfeasibleSubsystemResultProto(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    FEASIBILITY_FIELD_NUMBER: builtins.int
    INFEASIBLE_SUBSYSTEM_FIELD_NUMBER: builtins.int
    IS_MINIMAL_FIELD_NUMBER: builtins.int
    feasibility: ortools.math_opt.result_pb2.FeasibilityStatusProto.ValueType
    """The primal feasibility status of the model, as determined by the solver."""
    is_minimal: builtins.bool
    """True if the solver has certified that the returned subsystem is minimal
    (the instance is feasible if any additional constraint is removed). Note
    that, due to problem transformations MathOpt applies or idiosyncrasies of
    the solvers contract, the returned infeasible subsystem may not actually be
    minimal.
    """
    @property
    def infeasible_subsystem(self) -> global___ModelSubsetProto:
        """An infeasible subsystem of the input model. Set if `feasibility` is
        INFEASIBLE and empty otherwise. The IDs correspond to those constraints
        included in the infeasible subsystem. Submessages with `Bounds` values
        indicate which side of a potentially ranged constraint are included in the
        subsystem: lower bound, upper bound, or both.
        """

    def __init__(
        self,
        *,
        feasibility: ortools.math_opt.result_pb2.FeasibilityStatusProto.ValueType = ...,
        infeasible_subsystem: global___ModelSubsetProto | None = ...,
        is_minimal: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["infeasible_subsystem", b"infeasible_subsystem"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["feasibility", b"feasibility", "infeasible_subsystem", b"infeasible_subsystem", "is_minimal", b"is_minimal"]) -> None: ...

global___ComputeInfeasibleSubsystemResultProto = ComputeInfeasibleSubsystemResultProto
