@echo off
title Dental Scheduler - Build Windows Installer

echo.
echo ========================================
echo  🦷 Dental Scheduler
echo     Windows Installer Builder
echo ========================================
echo.
echo This will create a professional Windows installer
echo that includes everything needed to run the application.
echo.
echo Requirements:
echo  - Python 3.7+ (will check automatically)
echo  - Internet connection (for downloading packages)
echo  - Inno Setup (will provide download link if needed)
echo.
echo The build process will:
echo  1. Install PyInstaller and dependencies
echo  2. Create standalone .exe files
echo  3. Build professional Windows installer
echo  4. Test the build
echo.
echo Estimated time: 10-30 minutes
echo Output size: ~250MB installer file
echo.
pause

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo ✓ Python found
python --version

:: Run the Python build script
echo.
echo Starting build process...
python build_installer.py

if %errorlevel% neq 0 (
    echo.
    echo Build failed. Please check the error messages above.
    echo.
    echo Common solutions:
    echo  1. Make sure Python 3.7+ is installed
    echo  2. Check your internet connection
    echo  3. Install Inno Setup if prompted
    echo  4. Run as administrator if needed
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo  Build completed successfully! 🎉
echo ========================================
echo.
echo The installer has been created in the installer_output folder.
echo You can now distribute this .exe file to users.
echo.
echo Users simply need to:
echo  1. Download the installer
echo  2. Double-click to run
echo  3. Follow the installation wizard
echo  4. Start using the application
echo.
pause
