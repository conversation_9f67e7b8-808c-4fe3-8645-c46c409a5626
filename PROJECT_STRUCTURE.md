# Project Structure

## 🦷 Dental Implant Manufacturing Scheduler

### 📁 Core Files

#### 🖥️ **User Interfaces**
- `dental_scheduler_gui.py` - Modern GUI application (main interface)
- `run_gui.py` - Simple GUI launcher
- `Main.py` - Command-line interface

#### 📊 **Core Engine**
- `job_loader.py` - Excel job file processing and validation
- `schedule_visualizer.py` - Interactive chart generation (Plotly)
- `create_dashboard.py` - Unified web dashboard creation

#### 📋 **Templates & Setup**
- `create_job_template.py` - Excel template generator
- `Job_Upload_Template.xlsx` - Sample job template with 20 dental jobs

#### 📚 **Documentation**
- `README.md` - Complete user guide and documentation
- `requirements.txt` - Python dependencies
- `PROJECT_STRUCTURE.md` - This file

#### ⚙️ **Configuration**
- `.gitignore` - Git ignore rules for generated files

### 🚀 **Quick Start**

#### GUI Application (Recommended)
```bash
python dental_scheduler_gui.py
```

#### Command Line
```bash
python Main.py
```

#### Create New Template
```bash
python create_job_template.py
```

### 📊 **Generated Output Files**
These files are created when running the scheduler:

- `gantt_by_employee.html` - Employee schedule Gantt chart
- `gantt_by_job.html` - Job progress Gantt chart  
- `team_workload.html` - Team workload analysis
- `job_priority_dashboard.html` - Executive dashboard
- `schedule_dashboard.html` - Unified web dashboard
- `jobs_summary.txt` - Text summary of jobs

### 🔧 **Dependencies**

#### Core
- `ortools` - Constraint programming solver
- `pandas` - Data processing
- `openpyxl` - Excel file handling

#### Visualization  
- `plotly` - Interactive charts
- `matplotlib` - Chart generation

#### GUI
- `customtkinter` - Modern GUI framework
- `tkinterweb` - Web browser integration

### 🏗️ **Architecture**

```
User Input (Excel) → Job Loader → Scheduler → Visualizer → Dashboard
                                      ↓
                                 GUI Interface
```

1. **Job Loader**: Reads and validates Excel job files
2. **Scheduler**: Optimizes job assignments using OR-Tools
3. **Visualizer**: Creates interactive charts with Plotly
4. **Dashboard**: Combines all visualizations into web interface
5. **GUI**: Provides user-friendly interface for entire workflow

### 📝 **File Dependencies**

- `Main.py` depends on: `job_loader.py`, `schedule_visualizer.py`, `create_dashboard.py`
- `dental_scheduler_gui.py` depends on: `job_loader.py`, `schedule_visualizer.py`, `create_dashboard.py`
- `schedule_visualizer.py` depends on: `plotly`, `pandas`
- `job_loader.py` depends on: `pandas`, `openpyxl`
- `create_job_template.py` depends on: `pandas`, `openpyxl`

### 🧹 **Maintenance**

#### Clean Generated Files
Generated HTML and text files are automatically ignored by Git.
To clean manually:
```bash
rm *.html jobs_summary.txt
```

#### Update Dependencies
```bash
pip install -r requirements.txt --upgrade
```
