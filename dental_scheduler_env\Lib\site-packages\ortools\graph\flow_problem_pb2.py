# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/graph/flow_problem.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/graph/flow_problem.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n ortools/graph/flow_problem.proto\x12\x13operations_research\"U\n\x0c\x46lowArcProto\x12\x0c\n\x04tail\x18\x01 \x01(\x03\x12\x0c\n\x04head\x18\x02 \x01(\x03\x12\x13\n\x08\x63\x61pacity\x18\x03 \x01(\x03:\x01\x31\x12\x14\n\tunit_cost\x18\x04 \x01(\x03:\x01\x30\".\n\rFlowNodeProto\x12\n\n\x02id\x18\x01 \x01(\x03\x12\x11\n\x06supply\x18\x02 \x01(\x03:\x01\x30\"\x86\x02\n\x0e\x46lowModelProto\x12\x31\n\x05nodes\x18\x01 \x03(\x0b\x32\".operations_research.FlowNodeProto\x12/\n\x04\x61rcs\x18\x02 \x03(\x0b\x32!.operations_research.FlowArcProto\x12\x45\n\x0cproblem_type\x18\x03 \x01(\x0e\x32/.operations_research.FlowModelProto.ProblemType\"I\n\x0bProblemType\x12\x19\n\x15LINEAR_SUM_ASSIGNMENT\x10\x00\x12\x0c\n\x08MAX_FLOW\x10\x01\x12\x11\n\rMIN_COST_FLOW\x10\x02\x42\x33\n\x18\x63om.google.ortools.graphP\x01\xaa\x02\x14Google.OrTools.Graph')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.graph.flow_problem_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\030com.google.ortools.graphP\001\252\002\024Google.OrTools.Graph'
  _globals['_FLOWARCPROTO']._serialized_start=57
  _globals['_FLOWARCPROTO']._serialized_end=142
  _globals['_FLOWNODEPROTO']._serialized_start=144
  _globals['_FLOWNODEPROTO']._serialized_end=190
  _globals['_FLOWMODELPROTO']._serialized_start=193
  _globals['_FLOWMODELPROTO']._serialized_end=455
  _globals['_FLOWMODELPROTO_PROBLEMTYPE']._serialized_start=382
  _globals['_FLOWMODELPROTO_PROBLEMTYPE']._serialized_end=455
# @@protoc_insertion_point(module_scope)
