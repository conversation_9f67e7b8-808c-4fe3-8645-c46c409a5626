"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Solve parameters that are specific to the model."""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.duration_pb2
import google.protobuf.internal.containers
import google.protobuf.message
import ortools.math_opt.solution_pb2
import ortools.math_opt.sparse_containers_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class SolutionHintProto(google.protobuf.message.Message):
    """A suggested starting solution for the solver.

    MIP solvers generally only want primal information (`variable_values`), while
    LP solvers want both primal and dual information (`dual_values`).

    Many MIP solvers can work with: (1) partial solutions that do not specify all
    variables or (2) infeasible solutions. In these cases, solvers typically
    solve a sub-MIP to complete/correct the hint.

    How the hint is used by the solver, if at all, is highly dependent on the
    solver, the problem type, and the algorithm used. The most reliable way to
    ensure your hint has an effect is to read the underlying solvers logs with
    and without the hint.

    Simplex-based LP solvers typically prefer an initial basis to a solution hint
    (they need to crossover to convert the hint to a basic feasible solution
    otherwise).

    TODO(b/183616124): Add hint-priorities to variable_values.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    VARIABLE_VALUES_FIELD_NUMBER: builtins.int
    DUAL_VALUES_FIELD_NUMBER: builtins.int
    @property
    def variable_values(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """A possibly partial assignment of values to the primal variables of the
        problem. The solver-independent requirements for this sub-message are:
         * variable_values.ids are elements of VariablesProto.ids.
         * variable_values.values must all be finite.
        """

    @property
    def dual_values(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """A (potentially partial) assignment of values to the linear constraints of
        the problem.

         Requirements:
          * dual_values.ids are elements of LinearConstraintsProto.ids.
          * dual_values.values must all be finite.
        """

    def __init__(
        self,
        *,
        variable_values: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        dual_values: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["dual_values", b"dual_values", "variable_values", b"variable_values"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["dual_values", b"dual_values", "variable_values", b"variable_values"]) -> None: ...

global___SolutionHintProto = SolutionHintProto

@typing.final
class ObjectiveParametersProto(google.protobuf.message.Message):
    """Parameters for an individual objective in a multi-objective model."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    OBJECTIVE_DEGRADATION_ABSOLUTE_TOLERANCE_FIELD_NUMBER: builtins.int
    OBJECTIVE_DEGRADATION_RELATIVE_TOLERANCE_FIELD_NUMBER: builtins.int
    TIME_LIMIT_FIELD_NUMBER: builtins.int
    objective_degradation_absolute_tolerance: builtins.float
    """Optional objective degradation absolute tolerance. For a hierarchical
    multi-objective solver, each objective fⁱ is processed in priority order:
    the solver determines the optimal objective value Γⁱ, if it exists, subject
    to all constraints in the model and the additional constraints that
    fᵏ(x) = Γᵏ (within tolerances) for each k < i. If set, a solution is
    considered to be "within tolerances" for this objective fᵏ if
    |fᵏ(x) - Γᵏ| ≤ `objective_degradation_absolute_tolerance`.

    See also `objective_degradation_relative_tolerance`; if both parameters are
    set for a given objective, the solver need only satisfy one to be
    considered "within tolerances".

     If set, must be nonnegative.
    """
    objective_degradation_relative_tolerance: builtins.float
    """Optional objective degradation relative tolerance. For a hierarchical
    multi-objective solver, each objective fⁱ is processed in priority order:
    the solver determines the optimal objective value Γⁱ, if it exists, subject
    to all constraints in the model and the additional constraints that
    fᵏ(x) = Γᵏ (within tolerances) for each k < i. If set, a solution is
    considered to be "within tolerances" for this objective fᵏ if
    |fᵏ(x) - Γᵏ| ≤ `objective_degradation_relative_tolerance` * |Γᵏ|.

    See also `objective_degradation_absolute_tolerance`; if both parameters are
    set for a given objective, the solver need only satisfy one to be
    considered "within tolerances".

     If set, must be nonnegative.
    """
    @property
    def time_limit(self) -> google.protobuf.duration_pb2.Duration:
        """Maximum time a solver should spend on optimizing this particular objective
        (or infinite if not set).

        Note that this does not supersede the global time limit in
        SolveParametersProto.time_limit; both will be enforced when set.

        This value is not a hard limit, solve time may slightly exceed this value.
        """

    def __init__(
        self,
        *,
        objective_degradation_absolute_tolerance: builtins.float | None = ...,
        objective_degradation_relative_tolerance: builtins.float | None = ...,
        time_limit: google.protobuf.duration_pb2.Duration | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_objective_degradation_absolute_tolerance", b"_objective_degradation_absolute_tolerance", "_objective_degradation_relative_tolerance", b"_objective_degradation_relative_tolerance", "objective_degradation_absolute_tolerance", b"objective_degradation_absolute_tolerance", "objective_degradation_relative_tolerance", b"objective_degradation_relative_tolerance", "time_limit", b"time_limit"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_objective_degradation_absolute_tolerance", b"_objective_degradation_absolute_tolerance", "_objective_degradation_relative_tolerance", b"_objective_degradation_relative_tolerance", "objective_degradation_absolute_tolerance", b"objective_degradation_absolute_tolerance", "objective_degradation_relative_tolerance", b"objective_degradation_relative_tolerance", "time_limit", b"time_limit"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_objective_degradation_absolute_tolerance", b"_objective_degradation_absolute_tolerance"]) -> typing.Literal["objective_degradation_absolute_tolerance"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_objective_degradation_relative_tolerance", b"_objective_degradation_relative_tolerance"]) -> typing.Literal["objective_degradation_relative_tolerance"] | None: ...

global___ObjectiveParametersProto = ObjectiveParametersProto

@typing.final
class ModelSolveParametersProto(google.protobuf.message.Message):
    """TODO(b/183628247): follow naming convention in fields below.
    Parameters to control a single solve that are specific to the input model
    (see SolveParametersProto for model independent parameters).
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class AuxiliaryObjectiveParametersEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> global___ObjectiveParametersProto: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: global___ObjectiveParametersProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    VARIABLE_VALUES_FILTER_FIELD_NUMBER: builtins.int
    DUAL_VALUES_FILTER_FIELD_NUMBER: builtins.int
    QUADRATIC_DUAL_VALUES_FILTER_FIELD_NUMBER: builtins.int
    REDUCED_COSTS_FILTER_FIELD_NUMBER: builtins.int
    INITIAL_BASIS_FIELD_NUMBER: builtins.int
    SOLUTION_HINTS_FIELD_NUMBER: builtins.int
    BRANCHING_PRIORITIES_FIELD_NUMBER: builtins.int
    PRIMARY_OBJECTIVE_PARAMETERS_FIELD_NUMBER: builtins.int
    AUXILIARY_OBJECTIVE_PARAMETERS_FIELD_NUMBER: builtins.int
    LAZY_LINEAR_CONSTRAINT_IDS_FIELD_NUMBER: builtins.int
    @property
    def variable_values_filter(self) -> ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto:
        """Filter that is applied to all returned sparse containers keyed by variables
        in PrimalSolutionProto and PrimalRayProto
        (PrimalSolutionProto.variable_values, PrimalRayProto.variable_values).

        Requirements:
         * filtered_ids are elements of VariablesProto.ids.
        """

    @property
    def dual_values_filter(self) -> ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto:
        """Filter that is applied to all returned sparse containers keyed by linear
        constraints in DualSolutionProto and DualRay
        (DualSolutionProto.dual_values, DualRay.dual_values).

        Requirements:
         * filtered_ids are elements of LinearConstraints.ids.
        """

    @property
    def quadratic_dual_values_filter(self) -> ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto:
        """Filter that is applied to all returned sparse containers keyed by quadratic
        constraints in DualSolutionProto and DualRay
        (DualSolutionProto.quadratic_dual_values, DualRay.quadratic_dual_values).

        Requirements:
         * filtered_ids are keys of ModelProto.quadratic_constraints.
        """

    @property
    def reduced_costs_filter(self) -> ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto:
        """Filter that is applied to all returned sparse containers keyed by variables
        in DualSolutionProto and DualRay (DualSolutionProto.reduced_costs,
        DualRay.reduced_costs).

        Requirements:
         * filtered_ids are elements of VariablesProto.ids.
        """

    @property
    def initial_basis(self) -> ortools.math_opt.solution_pb2.BasisProto:
        """Optional initial basis for warm starting simplex LP solvers. If set, it is
        expected to be valid according to `ValidateBasis` in
        `validators/solution_validator.h` for the current `ModelSummary`.
        """

    @property
    def solution_hints(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___SolutionHintProto]:
        """Optional solution hints. If the underlying solver only accepts a single
        hint, the first hint is used.
        """

    @property
    def branching_priorities(self) -> ortools.math_opt.sparse_containers_pb2.SparseInt32VectorProto:
        """Optional branching priorities. Variables with higher values will be
        branched on first. Variables for which priorities are not set get the
        solver's default priority (usually zero).

        Requirements:
         * branching_priorities.values must be finite.
         * branching_priorities.ids must be elements of VariablesProto.ids.
        """

    @property
    def primary_objective_parameters(self) -> global___ObjectiveParametersProto:
        """Optional parameters for the primary objective in a multi-objective model."""

    @property
    def auxiliary_objective_parameters(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, global___ObjectiveParametersProto]:
        """Optional parameters for the auxiliary objectives in a multi-objective
        model.

        Requirements:
         * Map keys must also be map keys of ModelProto.auxiliary_objectives.
        """

    @property
    def lazy_linear_constraint_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Optional lazy constraint annotations. Included linear constraints will be
        marked as "lazy" with supporting solvers, meaning that they will only be
        added to the working model as-needed as the solver runs.

        Note that this an algorithmic hint that does not affect the model's
        feasible region; solvers not supporting these annotations will simply
        ignore it.

        Requirements:
         * Each entry must be an element of VariablesProto.ids.
         * Entries must be in strictly increasing order (i.e., sorted, no repeats).
        """

    def __init__(
        self,
        *,
        variable_values_filter: ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto | None = ...,
        dual_values_filter: ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto | None = ...,
        quadratic_dual_values_filter: ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto | None = ...,
        reduced_costs_filter: ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto | None = ...,
        initial_basis: ortools.math_opt.solution_pb2.BasisProto | None = ...,
        solution_hints: collections.abc.Iterable[global___SolutionHintProto] | None = ...,
        branching_priorities: ortools.math_opt.sparse_containers_pb2.SparseInt32VectorProto | None = ...,
        primary_objective_parameters: global___ObjectiveParametersProto | None = ...,
        auxiliary_objective_parameters: collections.abc.Mapping[builtins.int, global___ObjectiveParametersProto] | None = ...,
        lazy_linear_constraint_ids: collections.abc.Iterable[builtins.int] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["branching_priorities", b"branching_priorities", "dual_values_filter", b"dual_values_filter", "initial_basis", b"initial_basis", "primary_objective_parameters", b"primary_objective_parameters", "quadratic_dual_values_filter", b"quadratic_dual_values_filter", "reduced_costs_filter", b"reduced_costs_filter", "variable_values_filter", b"variable_values_filter"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["auxiliary_objective_parameters", b"auxiliary_objective_parameters", "branching_priorities", b"branching_priorities", "dual_values_filter", b"dual_values_filter", "initial_basis", b"initial_basis", "lazy_linear_constraint_ids", b"lazy_linear_constraint_ids", "primary_objective_parameters", b"primary_objective_parameters", "quadratic_dual_values_filter", b"quadratic_dual_values_filter", "reduced_costs_filter", b"reduced_costs_filter", "solution_hints", b"solution_hints", "variable_values_filter", b"variable_values_filter"]) -> None: ...

global___ModelSolveParametersProto = ModelSolveParametersProto
