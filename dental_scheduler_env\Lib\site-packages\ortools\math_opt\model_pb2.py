# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/model.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/model.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.math_opt import sparse_containers_pb2 as ortools_dot_math__opt_dot_sparse__containers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cortools/math_opt/model.proto\x12\x1coperations_research.math_opt\x1a(ortools/math_opt/sparse_containers.proto\"j\n\x0eVariablesProto\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\x14\n\x0clower_bounds\x18\x02 \x03(\x01\x12\x14\n\x0cupper_bounds\x18\x03 \x03(\x01\x12\x10\n\x08integers\x18\x04 \x03(\x08\x12\r\n\x05names\x18\x05 \x03(\t\"\xfd\x01\n\x0eObjectiveProto\x12\x10\n\x08maximize\x18\x01 \x01(\x08\x12\x0e\n\x06offset\x18\x02 \x01(\x01\x12R\n\x13linear_coefficients\x18\x03 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12U\n\x16quadratic_coefficients\x18\x04 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleMatrixProto\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x10\n\x08priority\x18\x06 \x01(\x03\"`\n\x16LinearConstraintsProto\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\x14\n\x0clower_bounds\x18\x02 \x03(\x01\x12\x14\n\x0cupper_bounds\x18\x03 \x03(\x01\x12\r\n\x05names\x18\x04 \x03(\t\"\xef\x01\n\x18QuadraticConstraintProto\x12K\n\x0clinear_terms\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12N\n\x0fquadratic_terms\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleMatrixProto\x12\x13\n\x0blower_bound\x18\x03 \x01(\x01\x12\x13\n\x0bupper_bound\x18\x04 \x01(\x01\x12\x0c\n\x04name\x18\x05 \x01(\t\"\xc8\x01\n\x1eSecondOrderConeConstraintProto\x12H\n\x0bupper_bound\x18\x01 \x01(\x0b\x32\x33.operations_research.math_opt.LinearExpressionProto\x12N\n\x11\x61rguments_to_norm\x18\x02 \x03(\x0b\x32\x33.operations_research.math_opt.LinearExpressionProto\x12\x0c\n\x04name\x18\x03 \x01(\t\"}\n\x12SosConstraintProto\x12H\n\x0b\x65xpressions\x18\x01 \x03(\x0b\x32\x33.operations_research.math_opt.LinearExpressionProto\x12\x0f\n\x07weights\x18\x02 \x03(\x01\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xe3\x01\n\x18IndicatorConstraintProto\x12\x19\n\x0cindicator_id\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x18\n\x10\x61\x63tivate_on_zero\x18\x06 \x01(\x08\x12I\n\nexpression\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12\x13\n\x0blower_bound\x18\x03 \x01(\x01\x12\x13\n\x0bupper_bound\x18\x04 \x01(\x01\x12\x0c\n\x04name\x18\x05 \x01(\tB\x0f\n\r_indicator_id\"\xba\x0c\n\nModelProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12?\n\tvariables\x18\x02 \x01(\x0b\x32,.operations_research.math_opt.VariablesProto\x12?\n\tobjective\x18\x03 \x01(\x0b\x32,.operations_research.math_opt.ObjectiveProto\x12_\n\x14\x61uxiliary_objectives\x18\n \x03(\x0b\x32\x41.operations_research.math_opt.ModelProto.AuxiliaryObjectivesEntry\x12P\n\x12linear_constraints\x18\x04 \x01(\x0b\x32\x34.operations_research.math_opt.LinearConstraintsProto\x12W\n\x18linear_constraint_matrix\x18\x05 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleMatrixProto\x12\x61\n\x15quadratic_constraints\x18\x06 \x03(\x0b\x32\x42.operations_research.math_opt.ModelProto.QuadraticConstraintsEntry\x12o\n\x1dsecond_order_cone_constraints\x18\x0b \x03(\x0b\x32H.operations_research.math_opt.ModelProto.SecondOrderConeConstraintsEntry\x12W\n\x10sos1_constraints\x18\x07 \x03(\x0b\x32=.operations_research.math_opt.ModelProto.Sos1ConstraintsEntry\x12W\n\x10sos2_constraints\x18\x08 \x03(\x0b\x32=.operations_research.math_opt.ModelProto.Sos2ConstraintsEntry\x12\x61\n\x15indicator_constraints\x18\t \x03(\x0b\x32\x42.operations_research.math_opt.ModelProto.IndicatorConstraintsEntry\x1ah\n\x18\x41uxiliaryObjectivesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12;\n\x05value\x18\x02 \x01(\x0b\x32,.operations_research.math_opt.ObjectiveProto:\x02\x38\x01\x1as\n\x19QuadraticConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x45\n\x05value\x18\x02 \x01(\x0b\x32\x36.operations_research.math_opt.QuadraticConstraintProto:\x02\x38\x01\x1a\x7f\n\x1fSecondOrderConeConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12K\n\x05value\x18\x02 \x01(\x0b\x32<.operations_research.math_opt.SecondOrderConeConstraintProto:\x02\x38\x01\x1ah\n\x14Sos1ConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12?\n\x05value\x18\x02 \x01(\x0b\x32\x30.operations_research.math_opt.SosConstraintProto:\x02\x38\x01\x1ah\n\x14Sos2ConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12?\n\x05value\x18\x02 \x01(\x0b\x32\x30.operations_research.math_opt.SosConstraintProto:\x02\x38\x01\x1as\n\x19IndicatorConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x45\n\x05value\x18\x02 \x01(\x0b\x32\x36.operations_research.math_opt.IndicatorConstraintProto:\x02\x38\x01\x42\x1e\n\x1a\x63om.google.ortools.mathoptP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.model_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.ortools.mathoptP\001'
  _globals['_MODELPROTO_AUXILIARYOBJECTIVESENTRY']._loaded_options = None
  _globals['_MODELPROTO_AUXILIARYOBJECTIVESENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_QUADRATICCONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_SECONDORDERCONECONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_SECONDORDERCONECONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_SOS1CONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_SOS1CONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_SOS2CONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_SOS2CONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_INDICATORCONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_INDICATORCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_VARIABLESPROTO']._serialized_start=104
  _globals['_VARIABLESPROTO']._serialized_end=210
  _globals['_OBJECTIVEPROTO']._serialized_start=213
  _globals['_OBJECTIVEPROTO']._serialized_end=466
  _globals['_LINEARCONSTRAINTSPROTO']._serialized_start=468
  _globals['_LINEARCONSTRAINTSPROTO']._serialized_end=564
  _globals['_QUADRATICCONSTRAINTPROTO']._serialized_start=567
  _globals['_QUADRATICCONSTRAINTPROTO']._serialized_end=806
  _globals['_SECONDORDERCONECONSTRAINTPROTO']._serialized_start=809
  _globals['_SECONDORDERCONECONSTRAINTPROTO']._serialized_end=1009
  _globals['_SOSCONSTRAINTPROTO']._serialized_start=1011
  _globals['_SOSCONSTRAINTPROTO']._serialized_end=1136
  _globals['_INDICATORCONSTRAINTPROTO']._serialized_start=1139
  _globals['_INDICATORCONSTRAINTPROTO']._serialized_end=1366
  _globals['_MODELPROTO']._serialized_start=1369
  _globals['_MODELPROTO']._serialized_end=2963
  _globals['_MODELPROTO_AUXILIARYOBJECTIVESENTRY']._serialized_start=2284
  _globals['_MODELPROTO_AUXILIARYOBJECTIVESENTRY']._serialized_end=2388
  _globals['_MODELPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_start=2390
  _globals['_MODELPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_end=2505
  _globals['_MODELPROTO_SECONDORDERCONECONSTRAINTSENTRY']._serialized_start=2507
  _globals['_MODELPROTO_SECONDORDERCONECONSTRAINTSENTRY']._serialized_end=2634
  _globals['_MODELPROTO_SOS1CONSTRAINTSENTRY']._serialized_start=2636
  _globals['_MODELPROTO_SOS1CONSTRAINTSENTRY']._serialized_end=2740
  _globals['_MODELPROTO_SOS2CONSTRAINTSENTRY']._serialized_start=2742
  _globals['_MODELPROTO_SOS2CONSTRAINTSENTRY']._serialized_end=2846
  _globals['_MODELPROTO_INDICATORCONSTRAINTSENTRY']._serialized_start=2848
  _globals['_MODELPROTO_INDICATORCONSTRAINTSENTRY']._serialized_end=2963
# @@protoc_insertion_point(module_scope)
