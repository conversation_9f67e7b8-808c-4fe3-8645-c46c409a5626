"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Enums used to define routing parameters."""

import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class FirstSolutionStrategy(google.protobuf.message.Message):
    """First solution strategies, used as starting point of local search."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Value:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ValueEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[FirstSolutionStrategy._Value.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSET: FirstSolutionStrategy._Value.ValueType  # 0
        """See the homonymous value in LocalSearchMetaheuristic."""
        AUTOMATIC: FirstSolutionStrategy._Value.ValueType  # 15
        """Lets the solver detect which strategy to use according to the model being
        solved.
        """
        PATH_CHEAPEST_ARC: FirstSolutionStrategy._Value.ValueType  # 3
        """--- Path addition heuristics ---
        Starting from a route "start" node, connect it to the node which produces
        the cheapest route segment, then extend the route by iterating on the
        last node added to the route.
        """
        PATH_MOST_CONSTRAINED_ARC: FirstSolutionStrategy._Value.ValueType  # 4
        """Same as PATH_CHEAPEST_ARC, but arcs are evaluated with a comparison-based
        selector which will favor the most constrained arc first. To assign a
        selector to the routing model, see
        RoutingModel::ArcIsMoreConstrainedThanArc() in routing.h for details.
        """
        EVALUATOR_STRATEGY: FirstSolutionStrategy._Value.ValueType  # 5
        """Same as PATH_CHEAPEST_ARC, except that arc costs are evaluated using the
        function passed to RoutingModel::SetFirstSolutionEvaluator()
        (cf. routing.h).
        """
        SAVINGS: FirstSolutionStrategy._Value.ValueType  # 10
        """Savings algorithm (Clarke & Wright).
        Reference: Clarke, G. & Wright, J.W.:
        "Scheduling of Vehicles from a Central Depot to a Number of Delivery
        Points", Operations Research, Vol. 12, 1964, pp. 568-581
        """
        PARALLEL_SAVINGS: FirstSolutionStrategy._Value.ValueType  # 17
        """Parallel version of the Savings algorithm.
        Instead of extending a single route until it is no longer possible,
        the parallel version iteratively considers the next most improving
        feasible saving and possibly builds several routes in parallel.
        """
        SWEEP: FirstSolutionStrategy._Value.ValueType  # 11
        """Sweep algorithm (Wren & Holliday).
        Reference: Anthony Wren & Alan Holliday: Computer Scheduling of Vehicles
        from One or More Depots to a Number of Delivery Points Operational
        Research Quarterly (1970-1977),
        Vol. 23, No. 3 (Sep., 1972), pp. 333-344
        """
        CHRISTOFIDES: FirstSolutionStrategy._Value.ValueType  # 13
        """Christofides algorithm (actually a variant of the Christofides algorithm
        using a maximal matching instead of a maximum matching, which does
        not guarantee the 3/2 factor of the approximation on a metric travelling
        salesman). Works on generic vehicle routing models by extending a route
        until no nodes can be inserted on it.
        Reference: Nicos Christofides, Worst-case analysis of a new heuristic for
        the travelling salesman problem, Report 388, Graduate School of
        Industrial Administration, CMU, 1976.
        """
        ALL_UNPERFORMED: FirstSolutionStrategy._Value.ValueType  # 6
        """--- Path insertion heuristics ---
        Make all nodes inactive. Only finds a solution if nodes are optional (are
        element of a disjunction constraint with a finite penalty cost).
        """
        BEST_INSERTION: FirstSolutionStrategy._Value.ValueType  # 7
        """Iteratively build a solution by inserting the cheapest node at its
        cheapest position; the cost of insertion is based on the global cost
        function of the routing model. As of 2/2012, only works on models with
        optional nodes (with finite penalty costs).
        """
        PARALLEL_CHEAPEST_INSERTION: FirstSolutionStrategy._Value.ValueType  # 8
        """Iteratively build a solution by inserting the cheapest node at its
        cheapest position; the cost of insertion is based on the arc cost
        function. Is faster than BEST_INSERTION.
        """
        SEQUENTIAL_CHEAPEST_INSERTION: FirstSolutionStrategy._Value.ValueType  # 14
        """Iteratively build a solution by constructing routes sequentially, for
        each route inserting the cheapest node at its cheapest position until the
        route is completed; the cost of insertion is based on the arc cost
        function. Is faster than PARALLEL_CHEAPEST_INSERTION.
        """
        LOCAL_CHEAPEST_INSERTION: FirstSolutionStrategy._Value.ValueType  # 9
        """Iteratively build a solution by inserting each node at its cheapest
        position; the cost of insertion is based on the arc cost function.
        Differs from PARALLEL_CHEAPEST_INSERTION by the node selected for
        insertion; here nodes are considered in decreasing order of distance to
        the start/ends of the routes, i.e. farthest nodes are inserted first.
        Is faster than SEQUENTIAL_CHEAPEST_INSERTION.
        """
        LOCAL_CHEAPEST_COST_INSERTION: FirstSolutionStrategy._Value.ValueType  # 16
        """Same as LOCAL_CHEAPEST_INSERTION except that the cost of insertion is
        based on the routing model cost function instead of arc costs only.
        """
        GLOBAL_CHEAPEST_ARC: FirstSolutionStrategy._Value.ValueType  # 1
        """--- Variable-based heuristics ---
        Iteratively connect two nodes which produce the cheapest route segment.
        """
        LOCAL_CHEAPEST_ARC: FirstSolutionStrategy._Value.ValueType  # 2
        """Select the first node with an unbound successor and connect it to the
        node which produces the cheapest route segment.
        """
        FIRST_UNBOUND_MIN_VALUE: FirstSolutionStrategy._Value.ValueType  # 12
        """Select the first node with an unbound successor and connect it to the
        first available node.
        This is equivalent to the CHOOSE_FIRST_UNBOUND strategy combined with
        ASSIGN_MIN_VALUE (cf. constraint_solver.h).
        """

    class Value(_Value, metaclass=_ValueEnumTypeWrapper): ...
    UNSET: FirstSolutionStrategy.Value.ValueType  # 0
    """See the homonymous value in LocalSearchMetaheuristic."""
    AUTOMATIC: FirstSolutionStrategy.Value.ValueType  # 15
    """Lets the solver detect which strategy to use according to the model being
    solved.
    """
    PATH_CHEAPEST_ARC: FirstSolutionStrategy.Value.ValueType  # 3
    """--- Path addition heuristics ---
    Starting from a route "start" node, connect it to the node which produces
    the cheapest route segment, then extend the route by iterating on the
    last node added to the route.
    """
    PATH_MOST_CONSTRAINED_ARC: FirstSolutionStrategy.Value.ValueType  # 4
    """Same as PATH_CHEAPEST_ARC, but arcs are evaluated with a comparison-based
    selector which will favor the most constrained arc first. To assign a
    selector to the routing model, see
    RoutingModel::ArcIsMoreConstrainedThanArc() in routing.h for details.
    """
    EVALUATOR_STRATEGY: FirstSolutionStrategy.Value.ValueType  # 5
    """Same as PATH_CHEAPEST_ARC, except that arc costs are evaluated using the
    function passed to RoutingModel::SetFirstSolutionEvaluator()
    (cf. routing.h).
    """
    SAVINGS: FirstSolutionStrategy.Value.ValueType  # 10
    """Savings algorithm (Clarke & Wright).
    Reference: Clarke, G. & Wright, J.W.:
    "Scheduling of Vehicles from a Central Depot to a Number of Delivery
    Points", Operations Research, Vol. 12, 1964, pp. 568-581
    """
    PARALLEL_SAVINGS: FirstSolutionStrategy.Value.ValueType  # 17
    """Parallel version of the Savings algorithm.
    Instead of extending a single route until it is no longer possible,
    the parallel version iteratively considers the next most improving
    feasible saving and possibly builds several routes in parallel.
    """
    SWEEP: FirstSolutionStrategy.Value.ValueType  # 11
    """Sweep algorithm (Wren & Holliday).
    Reference: Anthony Wren & Alan Holliday: Computer Scheduling of Vehicles
    from One or More Depots to a Number of Delivery Points Operational
    Research Quarterly (1970-1977),
    Vol. 23, No. 3 (Sep., 1972), pp. 333-344
    """
    CHRISTOFIDES: FirstSolutionStrategy.Value.ValueType  # 13
    """Christofides algorithm (actually a variant of the Christofides algorithm
    using a maximal matching instead of a maximum matching, which does
    not guarantee the 3/2 factor of the approximation on a metric travelling
    salesman). Works on generic vehicle routing models by extending a route
    until no nodes can be inserted on it.
    Reference: Nicos Christofides, Worst-case analysis of a new heuristic for
    the travelling salesman problem, Report 388, Graduate School of
    Industrial Administration, CMU, 1976.
    """
    ALL_UNPERFORMED: FirstSolutionStrategy.Value.ValueType  # 6
    """--- Path insertion heuristics ---
    Make all nodes inactive. Only finds a solution if nodes are optional (are
    element of a disjunction constraint with a finite penalty cost).
    """
    BEST_INSERTION: FirstSolutionStrategy.Value.ValueType  # 7
    """Iteratively build a solution by inserting the cheapest node at its
    cheapest position; the cost of insertion is based on the global cost
    function of the routing model. As of 2/2012, only works on models with
    optional nodes (with finite penalty costs).
    """
    PARALLEL_CHEAPEST_INSERTION: FirstSolutionStrategy.Value.ValueType  # 8
    """Iteratively build a solution by inserting the cheapest node at its
    cheapest position; the cost of insertion is based on the arc cost
    function. Is faster than BEST_INSERTION.
    """
    SEQUENTIAL_CHEAPEST_INSERTION: FirstSolutionStrategy.Value.ValueType  # 14
    """Iteratively build a solution by constructing routes sequentially, for
    each route inserting the cheapest node at its cheapest position until the
    route is completed; the cost of insertion is based on the arc cost
    function. Is faster than PARALLEL_CHEAPEST_INSERTION.
    """
    LOCAL_CHEAPEST_INSERTION: FirstSolutionStrategy.Value.ValueType  # 9
    """Iteratively build a solution by inserting each node at its cheapest
    position; the cost of insertion is based on the arc cost function.
    Differs from PARALLEL_CHEAPEST_INSERTION by the node selected for
    insertion; here nodes are considered in decreasing order of distance to
    the start/ends of the routes, i.e. farthest nodes are inserted first.
    Is faster than SEQUENTIAL_CHEAPEST_INSERTION.
    """
    LOCAL_CHEAPEST_COST_INSERTION: FirstSolutionStrategy.Value.ValueType  # 16
    """Same as LOCAL_CHEAPEST_INSERTION except that the cost of insertion is
    based on the routing model cost function instead of arc costs only.
    """
    GLOBAL_CHEAPEST_ARC: FirstSolutionStrategy.Value.ValueType  # 1
    """--- Variable-based heuristics ---
    Iteratively connect two nodes which produce the cheapest route segment.
    """
    LOCAL_CHEAPEST_ARC: FirstSolutionStrategy.Value.ValueType  # 2
    """Select the first node with an unbound successor and connect it to the
    node which produces the cheapest route segment.
    """
    FIRST_UNBOUND_MIN_VALUE: FirstSolutionStrategy.Value.ValueType  # 12
    """Select the first node with an unbound successor and connect it to the
    first available node.
    This is equivalent to the CHOOSE_FIRST_UNBOUND strategy combined with
    ASSIGN_MIN_VALUE (cf. constraint_solver.h).
    """

    def __init__(
        self,
    ) -> None: ...

global___FirstSolutionStrategy = FirstSolutionStrategy

@typing.final
class LocalSearchMetaheuristic(google.protobuf.message.Message):
    """Local search metaheuristics used to guide the search. Apart from greedy
    descent, they will try to escape local minima.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Value:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ValueEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[LocalSearchMetaheuristic._Value.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        UNSET: LocalSearchMetaheuristic._Value.ValueType  # 0
        """Means "not set". If the solver sees that, it'll behave like for
        AUTOMATIC. But this value won't override others upon a proto MergeFrom(),
        whereas "AUTOMATIC" will.
        """
        AUTOMATIC: LocalSearchMetaheuristic._Value.ValueType  # 6
        """Lets the solver select the metaheuristic."""
        GREEDY_DESCENT: LocalSearchMetaheuristic._Value.ValueType  # 1
        """Accepts improving (cost-reducing) local search neighbors until a local
        minimum is reached.
        """
        GUIDED_LOCAL_SEARCH: LocalSearchMetaheuristic._Value.ValueType  # 2
        """Uses guided local search to escape local minima
        (cf. http://en.wikipedia.org/wiki/Guided_Local_Search); this is generally
        the most efficient metaheuristic for vehicle routing.
        """
        SIMULATED_ANNEALING: LocalSearchMetaheuristic._Value.ValueType  # 3
        """Uses simulated annealing to escape local minima
        (cf. http://en.wikipedia.org/wiki/Simulated_annealing).
        """
        TABU_SEARCH: LocalSearchMetaheuristic._Value.ValueType  # 4
        """Uses tabu search to escape local minima
        (cf. http://en.wikipedia.org/wiki/Tabu_search).
        """
        GENERIC_TABU_SEARCH: LocalSearchMetaheuristic._Value.ValueType  # 5
        """Uses tabu search on a list of variables to escape local minima. The list
        of variables to use must be provided via the SetTabuVarsCallback
        callback.
        """

    class Value(_Value, metaclass=_ValueEnumTypeWrapper): ...
    UNSET: LocalSearchMetaheuristic.Value.ValueType  # 0
    """Means "not set". If the solver sees that, it'll behave like for
    AUTOMATIC. But this value won't override others upon a proto MergeFrom(),
    whereas "AUTOMATIC" will.
    """
    AUTOMATIC: LocalSearchMetaheuristic.Value.ValueType  # 6
    """Lets the solver select the metaheuristic."""
    GREEDY_DESCENT: LocalSearchMetaheuristic.Value.ValueType  # 1
    """Accepts improving (cost-reducing) local search neighbors until a local
    minimum is reached.
    """
    GUIDED_LOCAL_SEARCH: LocalSearchMetaheuristic.Value.ValueType  # 2
    """Uses guided local search to escape local minima
    (cf. http://en.wikipedia.org/wiki/Guided_Local_Search); this is generally
    the most efficient metaheuristic for vehicle routing.
    """
    SIMULATED_ANNEALING: LocalSearchMetaheuristic.Value.ValueType  # 3
    """Uses simulated annealing to escape local minima
    (cf. http://en.wikipedia.org/wiki/Simulated_annealing).
    """
    TABU_SEARCH: LocalSearchMetaheuristic.Value.ValueType  # 4
    """Uses tabu search to escape local minima
    (cf. http://en.wikipedia.org/wiki/Tabu_search).
    """
    GENERIC_TABU_SEARCH: LocalSearchMetaheuristic.Value.ValueType  # 5
    """Uses tabu search on a list of variables to escape local minima. The list
    of variables to use must be provided via the SetTabuVarsCallback
    callback.
    """

    def __init__(
        self,
    ) -> None: ...

global___LocalSearchMetaheuristic = LocalSearchMetaheuristic

@typing.final
class RoutingSearchStatus(google.protobuf.message.Message):
    """Used by `RoutingModel` to report the status of the search for a solution."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _Value:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _ValueEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[RoutingSearchStatus._Value.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        ROUTING_NOT_SOLVED: RoutingSearchStatus._Value.ValueType  # 0
        """Problem not solved yet (before calling RoutingModel::Solve())."""
        ROUTING_SUCCESS: RoutingSearchStatus._Value.ValueType  # 1
        """Problem solved successfully after calling RoutingModel::Solve()."""
        ROUTING_PARTIAL_SUCCESS_LOCAL_OPTIMUM_NOT_REACHED: RoutingSearchStatus._Value.ValueType  # 2
        """Problem solved successfully after calling RoutingModel::Solve(), except
        that a local optimum has not been reached. Leaving more time would allow
        improving the solution.
        """
        ROUTING_FAIL: RoutingSearchStatus._Value.ValueType  # 3
        """No solution found to the problem after calling RoutingModel::Solve()."""
        ROUTING_FAIL_TIMEOUT: RoutingSearchStatus._Value.ValueType  # 4
        """Time limit reached before finding a solution with RoutingModel::Solve()."""
        ROUTING_INVALID: RoutingSearchStatus._Value.ValueType  # 5
        """Model, model parameters or flags are not valid."""
        ROUTING_INFEASIBLE: RoutingSearchStatus._Value.ValueType  # 6
        """Problem proven to be infeasible."""
        ROUTING_OPTIMAL: RoutingSearchStatus._Value.ValueType  # 7
        """Problem has been solved to optimality."""

    class Value(_Value, metaclass=_ValueEnumTypeWrapper): ...
    ROUTING_NOT_SOLVED: RoutingSearchStatus.Value.ValueType  # 0
    """Problem not solved yet (before calling RoutingModel::Solve())."""
    ROUTING_SUCCESS: RoutingSearchStatus.Value.ValueType  # 1
    """Problem solved successfully after calling RoutingModel::Solve()."""
    ROUTING_PARTIAL_SUCCESS_LOCAL_OPTIMUM_NOT_REACHED: RoutingSearchStatus.Value.ValueType  # 2
    """Problem solved successfully after calling RoutingModel::Solve(), except
    that a local optimum has not been reached. Leaving more time would allow
    improving the solution.
    """
    ROUTING_FAIL: RoutingSearchStatus.Value.ValueType  # 3
    """No solution found to the problem after calling RoutingModel::Solve()."""
    ROUTING_FAIL_TIMEOUT: RoutingSearchStatus.Value.ValueType  # 4
    """Time limit reached before finding a solution with RoutingModel::Solve()."""
    ROUTING_INVALID: RoutingSearchStatus.Value.ValueType  # 5
    """Model, model parameters or flags are not valid."""
    ROUTING_INFEASIBLE: RoutingSearchStatus.Value.ValueType  # 6
    """Problem proven to be infeasible."""
    ROUTING_OPTIMAL: RoutingSearchStatus.Value.ValueType  # 7
    """Problem has been solved to optimality."""

    def __init__(
        self,
    ) -> None: ...

global___RoutingSearchStatus = RoutingSearchStatus
