# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/sat/routes_support_graph.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/sat/routes_support_graph.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n&ortools/sat/routes_support_graph.proto\x12\x17operations_research.sat\":\n\nArcLpValue\x12\x0c\n\x04tail\x18\x01 \x01(\x05\x12\x0c\n\x04head\x18\x02 \x01(\x05\x12\x10\n\x08lp_value\x18\x03 \x01(\x01\"U\n\x17RoutesSupportGraphProto\x12:\n\rarc_lp_values\x18\x01 \x03(\x0b\x32#.operations_research.sat.ArcLpValue')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.sat.routes_support_graph_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_ARCLPVALUE']._serialized_start=67
  _globals['_ARCLPVALUE']._serialized_end=125
  _globals['_ROUTESSUPPORTGRAPHPROTO']._serialized_start=127
  _globals['_ROUTESSUPPORTGRAPHPROTO']._serialized_end=212
# @@protoc_insertion_point(module_scope)
