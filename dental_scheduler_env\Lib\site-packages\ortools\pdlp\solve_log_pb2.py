# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/pdlp/solve_log.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/pdlp/solve_log.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.pdlp import solvers_pb2 as ortools_dot_pdlp_dot_solvers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cortools/pdlp/solve_log.proto\x12\x18operations_research.pdlp\x1a\x1aortools/pdlp/solvers.proto\"\xd2\x08\n\x15QuadraticProgramStats\x12\x15\n\rnum_variables\x18\x01 \x01(\x03\x12\x17\n\x0fnum_constraints\x18\x02 \x01(\x03\x12,\n$constraint_matrix_col_min_l_inf_norm\x18\x03 \x01(\x01\x12,\n$constraint_matrix_row_min_l_inf_norm\x18\x04 \x01(\x01\x12&\n\x1e\x63onstraint_matrix_num_nonzeros\x18\x05 \x01(\x03\x12!\n\x19\x63onstraint_matrix_abs_max\x18\x06 \x01(\x01\x12!\n\x19\x63onstraint_matrix_abs_min\x18\x07 \x01(\x01\x12!\n\x19\x63onstraint_matrix_abs_avg\x18\x08 \x01(\x01\x12!\n\x19\x63onstraint_matrix_l2_norm\x18\x19 \x01(\x01\x12\x1b\n\x13\x63ombined_bounds_max\x18\t \x01(\x01\x12\x1b\n\x13\x63ombined_bounds_min\x18\n \x01(\x01\x12\x1b\n\x13\x63ombined_bounds_avg\x18\x0b \x01(\x01\x12\x1f\n\x17\x63ombined_bounds_l2_norm\x18\x18 \x01(\x01\x12$\n\x1c\x63ombined_variable_bounds_max\x18\x1c \x01(\x01\x12$\n\x1c\x63ombined_variable_bounds_min\x18\x1d \x01(\x01\x12$\n\x1c\x63ombined_variable_bounds_avg\x18\x1e \x01(\x01\x12(\n combined_variable_bounds_l2_norm\x18\x1f \x01(\x01\x12&\n\x1evariable_bound_gaps_num_finite\x18\x0c \x01(\x03\x12\x1f\n\x17variable_bound_gaps_max\x18\r \x01(\x01\x12\x1f\n\x17variable_bound_gaps_min\x18\x0e \x01(\x01\x12\x1f\n\x17variable_bound_gaps_avg\x18\x0f \x01(\x01\x12#\n\x1bvariable_bound_gaps_l2_norm\x18\x1a \x01(\x01\x12 \n\x18objective_vector_abs_max\x18\x10 \x01(\x01\x12 \n\x18objective_vector_abs_min\x18\x11 \x01(\x01\x12 \n\x18objective_vector_abs_avg\x18\x12 \x01(\x01\x12 \n\x18objective_vector_l2_norm\x18\x17 \x01(\x01\x12%\n\x1dobjective_matrix_num_nonzeros\x18\x13 \x01(\x03\x12 \n\x18objective_matrix_abs_max\x18\x14 \x01(\x01\x12 \n\x18objective_matrix_abs_min\x18\x15 \x01(\x01\x12 \n\x18objective_matrix_abs_avg\x18\x16 \x01(\x01\x12 \n\x18objective_matrix_l2_norm\x18\x1b \x01(\x01\"\xa7\x04\n\x16\x43onvergenceInformation\x12;\n\x0e\x63\x61ndidate_type\x18\x01 \x01(\x0e\x32#.operations_research.pdlp.PointType\x12\x18\n\x10primal_objective\x18\x02 \x01(\x01\x12\x16\n\x0e\x64ual_objective\x18\x03 \x01(\x01\x12 \n\x18\x63orrected_dual_objective\x18\x04 \x01(\x01\x12\x1d\n\x15l_inf_primal_residual\x18\x05 \x01(\x01\x12\x1a\n\x12l2_primal_residual\x18\x06 \x01(\x01\x12+\n#l_inf_componentwise_primal_residual\x18\x18 \x01(\x01\x12\x1b\n\x13l_inf_dual_residual\x18\x07 \x01(\x01\x12\x18\n\x10l2_dual_residual\x18\x08 \x01(\x01\x12)\n!l_inf_componentwise_dual_residual\x18\x19 \x01(\x01\x12\x1d\n\x15l_inf_primal_variable\x18\x0e \x01(\x01\x12\x1a\n\x12l2_primal_variable\x18\x0f \x01(\x01\x12\x1b\n\x13l_inf_dual_variable\x18\x10 \x01(\x01\x12\x18\n\x10l2_dual_variable\x18\x11 \x01(\x01J\x04\x08\t\x10\nJ\x04\x08\n\x10\x0bJ\x04\x08\x0b\x10\x0cJ\x04\x08\x0c\x10\rJ\x04\x08\r\x10\x0eJ\x04\x08\x12\x10\x13J\x04\x08\x13\x10\x14J\x04\x08\x14\x10\x15J\x04\x08\x15\x10\x16J\x04\x08\x16\x10\x17J\x04\x08\x17\x10\x18\"\x91\x02\n\x18InfeasibilityInformation\x12$\n\x1cmax_primal_ray_infeasibility\x18\x01 \x01(\x01\x12#\n\x1bprimal_ray_linear_objective\x18\x02 \x01(\x01\x12!\n\x19primal_ray_quadratic_norm\x18\x03 \x01(\x01\x12\"\n\x1amax_dual_ray_infeasibility\x18\x04 \x01(\x01\x12\x1a\n\x12\x64ual_ray_objective\x18\x05 \x01(\x01\x12;\n\x0e\x63\x61ndidate_type\x18\x06 \x01(\x0e\x32#.operations_research.pdlp.PointTypeJ\x04\x08\x07\x10\x08J\x04\x08\x08\x10\t\"\xaa\x02\n\rPointMetadata\x12\x37\n\npoint_type\x18\x01 \x01(\x0e\x32#.operations_research.pdlp.PointType\x12%\n\x19random_primal_projections\x18\x02 \x03(\x01\x42\x02\x10\x01\x12#\n\x17random_dual_projections\x18\x03 \x03(\x01\x42\x02\x10\x01\x12$\n\x1c\x61\x63tive_primal_variable_count\x18\x04 \x01(\x03\x12\"\n\x1a\x61\x63tive_dual_variable_count\x18\x05 \x01(\x03\x12%\n\x1d\x61\x63tive_primal_variable_change\x18\x06 \x01(\x03\x12#\n\x1b\x61\x63tive_dual_variable_change\x18\x07 \x01(\x03\"\xea\x03\n\x0eIterationStats\x12\x18\n\x10iteration_number\x18\x01 \x01(\x05\x12Q\n\x17\x63onvergence_information\x18\x02 \x03(\x0b\x32\x30.operations_research.pdlp.ConvergenceInformation\x12U\n\x19infeasibility_information\x18\x03 \x03(\x0b\x32\x32.operations_research.pdlp.InfeasibilityInformation\x12?\n\x0epoint_metadata\x18\x0b \x03(\x0b\x32\'.operations_research.pdlp.PointMetadata\x12$\n\x1c\x63umulative_kkt_matrix_passes\x18\x04 \x01(\x01\x12!\n\x19\x63umulative_rejected_steps\x18\x05 \x01(\x05\x12\x1b\n\x13\x63umulative_time_sec\x18\x06 \x01(\x01\x12=\n\x0crestart_used\x18\x07 \x01(\x0e\x32\'.operations_research.pdlp.RestartChoice\x12\x11\n\tstep_size\x18\x08 \x01(\x01\x12\x15\n\rprimal_weight\x18\t \x01(\x01J\x04\x08\n\x10\x0b\"\x8c\x04\n\x1b\x46\x65\x61sibilityPolishingDetails\x12J\n\x14polishing_phase_type\x18\x01 \x01(\x0e\x32,.operations_research.pdlp.PolishingPhaseType\x12\x1c\n\x14main_iteration_count\x18\x02 \x01(\x05\x12H\n\x06params\x18\x03 \x01(\x0b\x32\x38.operations_research.pdlp.PrimalDualHybridGradientParams\x12G\n\x12termination_reason\x18\x04 \x01(\x0e\x32+.operations_research.pdlp.TerminationReason\x12\x17\n\x0fiteration_count\x18\x05 \x01(\x05\x12\x16\n\x0esolve_time_sec\x18\x06 \x01(\x01\x12@\n\x0esolution_stats\x18\x07 \x01(\x0b\x32(.operations_research.pdlp.IterationStats\x12:\n\rsolution_type\x18\x08 \x01(\x0e\x32#.operations_research.pdlp.PointType\x12\x41\n\x0fiteration_stats\x18\t \x03(\x0b\x32(.operations_research.pdlp.IterationStats\"\xf2\x05\n\x08SolveLog\x12\x15\n\rinstance_name\x18\x01 \x01(\t\x12H\n\x06params\x18\x0e \x01(\x0b\x32\x38.operations_research.pdlp.PrimalDualHybridGradientParams\x12G\n\x12termination_reason\x18\x03 \x01(\x0e\x32+.operations_research.pdlp.TerminationReason\x12\x1a\n\x12termination_string\x18\x04 \x01(\t\x12\x17\n\x0fiteration_count\x18\x05 \x01(\x05\x12\x1e\n\x16preprocessing_time_sec\x18\r \x01(\x01\x12\x16\n\x0esolve_time_sec\x18\x06 \x01(\x01\x12@\n\x0esolution_stats\x18\x08 \x01(\x0b\x32(.operations_research.pdlp.IterationStats\x12:\n\rsolution_type\x18\n \x01(\x0e\x32#.operations_research.pdlp.PointType\x12\x41\n\x0fiteration_stats\x18\x07 \x03(\x0b\x32(.operations_research.pdlp.IterationStats\x12O\n\x16original_problem_stats\x18\x0b \x01(\x0b\x32/.operations_research.pdlp.QuadraticProgramStats\x12S\n\x1apreprocessed_problem_stats\x18\x0c \x01(\x0b\x32/.operations_research.pdlp.QuadraticProgramStats\x12\\\n\x1d\x66\x65\x61sibility_polishing_details\x18\x0f \x03(\x0b\x32\x35.operations_research.pdlp.FeasibilityPolishingDetailsJ\x04\x08\x02\x10\x03J\x04\x08\t\x10\n*\xa0\x01\n\rRestartChoice\x12\x1e\n\x1aRESTART_CHOICE_UNSPECIFIED\x10\x00\x12\x1d\n\x19RESTART_CHOICE_NO_RESTART\x10\x01\x12)\n%RESTART_CHOICE_WEIGHTED_AVERAGE_RESET\x10\x02\x12%\n!RESTART_CHOICE_RESTART_TO_AVERAGE\x10\x03*\xf1\x01\n\tPointType\x12\x1a\n\x16POINT_TYPE_UNSPECIFIED\x10\x00\x12\x1e\n\x1aPOINT_TYPE_CURRENT_ITERATE\x10\x01\x12!\n\x1dPOINT_TYPE_ITERATE_DIFFERENCE\x10\x02\x12\x1e\n\x1aPOINT_TYPE_AVERAGE_ITERATE\x10\x03\x12\x13\n\x0fPOINT_TYPE_NONE\x10\x04\x12!\n\x1dPOINT_TYPE_PRESOLVER_SOLUTION\x10\x05\x12-\n)POINT_TYPE_FEASIBILITY_POLISHING_SOLUTION\x10\x06*\xc9\x04\n\x11TerminationReason\x12\"\n\x1eTERMINATION_REASON_UNSPECIFIED\x10\x00\x12\x1e\n\x1aTERMINATION_REASON_OPTIMAL\x10\x01\x12(\n$TERMINATION_REASON_PRIMAL_INFEASIBLE\x10\x02\x12&\n\"TERMINATION_REASON_DUAL_INFEASIBLE\x10\x03\x12!\n\x1dTERMINATION_REASON_TIME_LIMIT\x10\x04\x12&\n\"TERMINATION_REASON_ITERATION_LIMIT\x10\x05\x12,\n(TERMINATION_REASON_KKT_MATRIX_PASS_LIMIT\x10\x08\x12*\n&TERMINATION_REASON_INTERRUPTED_BY_USER\x10\x0c\x12&\n\"TERMINATION_REASON_NUMERICAL_ERROR\x10\x06\x12&\n\"TERMINATION_REASON_INVALID_PROBLEM\x10\t\x12/\n+TERMINATION_REASON_INVALID_INITIAL_SOLUTION\x10\r\x12(\n$TERMINATION_REASON_INVALID_PARAMETER\x10\n\x12\x1c\n\x18TERMINATION_REASON_OTHER\x10\x07\x12\x30\n,TERMINATION_REASON_PRIMAL_OR_DUAL_INFEASIBLE\x10\x0b*\x92\x01\n\x12PolishingPhaseType\x12$\n POLISHING_PHASE_TYPE_UNSPECIFIED\x10\x00\x12+\n\'POLISHING_PHASE_TYPE_PRIMAL_FEASIBILITY\x10\x01\x12)\n%POLISHING_PHASE_TYPE_DUAL_FEASIBILITY\x10\x02\x42\x31\n\x17\x63om.google.ortools.pdlpP\x01\xaa\x02\x13Google.OrTools.PDLP')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.pdlp.solve_log_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\027com.google.ortools.pdlpP\001\252\002\023Google.OrTools.PDLP'
  _globals['_POINTMETADATA'].fields_by_name['random_primal_projections']._loaded_options = None
  _globals['_POINTMETADATA'].fields_by_name['random_primal_projections']._serialized_options = b'\020\001'
  _globals['_POINTMETADATA'].fields_by_name['random_dual_projections']._loaded_options = None
  _globals['_POINTMETADATA'].fields_by_name['random_dual_projections']._serialized_options = b'\020\001'
  _globals['_RESTARTCHOICE']._serialized_start=4104
  _globals['_RESTARTCHOICE']._serialized_end=4264
  _globals['_POINTTYPE']._serialized_start=4267
  _globals['_POINTTYPE']._serialized_end=4508
  _globals['_TERMINATIONREASON']._serialized_start=4511
  _globals['_TERMINATIONREASON']._serialized_end=5096
  _globals['_POLISHINGPHASETYPE']._serialized_start=5099
  _globals['_POLISHINGPHASETYPE']._serialized_end=5245
  _globals['_QUADRATICPROGRAMSTATS']._serialized_start=87
  _globals['_QUADRATICPROGRAMSTATS']._serialized_end=1193
  _globals['_CONVERGENCEINFORMATION']._serialized_start=1196
  _globals['_CONVERGENCEINFORMATION']._serialized_end=1747
  _globals['_INFEASIBILITYINFORMATION']._serialized_start=1750
  _globals['_INFEASIBILITYINFORMATION']._serialized_end=2023
  _globals['_POINTMETADATA']._serialized_start=2026
  _globals['_POINTMETADATA']._serialized_end=2324
  _globals['_ITERATIONSTATS']._serialized_start=2327
  _globals['_ITERATIONSTATS']._serialized_end=2817
  _globals['_FEASIBILITYPOLISHINGDETAILS']._serialized_start=2820
  _globals['_FEASIBILITYPOLISHINGDETAILS']._serialized_end=3344
  _globals['_SOLVELOG']._serialized_start=3347
  _globals['_SOLVELOG']._serialized_end=4101
# @@protoc_insertion_point(module_scope)
