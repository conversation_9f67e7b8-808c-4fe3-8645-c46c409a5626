
'''DO NOT EDIT: This file is autogenerated.'''

import enum
from typing import Generic, TypeVar, Union

import numpy as np
class ElementType(enum.Enum):
  VARIABLE = 0
  LINEAR_CONSTRAINT = 1
  AUXILIARY_OBJECTIVE = 2
  QUADRATIC_CONSTRAINT = 3
  INDICATOR_CONSTRAINT = 4


AttrValueType = TypeVar('AttrValueType', np.bool_, np.float64, np.int64)

AttrPyValueType = TypeVar('AttrPyValueType', bool, float, int)

class Attr(Generic[AttrValueType]):
  pass

class PyAttr(Generic[AttrPyValueType]):
  pass

class BoolAttr0(Attr[np.bool_], PyAttr[bool], int, enum.Enum):
  MAXIMIZE = 0


class BoolAttr1(Attr[np.bool_], <PERSON>y<PERSON>ttr[bool], int, enum.Enum):
  VARIABLE_INTEGER = 0
  AUXILIARY_OBJECTIVE_MAXIMIZE = 1
  INDICATOR_CONSTRAINT_ACTIVATE_ON_ZERO = 2


class IntAttr0(Attr[np.int64], PyAttr[int], int, enum.Enum):
  OBJECTIVE_PRIORITY = 0


class IntAttr1(Attr[np.int64], PyAttr[int], int, enum.Enum):
  AUXILIARY_OBJECTIVE_PRIORITY = 0


class DoubleAttr0(Attr[np.float64], PyAttr[float], int, enum.Enum):
  OBJECTIVE_OFFSET = 0


class DoubleAttr1(Attr[np.float64], PyAttr[float], int, enum.Enum):
  VARIABLE_LOWER_BOUND = 0
  VARIABLE_UPPER_BOUND = 1
  OBJECTIVE_LINEAR_COEFFICIENT = 2
  LINEAR_CONSTRAINT_LOWER_BOUND = 3
  LINEAR_CONSTRAINT_UPPER_BOUND = 4
  AUXILIARY_OBJECTIVE_OFFSET = 5
  QUADRATIC_CONSTRAINT_LOWER_BOUND = 6
  QUADRATIC_CONSTRAINT_UPPER_BOUND = 7
  INDICATOR_CONSTRAINT_LOWER_BOUND = 8
  INDICATOR_CONSTRAINT_UPPER_BOUND = 9


class DoubleAttr2(Attr[np.float64], PyAttr[float], int, enum.Enum):
  LINEAR_CONSTRAINT_COEFFICIENT = 0
  AUXILIARY_OBJECTIVE_LINEAR_COEFFICIENT = 1
  QUADRATIC_CONSTRAINT_LINEAR_COEFFICIENT = 2
  INDICATOR_CONSTRAINT_LINEAR_COEFFICIENT = 3


class SymmetricDoubleAttr2(Attr[np.float64], PyAttr[float], int, enum.Enum):
  OBJECTIVE_QUADRATIC_COEFFICIENT = 0


class SymmetricDoubleAttr3(Attr[np.float64], PyAttr[float], int, enum.Enum):
  QUADRATIC_CONSTRAINT_QUADRATIC_COEFFICIENT = 0


class VariableAttr1(Attr[np.int64], PyAttr[int], int, enum.Enum):
  INDICATOR_CONSTRAINT_INDICATOR = 0

AnyAttr = Union[BoolAttr0, BoolAttr1, IntAttr0, IntAttr1, DoubleAttr0, DoubleAttr1, DoubleAttr2, SymmetricDoubleAttr2, SymmetricDoubleAttr3, VariableAttr1]
