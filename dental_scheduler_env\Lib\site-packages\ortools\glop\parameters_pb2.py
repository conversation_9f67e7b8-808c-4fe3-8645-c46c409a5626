# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/glop/parameters.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/glop/parameters.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dortools/glop/parameters.proto\x12\x18operations_research.glop\"\xf0\x18\n\x0eGlopParameters\x12`\n\x0escaling_method\x18\x39 \x01(\x0e\x32\x39.operations_research.glop.GlopParameters.ScalingAlgorithm:\rEQUILIBRATION\x12]\n\x10\x66\x65\x61sibility_rule\x18\x01 \x01(\x0e\x32\x34.operations_research.glop.GlopParameters.PricingRule:\rSTEEPEST_EDGE\x12^\n\x11optimization_rule\x18\x02 \x01(\x0e\x32\x34.operations_research.glop.GlopParameters.PricingRule:\rSTEEPEST_EDGE\x12(\n\x19refactorization_threshold\x18\x06 \x01(\x01:\x05\x31\x65-09\x12\x30\n!recompute_reduced_costs_threshold\x18\x08 \x01(\x01:\x05\x31\x65-08\x12+\n\x1erecompute_edges_norm_threshold\x18\t \x01(\x01:\x03\x31\x30\x30\x12+\n\x1cprimal_feasibility_tolerance\x18\n \x01(\x01:\x05\x31\x65-08\x12)\n\x1a\x64ual_feasibility_tolerance\x18\x0b \x01(\x01:\x05\x31\x65-08\x12(\n\x19ratio_test_zero_threshold\x18\x0c \x01(\x01:\x05\x31\x65-09\x12#\n\x16harris_tolerance_ratio\x18\r \x01(\x01:\x03\x30.5\x12$\n\x15small_pivot_threshold\x18\x0e \x01(\x01:\x05\x31\x65-06\x12\'\n\x18minimum_acceptable_pivot\x18\x0f \x01(\x01:\x05\x31\x65-06\x12\x1d\n\x0e\x64rop_tolerance\x18\x34 \x01(\x01:\x05\x31\x65-14\x12\x19\n\x0buse_scaling\x18\x10 \x01(\x08:\x04true\x12m\n\x0c\x63ost_scaling\x18< \x01(\x0e\x32=.operations_research.glop.GlopParameters.CostScalingAlgorithm:\x18\x43ONTAIN_ONE_COST_SCALING\x12\x61\n\rinitial_basis\x18\x11 \x01(\x0e\x32>.operations_research.glop.GlopParameters.InitialBasisHeuristic:\nTRIANGULAR\x12#\n\x15use_transposed_matrix\x18\x12 \x01(\x08:\x04true\x12(\n\x1c\x62\x61sis_refactorization_period\x18\x13 \x01(\x05:\x02\x36\x34\x12\x37\n)dynamically_adjust_refactorization_period\x18? \x01(\x08:\x04true\x12\x66\n\x12solve_dual_problem\x18\x14 \x01(\x0e\x32\x37.operations_research.glop.GlopParameters.SolverBehavior:\x11LET_SOLVER_DECIDE\x12\x1f\n\x12\x64ualizer_threshold\x18\x15 \x01(\x01:\x03\x31.5\x12-\n\x1esolution_feasibility_tolerance\x18\x16 \x01(\x01:\x05\x31\x65-06\x12.\n provide_strong_optimal_guarantee\x18\x18 \x01(\x08:\x04true\x12(\n\x1a\x63hange_status_to_imprecise\x18: \x01(\x08:\x04true\x12)\n\x1dmax_number_of_reoptimizations\x18\x38 \x01(\x01:\x02\x34\x30\x12.\n lu_factorization_pivot_threshold\x18\x19 \x01(\x01:\x04\x30.01\x12 \n\x13max_time_in_seconds\x18\x1a \x01(\x01:\x03inf\x12#\n\x16max_deterministic_time\x18- \x01(\x01:\x03inf\x12$\n\x18max_number_of_iterations\x18\x1b \x01(\x03:\x02-1\x12%\n\x1amarkowitz_zlatev_parameter\x18\x1d \x01(\x05:\x01\x33\x12.\n\x1fmarkowitz_singularity_threshold\x18\x1e \x01(\x01:\x05\x31\x65-15\x12\x1f\n\x10use_dual_simplex\x18\x1f \x01(\x08:\x05\x66\x61lse\x12-\n\x1e\x61llow_simplex_algorithm_change\x18  \x01(\x08:\x05\x66\x61lse\x12\'\n\x1a\x64\x65vex_weights_reset_period\x18! \x01(\x05:\x03\x31\x35\x30\x12\x1f\n\x11use_preprocessing\x18\" \x01(\x08:\x04true\x12,\n\x1euse_middle_product_form_update\x18# \x01(\x08:\x04true\x12\x30\n\"initialize_devex_with_column_norms\x18$ \x01(\x08:\x04true\x12\x37\n)exploit_singleton_column_in_initial_basis\x18% \x01(\x08:\x04true\x12*\n\x1a\x64ual_small_pivot_threshold\x18& \x01(\x01:\x06\x30.0001\x12*\n\x1bpreprocessor_zero_tolerance\x18\' \x01(\x01:\x05\x31\x65-09\x12#\n\x15objective_lower_limit\x18( \x01(\x01:\x04-inf\x12\"\n\x15objective_upper_limit\x18) \x01(\x01:\x03inf\x12(\n\x1a\x64\x65generate_ministep_factor\x18* \x01(\x01:\x04\x30.01\x12\x16\n\x0brandom_seed\x18+ \x01(\x05:\x01\x31\x12\x1e\n\x0fuse_absl_random\x18H \x01(\x08:\x05\x66\x61lse\x12\x1a\n\x0fnum_omp_threads\x18, \x01(\x05:\x01\x31\x12,\n\x1dperturb_costs_in_dual_simplex\x18\x35 \x01(\x08:\x05\x66\x61lse\x12\x36\n(use_dedicated_dual_feasibility_algorithm\x18> \x01(\x08:\x04true\x12)\n\x1arelative_cost_perturbation\x18\x36 \x01(\x01:\x05\x31\x65-05\x12-\n\x1erelative_max_cost_perturbation\x18\x37 \x01(\x01:\x05\x31\x65-07\x12\x31\n\"initial_condition_number_threshold\x18; \x01(\x01:\x05\x31\x65+50\x12\"\n\x13log_search_progress\x18= \x01(\x08:\x05\x66\x61lse\x12\x1b\n\rlog_to_stdout\x18\x42 \x01(\x08:\x04true\x12.\n!crossover_bound_snapping_distance\x18@ \x01(\x01:\x03inf\x12\x1c\n\x0epush_to_vertex\x18\x41 \x01(\x08:\x04true\x12+\n\x1duse_implied_free_preprocessor\x18\x43 \x01(\x08:\x04true\x12\"\n\x13max_valid_magnitude\x18\x46 \x01(\x01:\x05\x31\x65+30\x12\x1d\n\x0e\x64rop_magnitude\x18G \x01(\x01:\x05\x31\x65-30\x12)\n\x1a\x64ual_price_prioritize_norm\x18\x45 \x01(\x08:\x05\x66\x61lse\"F\n\x10ScalingAlgorithm\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x11\n\rEQUILIBRATION\x10\x01\x12\x12\n\x0eLINEAR_PROGRAM\x10\x02\"D\n\x0eSolverBehavior\x12\r\n\tALWAYS_DO\x10\x00\x12\x0c\n\x08NEVER_DO\x10\x01\x12\x15\n\x11LET_SOLVER_DECIDE\x10\x02\"8\n\x0bPricingRule\x12\x0b\n\x07\x44\x41NTZIG\x10\x00\x12\x11\n\rSTEEPEST_EDGE\x10\x01\x12\t\n\x05\x44\x45VEX\x10\x02\"G\n\x15InitialBasisHeuristic\x12\x08\n\x04NONE\x10\x00\x12\t\n\x05\x42IXBY\x10\x01\x12\x0e\n\nTRIANGULAR\x10\x02\x12\t\n\x05MAROS\x10\x03\"y\n\x14\x43ostScalingAlgorithm\x12\x13\n\x0fNO_COST_SCALING\x10\x00\x12\x1c\n\x18\x43ONTAIN_ONE_COST_SCALING\x10\x01\x12\x15\n\x11MEAN_COST_SCALING\x10\x02\x12\x17\n\x13MEDIAN_COST_SCALING\x10\x03\x42\x31\n\x17\x63om.google.ortools.glopP\x01\xaa\x02\x13Google.OrTools.Glop')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.glop.parameters_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\027com.google.ortools.glopP\001\252\002\023Google.OrTools.Glop'
  _globals['_GLOPPARAMETERS']._serialized_start=60
  _globals['_GLOPPARAMETERS']._serialized_end=3244
  _globals['_GLOPPARAMETERS_SCALINGALGORITHM']._serialized_start=2850
  _globals['_GLOPPARAMETERS_SCALINGALGORITHM']._serialized_end=2920
  _globals['_GLOPPARAMETERS_SOLVERBEHAVIOR']._serialized_start=2922
  _globals['_GLOPPARAMETERS_SOLVERBEHAVIOR']._serialized_end=2990
  _globals['_GLOPPARAMETERS_PRICINGRULE']._serialized_start=2992
  _globals['_GLOPPARAMETERS_PRICINGRULE']._serialized_end=3048
  _globals['_GLOPPARAMETERS_INITIALBASISHEURISTIC']._serialized_start=3050
  _globals['_GLOPPARAMETERS_INITIALBASISHEURISTIC']._serialized_end=3121
  _globals['_GLOPPARAMETERS_COSTSCALINGALGORITHM']._serialized_start=3123
  _globals['_GLOPPARAMETERS_COSTSCALINGALGORITHM']._serialized_end=3244
# @@protoc_insertion_point(module_scope)
