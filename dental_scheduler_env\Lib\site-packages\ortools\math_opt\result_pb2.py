# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/result.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/result.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from ortools.pdlp import solve_log_pb2 as ortools_dot_pdlp_dot_solve__log__pb2
from ortools.gscip import gscip_pb2 as ortools_dot_gscip_dot_gscip__pb2
from ortools.math_opt import solution_pb2 as ortools_dot_math__opt_dot_solution__pb2
from ortools.math_opt.solvers import osqp_pb2 as ortools_dot_math__opt_dot_solvers_dot_osqp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dortools/math_opt/result.proto\x12\x1coperations_research.math_opt\x1a\x1egoogle/protobuf/duration.proto\x1a\x1cortools/pdlp/solve_log.proto\x1a\x19ortools/gscip/gscip.proto\x1a\x1fortools/math_opt/solution.proto\x1a#ortools/math_opt/solvers/osqp.proto\"\xcf\x01\n\x12ProblemStatusProto\x12K\n\rprimal_status\x18\x01 \x01(\x0e\x32\x34.operations_research.math_opt.FeasibilityStatusProto\x12I\n\x0b\x64ual_status\x18\x02 \x01(\x0e\x32\x34.operations_research.math_opt.FeasibilityStatusProto\x12!\n\x19primal_or_dual_infeasible\x18\x03 \x01(\x08\"\xb6\x02\n\x0fSolveStatsProto\x12-\n\nsolve_time\x18\x01 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x1d\n\x11\x62\x65st_primal_bound\x18\x02 \x01(\x01\x42\x02\x18\x01\x12\x1b\n\x0f\x62\x65st_dual_bound\x18\x03 \x01(\x01\x42\x02\x18\x01\x12L\n\x0eproblem_status\x18\x04 \x01(\x0b\x32\x30.operations_research.math_opt.ProblemStatusProtoB\x02\x18\x01\x12\x1a\n\x12simplex_iterations\x18\x05 \x01(\x03\x12\x1a\n\x12\x62\x61rrier_iterations\x18\x06 \x01(\x03\x12\x1e\n\x16\x66irst_order_iterations\x18\x08 \x01(\x03\x12\x12\n\nnode_count\x18\x07 \x01(\x03\"@\n\x14ObjectiveBoundsProto\x12\x14\n\x0cprimal_bound\x18\x02 \x01(\x01\x12\x12\n\ndual_bound\x18\x03 \x01(\x01\"\xb9\x02\n\x10TerminationProto\x12\x44\n\x06reason\x18\x01 \x01(\x0e\x32\x34.operations_research.math_opt.TerminationReasonProto\x12\x37\n\x05limit\x18\x02 \x01(\x0e\x32(.operations_research.math_opt.LimitProto\x12\x0e\n\x06\x64\x65tail\x18\x03 \x01(\t\x12H\n\x0eproblem_status\x18\x04 \x01(\x0b\x32\x30.operations_research.math_opt.ProblemStatusProto\x12L\n\x10objective_bounds\x18\x05 \x01(\x0b\x32\x32.operations_research.math_opt.ObjectiveBoundsProto\"\xab\x05\n\x10SolveResultProto\x12\x43\n\x0btermination\x18\x02 \x01(\x0b\x32..operations_research.math_opt.TerminationProto\x12>\n\tsolutions\x18\x03 \x03(\x0b\x32+.operations_research.math_opt.SolutionProto\x12\x41\n\x0bprimal_rays\x18\x04 \x03(\x0b\x32,.operations_research.math_opt.PrimalRayProto\x12=\n\tdual_rays\x18\x05 \x03(\x0b\x32*.operations_research.math_opt.DualRayProto\x12\x42\n\x0bsolve_stats\x18\x06 \x01(\x0b\x32-.operations_research.math_opt.SolveStatsProto\x12\x38\n\x0cgscip_output\x18\x07 \x01(\x0b\x32 .operations_research.GScipOutputH\x00\x12?\n\x0bosqp_output\x18\x08 \x01(\x0b\x32(.operations_research.math_opt.OsqpOutputH\x00\x12P\n\x0bpdlp_output\x18\t \x01(\x0b\x32\x39.operations_research.math_opt.SolveResultProto.PdlpOutputH\x00\x1a_\n\nPdlpOutput\x12Q\n\x17\x63onvergence_information\x18\x01 \x01(\x0b\x32\x30.operations_research.pdlp.ConvergenceInformationB\x18\n\x16solver_specific_outputJ\x04\x08\x01\x10\x02*\xa5\x01\n\x16\x46\x65\x61sibilityStatusProto\x12\"\n\x1e\x46\x45\x41SIBILITY_STATUS_UNSPECIFIED\x10\x00\x12#\n\x1f\x46\x45\x41SIBILITY_STATUS_UNDETERMINED\x10\x01\x12\x1f\n\x1b\x46\x45\x41SIBILITY_STATUS_FEASIBLE\x10\x02\x12!\n\x1d\x46\x45\x41SIBILITY_STATUS_INFEASIBLE\x10\x03*\x8a\x03\n\x16TerminationReasonProto\x12\"\n\x1eTERMINATION_REASON_UNSPECIFIED\x10\x00\x12\x1e\n\x1aTERMINATION_REASON_OPTIMAL\x10\x01\x12!\n\x1dTERMINATION_REASON_INFEASIBLE\x10\x02\x12 \n\x1cTERMINATION_REASON_UNBOUNDED\x10\x03\x12.\n*TERMINATION_REASON_INFEASIBLE_OR_UNBOUNDED\x10\x04\x12 \n\x1cTERMINATION_REASON_IMPRECISE\x10\x05\x12\x1f\n\x1bTERMINATION_REASON_FEASIBLE\x10\t\x12(\n$TERMINATION_REASON_NO_SOLUTION_FOUND\x10\x06\x12&\n\"TERMINATION_REASON_NUMERICAL_ERROR\x10\x07\x12\"\n\x1eTERMINATION_REASON_OTHER_ERROR\x10\x08*\x8e\x02\n\nLimitProto\x12\x15\n\x11LIMIT_UNSPECIFIED\x10\x00\x12\x16\n\x12LIMIT_UNDETERMINED\x10\x01\x12\x13\n\x0fLIMIT_ITERATION\x10\x02\x12\x0e\n\nLIMIT_TIME\x10\x03\x12\x0e\n\nLIMIT_NODE\x10\x04\x12\x12\n\x0eLIMIT_SOLUTION\x10\x05\x12\x10\n\x0cLIMIT_MEMORY\x10\x06\x12\x10\n\x0cLIMIT_CUTOFF\x10\x0c\x12\x13\n\x0fLIMIT_OBJECTIVE\x10\x07\x12\x0e\n\nLIMIT_NORM\x10\x08\x12\x15\n\x11LIMIT_INTERRUPTED\x10\t\x12\x17\n\x13LIMIT_SLOW_PROGRESS\x10\n\x12\x0f\n\x0bLIMIT_OTHER\x10\x0b\x42\x1e\n\x1a\x63om.google.ortools.mathoptP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.result_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.ortools.mathoptP\001'
  _globals['_SOLVESTATSPROTO'].fields_by_name['best_primal_bound']._loaded_options = None
  _globals['_SOLVESTATSPROTO'].fields_by_name['best_primal_bound']._serialized_options = b'\030\001'
  _globals['_SOLVESTATSPROTO'].fields_by_name['best_dual_bound']._loaded_options = None
  _globals['_SOLVESTATSPROTO'].fields_by_name['best_dual_bound']._serialized_options = b'\030\001'
  _globals['_SOLVESTATSPROTO'].fields_by_name['problem_status']._loaded_options = None
  _globals['_SOLVESTATSPROTO'].fields_by_name['problem_status']._serialized_options = b'\030\001'
  _globals['_FEASIBILITYSTATUSPROTO']._serialized_start=1814
  _globals['_FEASIBILITYSTATUSPROTO']._serialized_end=1979
  _globals['_TERMINATIONREASONPROTO']._serialized_start=1982
  _globals['_TERMINATIONREASONPROTO']._serialized_end=2376
  _globals['_LIMITPROTO']._serialized_start=2379
  _globals['_LIMITPROTO']._serialized_end=2649
  _globals['_PROBLEMSTATUSPROTO']._serialized_start=223
  _globals['_PROBLEMSTATUSPROTO']._serialized_end=430
  _globals['_SOLVESTATSPROTO']._serialized_start=433
  _globals['_SOLVESTATSPROTO']._serialized_end=743
  _globals['_OBJECTIVEBOUNDSPROTO']._serialized_start=745
  _globals['_OBJECTIVEBOUNDSPROTO']._serialized_end=809
  _globals['_TERMINATIONPROTO']._serialized_start=812
  _globals['_TERMINATIONPROTO']._serialized_end=1125
  _globals['_SOLVERESULTPROTO']._serialized_start=1128
  _globals['_SOLVERESULTPROTO']._serialized_end=1811
  _globals['_SOLVERESULTPROTO_PDLPOUTPUT']._serialized_start=1684
  _globals['_SOLVERESULTPROTO_PDLPOUTPUT']._serialized_end=1779
# @@protoc_insertion_point(module_scope)
