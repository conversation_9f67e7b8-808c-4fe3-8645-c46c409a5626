import numpy
import scipy.sparse
from typing import Any, ClassVar, overload

ABNORMAL: SolveStatus
CANCELLED_BY_USER: SolveStatus
FEASIBLE: SolveStatus
INCOMPATIBLE_OPTIONS: SolveStatus
INFEASIBLE: SolveStatus
INVALID_SOLVER_PARAMETERS: SolveStatus
MODEL_INVALID: SolveStatus
MODEL_IS_VALID: SolveStatus
NOT_SOLVED: SolveStatus
OPTIMAL: SolveStatus
SOLVER_TYPE_UNAVAILABLE: SolveStatus
UNBOUNDED: SolveStatus
UNKNOWN_STATUS: SolveStatus

class AffineExpr(LinearExpr):
    def __init__(self, arg0: LinearExpr, arg1: float, arg2: float) -> None: ...
    @property
    def coefficient(self) -> float: ...
    @property
    def expression(self) -> LinearExpr: ...
    @property
    def offset(self) -> float: ...

class BoundedLinearExpression:
    @overload
    def __init__(self, arg0: LinearExpr, arg1: float, arg2: float) -> None: ...
    @overload
    def __init__(self, arg0: LinearExpr, arg1: LinearExpr, arg2: float, arg3: float) -> None: ...
    @overload
    def __init__(self, arg0: LinearExpr, arg1: int, arg2: int) -> None: ...
    @overload
    def __init__(self, arg0: LinearExpr, arg1: LinearExpr, arg2: int, arg3: int) -> None: ...
    def __bool__(self) -> bool: ...
    @property
    def coeffs(self) -> list[float]: ...
    @property
    def lower_bound(self) -> float: ...
    @property
    def upper_bound(self) -> float: ...
    @property
    def vars(self) -> list[Variable]: ...

class FlatExpr(LinearExpr):
    @overload
    def __init__(self, arg0: LinearExpr) -> None: ...
    @overload
    def __init__(self, arg0: LinearExpr, arg1: LinearExpr) -> None: ...
    @overload
    def __init__(self, arg0, arg1: list[float], arg2: float) -> None: ...
    @overload
    def __init__(self, arg0: float) -> None: ...
    def variable_indices(self) -> list[int]: ...
    @property
    def coeffs(self) -> list[float]: ...
    @property
    def offset(self) -> float: ...
    @property
    def vars(self): ...

class LinearExpr:
    def __init__(self, *args, **kwargs) -> None: ...
    @overload
    @staticmethod
    def affine(expr: LinearExpr, coeff: float, constant: float = ...) -> LinearExpr: ...
    @overload
    @staticmethod
    def affine(value: float, coeff: float, constant: float = ...) -> LinearExpr: ...
    @staticmethod
    def constant(value: float) -> LinearExpr: ...
    @overload
    @staticmethod
    def sum(expressions) -> Any: ...
    @overload
    @staticmethod
    def sum(*args, **kwargs) -> LinearExpr: ...
    @staticmethod
    def term(expr: LinearExpr, coeff: float) -> LinearExpr: ...
    @staticmethod
    def weighted_sum(expressions: Sequence, coefficients: list[float], constant: float = ...) -> LinearExpr: ...
    def __abs__(self) -> None: ...
    @overload
    def __add__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __add__(self, cst: float) -> LinearExpr: ...
    def __and__(self, arg0: object) -> None: ...
    def __bool__(self) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __floordiv__(self, arg0: object) -> None: ...
    def __ge__(self, other: object) -> bool: ...
    def __le__(self, other: object) -> bool: ...
    def __lshift__(self, arg0: object) -> None: ...
    def __mod__(self, arg0: object) -> None: ...
    def __mul__(self, cst: float) -> LinearExpr: ...
    def __neg__(self) -> LinearExpr: ...
    def __or__(self, arg0: object) -> None: ...
    def __pow__(self, arg0: object) -> None: ...
    def __radd__(self, cst: float) -> LinearExpr: ...
    def __rmul__(self, cst: float) -> LinearExpr: ...
    def __rshift__(self, arg0: object) -> None: ...
    def __rsub__(self, cst: float) -> LinearExpr: ...
    @overload
    def __sub__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __sub__(self, cst: float) -> LinearExpr: ...
    def __truediv__(self, arg0: float) -> LinearExpr: ...
    def __xor__(self, arg0: object) -> None: ...

class MPModelExportOptions:
    log_invalid_names: bool
    max_line_length: int
    obfuscate: bool
    show_unused_variables: bool
    def __init__(self) -> None: ...

class ModelBuilderHelper:
    def __init__(self) -> None: ...
    def add_enforced_linear_constraint(self) -> int: ...
    def add_hint(self, var_index: int, var_value: float) -> None: ...
    def add_linear_constraint(self) -> int: ...
    def add_term_to_constraint(self, ct_index: int, var_index: int, coeff: float) -> None: ...
    def add_term_to_enforced_constraint(self, ct_index: int, var_index: int, coeff: float) -> None: ...
    def add_terms_to_constraint(self, arg0: int, arg1: list[Variable], arg2: list[float]) -> None: ...
    def add_terms_to_enforced_constraint(self, arg0: int, arg1: list[Variable], arg2: list[float]) -> None: ...
    def add_var(self) -> int: ...
    def add_var_array(self, arg0: list[int], arg1: float, arg2: float, arg3: bool, arg4: str) -> numpy.ndarray[numpy.int32]: ...
    def add_var_array_with_bounds(self, arg0: numpy.ndarray[numpy.float64], arg1: numpy.ndarray[numpy.float64], arg2: numpy.ndarray[bool], arg3: str) -> numpy.ndarray[numpy.int32]: ...
    def clear_hints(self) -> None: ...
    def clear_objective(self) -> None: ...
    def constraint_coefficients(self, ct_index: int) -> list[float]: ...
    def constraint_lower_bound(self, ct_index: int) -> float: ...
    def constraint_name(self, ct_index: int) -> str: ...
    def constraint_upper_bound(self, ct_index: int) -> float: ...
    def constraint_var_indices(self, ct_index: int) -> list[int]: ...
    def enforced_constraint_coefficients(self, ct_index: int) -> list[float]: ...
    def enforced_constraint_indicator_value(self, ct_index: int) -> bool: ...
    def enforced_constraint_indicator_variable_index(self, ct_index: int) -> int: ...
    def enforced_constraint_lower_bound(self, ct_index: int) -> float: ...
    def enforced_constraint_name(self, ct_index: int) -> str: ...
    def enforced_constraint_upper_bound(self, ct_index: int) -> float: ...
    def enforced_constraint_var_indices(self, ct_index: int) -> list[int]: ...
    def export_to_lp_string(self, options: MPModelExportOptions = ...) -> str: ...
    def export_to_mps_string(self, options: MPModelExportOptions = ...) -> str: ...
    def fill_model_from_sparse_data(self, variable_lower_bound: numpy.ndarray[numpy.float64[m, 1]], variable_upper_bound: numpy.ndarray[numpy.float64[m, 1]], objective_coefficients: numpy.ndarray[numpy.float64[m, 1]], constraint_lower_bounds: numpy.ndarray[numpy.float64[m, 1]], constraint_upper_bounds: numpy.ndarray[numpy.float64[m, 1]], constraint_matrix: scipy.sparse.csr_matrix[numpy.float64]) -> None: ...
    def import_from_lp_file(self, lp_file: str) -> bool: ...
    def import_from_lp_string(self, lp_string: str) -> bool: ...
    def import_from_mps_file(self, mps_file: str) -> bool: ...
    def import_from_mps_string(self, mps_string: str) -> bool: ...
    def is_enforced_linear_constraint(self, arg0: int) -> bool: ...
    def maximize(self) -> bool: ...
    def name(self) -> str: ...
    def num_constraints(self) -> int: ...
    def num_variables(self) -> int: ...
    def objective_offset(self) -> float: ...
    def overwrite_model(self, other_helper: ModelBuilderHelper) -> None: ...
    def read_model_from_proto_file(self, filename: str) -> bool: ...
    def safe_add_term_to_constraint(self, ct_index: int, var_index: int, coeff: float) -> None: ...
    def safe_add_term_to_enforced_constraint(self, ct_index: int, var_index: int, coeff: float) -> None: ...
    def set_constraint_coefficient(self, ct_index: int, var_index: int, coeff: float) -> None: ...
    def set_constraint_lower_bound(self, ct_index: int, lb: float) -> None: ...
    def set_constraint_name(self, ct_index: int, name: str) -> None: ...
    def set_constraint_upper_bound(self, ct_index: int, ub: float) -> None: ...
    def set_enforced_constraint_coefficient(self, ct_index: int, var_index: int, coeff: float) -> None: ...
    def set_enforced_constraint_indicator_value(self, ct_index: int, positive: bool) -> None: ...
    def set_enforced_constraint_indicator_variable_index(self, ct_index: int, var_index: int) -> None: ...
    def set_enforced_constraint_lower_bound(self, ct_index: int, lb: float) -> None: ...
    def set_enforced_constraint_name(self, ct_index: int, name: str) -> None: ...
    def set_enforced_constraint_upper_bound(self, ct_index: int, ub: float) -> None: ...
    def set_maximize(self, maximize: bool) -> None: ...
    def set_name(self, name: str) -> None: ...
    def set_objective_coefficients(self, arg0: list[int], arg1: list[float]) -> None: ...
    def set_objective_offset(self, offset: float) -> None: ...
    def set_var_integrality(self, var_index: int, is_integer: bool) -> None: ...
    def set_var_lower_bound(self, var_index: int, lb: float) -> None: ...
    def set_var_name(self, var_index: int, name: str) -> None: ...
    def set_var_objective_coefficient(self, var_index: int, coeff: float) -> None: ...
    def set_var_upper_bound(self, var_index: int, ub: float) -> None: ...
    def var_is_integral(self, var_index: int) -> bool: ...
    def var_lower_bound(self, var_index: int) -> float: ...
    def var_name(self, var_index: int) -> str: ...
    def var_objective_coefficient(self, var_index: int) -> float: ...
    def var_upper_bound(self, var_index: int) -> float: ...
    def write_model_to_proto_file(self, filename: str) -> bool: ...
    def write_to_mps_file(self, filename: str, options: MPModelExportOptions = ...) -> bool: ...

class ModelSolverHelper:
    def __init__(self, arg0: str) -> None: ...
    def activity(self, ct_index: int) -> float: ...
    def best_objective_bound(self) -> float: ...
    def clear_log_callback(self) -> None: ...
    def dual_value(self, ct_index: int) -> float: ...
    def dual_values(self) -> numpy.ndarray[numpy.float64[m, 1]]: ...
    def enable_output(self, output: bool) -> None: ...
    def expression_value(self, arg0: LinearExpr) -> float: ...
    def has_response(self) -> bool: ...
    def has_solution(self) -> bool: ...
    def interrupt_solve(self) -> bool: ...
    def objective_value(self) -> float: ...
    def reduced_cost(self, var_index: int) -> float: ...
    def reduced_costs(self) -> numpy.ndarray[numpy.float64[m, 1]]: ...
    def response(self, *args, **kwargs): ...
    def set_log_callback(self, arg0) -> None: ...
    def set_solver_specific_parameters(self, solver_specific_parameters: str) -> None: ...
    def set_time_limit_in_seconds(self, limit: float) -> None: ...
    def solve(self, model: ModelBuilderHelper) -> None: ...
    def solve_serialized_request(self, arg0: str) -> bytes: ...
    def solver_is_supported(self) -> bool: ...
    def status(self) -> SolveStatus: ...
    def status_string(self) -> str: ...
    def user_time(self) -> float: ...
    def variable_value(self, var_index: int) -> float: ...
    def variable_values(self) -> numpy.ndarray[numpy.float64[m, 1]]: ...
    def wall_time(self) -> float: ...

class SolveStatus:
    __members__: ClassVar[dict] = ...  # read-only
    ABNORMAL: ClassVar[SolveStatus] = ...
    CANCELLED_BY_USER: ClassVar[SolveStatus] = ...
    FEASIBLE: ClassVar[SolveStatus] = ...
    INCOMPATIBLE_OPTIONS: ClassVar[SolveStatus] = ...
    INFEASIBLE: ClassVar[SolveStatus] = ...
    INVALID_SOLVER_PARAMETERS: ClassVar[SolveStatus] = ...
    MODEL_INVALID: ClassVar[SolveStatus] = ...
    MODEL_IS_VALID: ClassVar[SolveStatus] = ...
    NOT_SOLVED: ClassVar[SolveStatus] = ...
    OPTIMAL: ClassVar[SolveStatus] = ...
    SOLVER_TYPE_UNAVAILABLE: ClassVar[SolveStatus] = ...
    UNBOUNDED: ClassVar[SolveStatus] = ...
    UNKNOWN_STATUS: ClassVar[SolveStatus] = ...
    __entries: ClassVar[dict] = ...
    def __init__(self, value: int) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __index__(self) -> int: ...
    def __int__(self) -> int: ...
    def __ne__(self, other: object) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def value(self) -> int: ...

class SumArray(LinearExpr):
    def __init__(self, arg0: list[LinearExpr], arg1: float) -> None: ...
    @overload
    def __add__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __add__(self, cst: float) -> LinearExpr: ...
    @overload
    def __iadd__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __iadd__(self, cst: float) -> LinearExpr: ...
    @overload
    def __isub__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __isub__(self, cst: float) -> LinearExpr: ...
    @overload
    def __radd__(self, cst: LinearExpr) -> LinearExpr: ...
    @overload
    def __radd__(self, cst: float) -> LinearExpr: ...
    @overload
    def __sub__(self, other: LinearExpr) -> LinearExpr: ...
    @overload
    def __sub__(self, cst: float) -> LinearExpr: ...
    @property
    def num_exprs(self) -> int: ...
    @property
    def offset(self) -> float: ...

class Variable(LinearExpr):
    is_integral: bool
    lower_bound: float
    name: str
    objective_coefficient: float
    upper_bound: float
    @overload
    def __init__(self, arg0, arg1: int) -> None: ...
    @overload
    def __init__(self, arg0, arg1: float, arg2: float, arg3: bool) -> None: ...
    @overload
    def __init__(self, arg0, arg1: float, arg2: float, arg3: bool, arg4: str) -> None: ...
    @overload
    def __init__(self, arg0, arg1: int, arg2: int, arg3: bool) -> None: ...
    @overload
    def __init__(self, arg0, arg1: int, arg2: int, arg3: bool, arg4: str) -> None: ...
    def __hash__(self) -> int: ...
    @property
    def helper(self): ...
    @property
    def index(self) -> int: ...

def to_mpmodel_proto(*args, **kwargs): ...
