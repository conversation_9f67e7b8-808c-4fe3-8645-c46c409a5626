# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/constraint_solver/search_stats.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/constraint_solver/search_stats.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n,ortools/constraint_solver/search_stats.proto\x12\x13operations_research\"\xcb\x07\n\x15LocalSearchStatistics\x12\x65\n\x19\x66irst_solution_statistics\x18\x06 \x03(\x0b\x32\x42.operations_research.LocalSearchStatistics.FirstSolutionStatistics\x12r\n local_search_operator_statistics\x18\x01 \x03(\x0b\x32H.operations_research.LocalSearchStatistics.LocalSearchOperatorStatistics\x12\x1b\n\x13total_num_neighbors\x18\x03 \x01(\x03\x12$\n\x1ctotal_num_filtered_neighbors\x18\x04 \x01(\x03\x12$\n\x1ctotal_num_accepted_neighbors\x18\x05 \x01(\x03\x12n\n\x1elocal_search_filter_statistics\x18\x02 \x03(\x0b\x32\x46.operations_research.LocalSearchStatistics.LocalSearchFilterStatistics\x1a\x45\n\x17\x46irstSolutionStatistics\x12\x10\n\x08strategy\x18\x01 \x01(\t\x12\x18\n\x10\x64uration_seconds\x18\x02 \x01(\x01\x1a\x86\x02\n\x1dLocalSearchOperatorStatistics\x12\x1d\n\x15local_search_operator\x18\x01 \x01(\t\x12\x15\n\rnum_neighbors\x18\x02 \x01(\x03\x12\x1e\n\x16num_filtered_neighbors\x18\x03 \x01(\x03\x12\x1e\n\x16num_accepted_neighbors\x18\x04 \x01(\x03\x12\x18\n\x10\x64uration_seconds\x18\x05 \x01(\x01\x12+\n#make_next_neighbor_duration_seconds\x18\x06 \x01(\x01\x12(\n accept_neighbor_duration_seconds\x18\x07 \x01(\x01\x1a\xad\x01\n\x1bLocalSearchFilterStatistics\x12\x1b\n\x13local_search_filter\x18\x01 \x01(\t\x12\x11\n\tnum_calls\x18\x02 \x01(\x03\x12\x13\n\x0bnum_rejects\x18\x03 \x01(\x03\x12\x18\n\x10\x64uration_seconds\x18\x04 \x01(\x01\x12\x1e\n\x16num_rejects_per_second\x18\x05 \x01(\x01\x12\x0f\n\x07\x63ontext\x18\x06 \x01(\t\"\x8d\x01\n\x1a\x43onstraintSolverStatistics\x12\x14\n\x0cnum_branches\x18\x01 \x01(\x03\x12\x14\n\x0cnum_failures\x18\x02 \x01(\x03\x12\x15\n\rnum_solutions\x18\x03 \x01(\x03\x12\x12\n\nbytes_used\x18\x04 \x01(\x03\x12\x18\n\x10\x64uration_seconds\x18\x05 \x01(\x01\"\xb6\x01\n\x10SearchStatistics\x12K\n\x17local_search_statistics\x18\x01 \x03(\x0b\x32*.operations_research.LocalSearchStatistics\x12U\n\x1c\x63onstraint_solver_statistics\x18\x02 \x03(\x0b\x32/.operations_research.ConstraintSolverStatisticsBI\n#com.google.ortools.constraintsolverP\x01\xaa\x02\x1fGoogle.OrTools.ConstraintSolverb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.constraint_solver.search_stats_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.google.ortools.constraintsolverP\001\252\002\037Google.OrTools.ConstraintSolver'
  _globals['_LOCALSEARCHSTATISTICS']._serialized_start=70
  _globals['_LOCALSEARCHSTATISTICS']._serialized_end=1041
  _globals['_LOCALSEARCHSTATISTICS_FIRSTSOLUTIONSTATISTICS']._serialized_start=531
  _globals['_LOCALSEARCHSTATISTICS_FIRSTSOLUTIONSTATISTICS']._serialized_end=600
  _globals['_LOCALSEARCHSTATISTICS_LOCALSEARCHOPERATORSTATISTICS']._serialized_start=603
  _globals['_LOCALSEARCHSTATISTICS_LOCALSEARCHOPERATORSTATISTICS']._serialized_end=865
  _globals['_LOCALSEARCHSTATISTICS_LOCALSEARCHFILTERSTATISTICS']._serialized_start=868
  _globals['_LOCALSEARCHSTATISTICS_LOCALSEARCHFILTERSTATISTICS']._serialized_end=1041
  _globals['_CONSTRAINTSOLVERSTATISTICS']._serialized_start=1044
  _globals['_CONSTRAINTSOLVERSTATISTICS']._serialized_end=1185
  _globals['_SEARCHSTATISTICS']._serialized_start=1188
  _globals['_SEARCHSTATISTICS']._serialized_end=1370
# @@protoc_insertion_point(module_scope)
