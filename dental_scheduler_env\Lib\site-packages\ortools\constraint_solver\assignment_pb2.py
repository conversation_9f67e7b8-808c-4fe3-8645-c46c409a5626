# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/constraint_solver/assignment.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/constraint_solver/assignment.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*ortools/constraint_solver/assignment.proto\x12\x13operations_research\"L\n\x10IntVarAssignment\x12\x0e\n\x06var_id\x18\x01 \x01(\t\x12\x0b\n\x03min\x18\x02 \x01(\x03\x12\x0b\n\x03max\x18\x03 \x01(\x03\x12\x0e\n\x06\x61\x63tive\x18\x04 \x01(\x08\"\xd9\x01\n\x15IntervalVarAssignment\x12\x0e\n\x06var_id\x18\x01 \x01(\t\x12\x11\n\tstart_min\x18\x02 \x01(\x03\x12\x11\n\tstart_max\x18\x03 \x01(\x03\x12\x14\n\x0c\x64uration_min\x18\x04 \x01(\x03\x12\x14\n\x0c\x64uration_max\x18\x05 \x01(\x03\x12\x0f\n\x07\x65nd_min\x18\x06 \x01(\x03\x12\x0f\n\x07\x65nd_max\x18\x07 \x01(\x03\x12\x15\n\rperformed_min\x18\x08 \x01(\x03\x12\x15\n\rperformed_max\x18\t \x01(\x03\x12\x0e\n\x06\x61\x63tive\x18\n \x01(\x08\"\x81\x01\n\x15SequenceVarAssignment\x12\x0e\n\x06var_id\x18\x01 \x01(\t\x12\x18\n\x10\x66orward_sequence\x18\x02 \x03(\x05\x12\x19\n\x11\x62\x61\x63kward_sequence\x18\x03 \x03(\x05\x12\x13\n\x0bunperformed\x18\x04 \x03(\x05\x12\x0e\n\x06\x61\x63tive\x18\x05 \x01(\x08\",\n\nWorkerInfo\x12\x11\n\tworker_id\x18\x01 \x01(\x05\x12\x0b\n\x03\x62ns\x18\x02 \x01(\t\"\xf0\x02\n\x0f\x41ssignmentProto\x12\x41\n\x12int_var_assignment\x18\x01 \x03(\x0b\x32%.operations_research.IntVarAssignment\x12K\n\x17interval_var_assignment\x18\x02 \x03(\x0b\x32*.operations_research.IntervalVarAssignment\x12K\n\x17sequence_var_assignment\x18\x06 \x03(\x0b\x32*.operations_research.SequenceVarAssignment\x12\x38\n\tobjective\x18\x03 \x03(\x0b\x32%.operations_research.IntVarAssignment\x12\x34\n\x0bworker_info\x18\x04 \x01(\x0b\x32\x1f.operations_research.WorkerInfo\x12\x10\n\x08is_valid\x18\x05 \x01(\x08\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.constraint_solver.assignment_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_INTVARASSIGNMENT']._serialized_start=67
  _globals['_INTVARASSIGNMENT']._serialized_end=143
  _globals['_INTERVALVARASSIGNMENT']._serialized_start=146
  _globals['_INTERVALVARASSIGNMENT']._serialized_end=363
  _globals['_SEQUENCEVARASSIGNMENT']._serialized_start=366
  _globals['_SEQUENCEVARASSIGNMENT']._serialized_end=495
  _globals['_WORKERINFO']._serialized_start=497
  _globals['_WORKERINFO']._serialized_end=541
  _globals['_ASSIGNMENTPROTO']._serialized_start=544
  _globals['_ASSIGNMENTPROTO']._serialized_end=912
# @@protoc_insertion_point(module_scope)
