"""Team-based job scheduling with employee hour constraints."""
import collections
from ortools.sat.python import cp_model



def main() -> None:
    """Team-based job scheduling problem."""
    # Team and employee data
    teams = {
        'red': ['<PERSON>', '<PERSON>'],
        'blue': ['<PERSON>', '<PERSON>', '<PERSON>'],
        'green': ['<PERSON>', '<PERSON>']
    }

    # Create employee to ID mapping
    all_employees = []
    employee_to_id = {}
    employee_to_team = {}

    for team_name, employees in teams.items():
        for employee in employees:
            if employee not in employee_to_id:  # Handle Bob being in multiple teams
                employee_id = len(all_employees)
                all_employees.append(employee)
                employee_to_id[employee] = employee_id
                employee_to_team[employee] = [team_name]
            else:
                employee_to_team[employee].append(team_name)

    # Job data - 10 jobs with tasks assigned to teams (team_id, processing_time)
    # Team IDs: red=0, blue=1, green=2
    jobs_data = [  # task = (team_id, processing_time).
        [(0, 2), (1, 3), (2, 1)],  # Job0
        [(1, 4), (2, 2), (0, 3)],  # Job1
        [(2, 3), (0, 1), (1, 2)],  # Job2
        [(0, 3), (1, 4), (2, 2)],  # Job3
        [(1, 2), (0, 3), (2, 4)],  # Job4
        [(2, 1), (1, 3), (0, 2)],  # Job5
        [(0, 4), (2, 2), (1, 3)],  # Job6
        [(1, 3), (0, 2), (2, 1)],  # Job7
        [(2, 2), (0, 4), (1, 3)],  # Job8
        [(0, 1), (1, 2), (2, 3)],  # Job9
    ]

    team_names = ['red', 'blue', 'green']

    # Computes horizon dynamically as the sum of all durations.
    horizon = sum(task[1] for job in jobs_data for task in job)

    # Daily work limit (8 hours = 8 time units)
    daily_work_limit = 8

    # Create the model.
    model = cp_model.CpModel()

    # Named tuple to store information about created variables.
    task_type = collections.namedtuple("task_type", "start end interval employee")
    # Named tuple to manipulate solution information.
    assigned_task_type = collections.namedtuple(
        "assigned_task_type", "start job index duration employee"
    )

    # Creates job intervals and employee assignment variables
    all_tasks = {}
    employee_to_intervals = collections.defaultdict(list)

    for job_id, job in enumerate(jobs_data):
        for task_id, task in enumerate(job):
            team_id, duration = task
            suffix = f"_{job_id}_{task_id}"

            # Create time variables
            start_var = model.new_int_var(0, horizon, "start" + suffix)
            end_var = model.new_int_var(0, horizon, "end" + suffix)
            interval_var = model.new_interval_var(
                start_var, duration, end_var, "interval" + suffix
            )

            # Create employee assignment variable for this team
            team_name = team_names[team_id]
            available_employees = [employee_to_id[emp] for emp in teams[team_name]]
            employee_var = model.new_int_var_from_domain(
                cp_model.Domain.from_values(available_employees),
                "employee" + suffix
            )

            all_tasks[job_id, task_id] = task_type(
                start=start_var, end=end_var, interval=interval_var, employee=employee_var
            )

            # Add interval to each possible employee (will be filtered by constraints)
            for emp_id in available_employees:
                employee_to_intervals[emp_id].append((interval_var, employee_var, emp_id))

    # Employee work hour constraints (8 hours per day)
    for emp_id in range(len(all_employees)):
        # Collect all tasks that could be assigned to this employee
        employee_work_vars = []

        for job_id, job in enumerate(jobs_data):
            for task_id, task in enumerate(job):
                team_id, duration = task
                team_name = team_names[team_id]

                # Check if this employee can work on this team's tasks
                if all_employees[emp_id] in teams[team_name]:
                    # Create boolean variable for assignment
                    is_assigned = model.new_bool_var(f"emp_{emp_id}_job_{job_id}_task_{task_id}")

                    # Link assignment to employee variable
                    model.add(all_tasks[job_id, task_id].employee == emp_id).only_enforce_if(is_assigned)

                    # Create work contribution variable
                    work_var = model.new_int_var(0, duration, f"work_emp_{emp_id}_job_{job_id}_task_{task_id}")
                    model.add(work_var == duration).only_enforce_if(is_assigned)
                    model.add(work_var == 0).only_enforce_if(is_assigned.Not())

                    employee_work_vars.append(work_var)

        # Constraint: total work for this employee <= 8 hours
        if employee_work_vars:
            model.add(sum(employee_work_vars) <= daily_work_limit)

    # Precedences inside a job.
    for job_id, job in enumerate(jobs_data):
        for task_id in range(len(job) - 1):
            model.add(
                all_tasks[job_id, task_id + 1].start >= all_tasks[job_id, task_id].end
            )

    # Makespan objective.
    obj_var = model.new_int_var(0, horizon, "makespan")
    model.add_max_equality(
        obj_var,
        [all_tasks[job_id, len(job) - 1].end for job_id, job in enumerate(jobs_data)],
    )
    model.minimize(obj_var)

    # Creates the solver and solve.
    solver = cp_model.CpSolver()
    status = solver.solve(model)

    if status == cp_model.OPTIMAL or status == cp_model.FEASIBLE:
        print("Solution:")
        # Create one list of assigned tasks per employee.
        assigned_jobs = collections.defaultdict(list)
        for job_id, job in enumerate(jobs_data):
            for task_id, task in enumerate(job):
                employee_id = solver.value(all_tasks[job_id, task_id].employee)
                assigned_jobs[employee_id].append(
                    assigned_task_type(
                        start=solver.value(all_tasks[job_id, task_id].start),
                        job=job_id,
                        index=task_id,
                        duration=task[1],
                        employee=employee_id,
                    )
                )

        # Create per employee output lines.
        output = ""
        for emp_id in range(len(all_employees)):
            if emp_id in assigned_jobs:
                # Sort by starting time.
                assigned_jobs[emp_id].sort()
                employee_name = all_employees[emp_id]
                teams_list = ", ".join(employee_to_team[employee_name])
                sol_line_tasks = f"Employee {employee_name} ({teams_list}): "
                sol_line = " " * len(sol_line_tasks)

                total_hours = 0
                for assigned_task in assigned_jobs[emp_id]:
                    name = f"job_{assigned_task.job}_task_{assigned_task.index}"
                    # add spaces to output to align columns.
                    sol_line_tasks += f"{name:15}"

                    start = assigned_task.start
                    duration = assigned_task.duration
                    total_hours += duration
                    sol_tmp = f"[{start},{start + duration}]"
                    # add spaces to output to align columns.
                    sol_line += f"{sol_tmp:15}"

                sol_line += f" (Total: {total_hours}h)\n"
                sol_line_tasks += "\n"
                output += sol_line_tasks
                output += sol_line

        # Finally print the solution found.
        print(f"Optimal Schedule Length: {solver.objective_value}")
        print(output)
    else:
        print("No solution found.")

    # Statistics.
    print("\nStatistics")
    print(f"  - conflicts: {solver.num_conflicts}")
    print(f"  - branches : {solver.num_branches}")
    print(f"  - wall time: {solver.wall_time}s")


if __name__ == "__main__":
    main()
    