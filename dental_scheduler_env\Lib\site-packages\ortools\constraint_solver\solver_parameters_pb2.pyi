"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
This file contains protocol buffers for all parameters of the CP solver."""

import builtins
import google.protobuf.descriptor
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class ConstraintSolverParameters(google.protobuf.message.Message):
    """Solver parameters."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _TrailCompression:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _TrailCompressionEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[ConstraintSolverParameters._TrailCompression.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        NO_COMPRESSION: ConstraintSolverParameters._TrailCompression.ValueType  # 0
        COMPRESS_WITH_ZLIB: ConstraintSolverParameters._TrailCompression.ValueType  # 1

    class TrailCompression(_TrailCompression, metaclass=_TrailCompressionEnumTypeWrapper):
        """
        Internal parameters of the solver.
        """

    NO_COMPRESSION: ConstraintSolverParameters.TrailCompression.ValueType  # 0
    COMPRESS_WITH_ZLIB: ConstraintSolverParameters.TrailCompression.ValueType  # 1

    COMPRESS_TRAIL_FIELD_NUMBER: builtins.int
    TRAIL_BLOCK_SIZE_FIELD_NUMBER: builtins.int
    ARRAY_SPLIT_SIZE_FIELD_NUMBER: builtins.int
    STORE_NAMES_FIELD_NUMBER: builtins.int
    NAME_CAST_VARIABLES_FIELD_NUMBER: builtins.int
    NAME_ALL_VARIABLES_FIELD_NUMBER: builtins.int
    PROFILE_PROPAGATION_FIELD_NUMBER: builtins.int
    PROFILE_FILE_FIELD_NUMBER: builtins.int
    PROFILE_LOCAL_SEARCH_FIELD_NUMBER: builtins.int
    PRINT_LOCAL_SEARCH_PROFILE_FIELD_NUMBER: builtins.int
    TRACE_PROPAGATION_FIELD_NUMBER: builtins.int
    TRACE_SEARCH_FIELD_NUMBER: builtins.int
    PRINT_MODEL_FIELD_NUMBER: builtins.int
    PRINT_MODEL_STATS_FIELD_NUMBER: builtins.int
    PRINT_ADDED_CONSTRAINTS_FIELD_NUMBER: builtins.int
    DISABLE_SOLVE_FIELD_NUMBER: builtins.int
    USE_SMALL_TABLE_FIELD_NUMBER: builtins.int
    USE_CUMULATIVE_EDGE_FINDER_FIELD_NUMBER: builtins.int
    USE_CUMULATIVE_TIME_TABLE_FIELD_NUMBER: builtins.int
    USE_CUMULATIVE_TIME_TABLE_SYNC_FIELD_NUMBER: builtins.int
    USE_SEQUENCE_HIGH_DEMAND_TASKS_FIELD_NUMBER: builtins.int
    USE_ALL_POSSIBLE_DISJUNCTIONS_FIELD_NUMBER: builtins.int
    MAX_EDGE_FINDER_SIZE_FIELD_NUMBER: builtins.int
    DIFFN_USE_CUMULATIVE_FIELD_NUMBER: builtins.int
    USE_ELEMENT_RMQ_FIELD_NUMBER: builtins.int
    SKIP_LOCALLY_OPTIMAL_PATHS_FIELD_NUMBER: builtins.int
    CHECK_SOLUTION_PERIOD_FIELD_NUMBER: builtins.int
    compress_trail: global___ConstraintSolverParameters.TrailCompression.ValueType
    """This parameter indicates if the solver should compress the trail
    during the search. No compression means that the solver will be faster,
    but will use more memory.
    """
    trail_block_size: builtins.int
    """This parameter indicates the default size of a block of the trail.
    Compression applies at the block level.
    """
    array_split_size: builtins.int
    """When a sum/min/max operation is applied on a large array, this
    array is recursively split into blocks of size 'array_split_size'.
    """
    store_names: builtins.bool
    """
    Control naming of the variables..

    This parameters indicates if the solver should store the names of
    the objets it manages.
    """
    name_cast_variables: builtins.bool
    """Create names for cast variables."""
    name_all_variables: builtins.bool
    """Should anonymous variables be given a name."""
    profile_propagation: builtins.bool
    """
    Control monitoring of the solver and the model.

    Activate propagation profiling.
    """
    profile_file: builtins.str
    """Export propagation profiling data to file."""
    profile_local_search: builtins.bool
    """Activate local search profiling."""
    print_local_search_profile: builtins.bool
    """Print local search profiling data after solving."""
    trace_propagation: builtins.bool
    """Activate propagate tracing."""
    trace_search: builtins.bool
    """Trace search."""
    print_model: builtins.bool
    """Print the model before solving."""
    print_model_stats: builtins.bool
    """Print model statistics before solving."""
    print_added_constraints: builtins.bool
    """Print added constraints."""
    disable_solve: builtins.bool
    """Disable solving."""
    use_small_table: builtins.bool
    """The following flags are meant for internal use only.


    Control the implementation of the table constraint.
    """
    use_cumulative_edge_finder: builtins.bool
    """
    Control the propagation of the cumulative constraint.
    """
    use_cumulative_time_table: builtins.bool
    use_cumulative_time_table_sync: builtins.bool
    use_sequence_high_demand_tasks: builtins.bool
    use_all_possible_disjunctions: builtins.bool
    max_edge_finder_size: builtins.int
    diffn_use_cumulative: builtins.bool
    """
    Control the propagation of the diffn constraint.
    """
    use_element_rmq: builtins.bool
    """
    Control the implementation of the element constraint.
    """
    skip_locally_optimal_paths: builtins.bool
    """
    Skip locally optimal pairs of paths in PathOperators. Setting this
    parameter to true might skip valid neighbors if there are constraints
    linking paths together (such as precedences). In any other case this
    should only speed up the search without omitting any neighbors.
    """
    check_solution_period: builtins.int
    """
    Control the behavior of local search.
    """
    def __init__(
        self,
        *,
        compress_trail: global___ConstraintSolverParameters.TrailCompression.ValueType = ...,
        trail_block_size: builtins.int = ...,
        array_split_size: builtins.int = ...,
        store_names: builtins.bool = ...,
        name_cast_variables: builtins.bool = ...,
        name_all_variables: builtins.bool = ...,
        profile_propagation: builtins.bool = ...,
        profile_file: builtins.str = ...,
        profile_local_search: builtins.bool = ...,
        print_local_search_profile: builtins.bool = ...,
        trace_propagation: builtins.bool = ...,
        trace_search: builtins.bool = ...,
        print_model: builtins.bool = ...,
        print_model_stats: builtins.bool = ...,
        print_added_constraints: builtins.bool = ...,
        disable_solve: builtins.bool = ...,
        use_small_table: builtins.bool = ...,
        use_cumulative_edge_finder: builtins.bool = ...,
        use_cumulative_time_table: builtins.bool = ...,
        use_cumulative_time_table_sync: builtins.bool = ...,
        use_sequence_high_demand_tasks: builtins.bool = ...,
        use_all_possible_disjunctions: builtins.bool = ...,
        max_edge_finder_size: builtins.int = ...,
        diffn_use_cumulative: builtins.bool = ...,
        use_element_rmq: builtins.bool = ...,
        skip_locally_optimal_paths: builtins.bool = ...,
        check_solution_period: builtins.int = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["array_split_size", b"array_split_size", "check_solution_period", b"check_solution_period", "compress_trail", b"compress_trail", "diffn_use_cumulative", b"diffn_use_cumulative", "disable_solve", b"disable_solve", "max_edge_finder_size", b"max_edge_finder_size", "name_all_variables", b"name_all_variables", "name_cast_variables", b"name_cast_variables", "print_added_constraints", b"print_added_constraints", "print_local_search_profile", b"print_local_search_profile", "print_model", b"print_model", "print_model_stats", b"print_model_stats", "profile_file", b"profile_file", "profile_local_search", b"profile_local_search", "profile_propagation", b"profile_propagation", "skip_locally_optimal_paths", b"skip_locally_optimal_paths", "store_names", b"store_names", "trace_propagation", b"trace_propagation", "trace_search", b"trace_search", "trail_block_size", b"trail_block_size", "use_all_possible_disjunctions", b"use_all_possible_disjunctions", "use_cumulative_edge_finder", b"use_cumulative_edge_finder", "use_cumulative_time_table", b"use_cumulative_time_table", "use_cumulative_time_table_sync", b"use_cumulative_time_table_sync", "use_element_rmq", b"use_element_rmq", "use_sequence_high_demand_tasks", b"use_sequence_high_demand_tasks", "use_small_table", b"use_small_table"]) -> None: ...

global___ConstraintSolverParameters = ConstraintSolverParameters
