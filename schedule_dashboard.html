
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dental Implant Manufacturing Schedule Dashboard</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .nav-tabs {
            display: flex;
            background: white;
            border-radius: 10px 10px 0 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            border-right: 1px solid #dee2e6;
        }
        
        .nav-tab:last-child {
            border-right: none;
        }
        
        .nav-tab.active {
            background: white;
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }
        
        .nav-tab:hover {
            background: #e9ecef;
        }
        
        .nav-tab.active:hover {
            background: white;
        }
        
        .content-area {
            background: white;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            min-height: 600px;
        }
        
        .tab-content {
            display: none;
            padding: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .viz-frame {
            width: 100%;
            height: 800px;
            border: none;
            border-radius: 5px;
        }
        
        .info-panel {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 5px 5px 0;
        }
        
        .info-panel h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .info-panel p {
            margin: 5px 0;
            color: #424242;
        }
        
        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 20px;
            color: #666;
            font-size: 14px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🦷 Dental Implant Manufacturing</h1>
        <p>Production Schedule Dashboard - Generated on July 16, 2025 at 05:45 PM</p>
    </div>
    
    <div class="nav-tabs">
        <button class="nav-tab active" onclick="showTab('employee')">👥 By Employee</button>
        <button class="nav-tab" onclick="showTab('job')">📋 By Job</button>
        <button class="nav-tab" onclick="showTab('workload')">📊 Team Workload</button>
        <button class="nav-tab" onclick="showTab('dashboard')">📈 Dashboard</button>
    </div>
    
    <div class="content-area">
        <div id="employee" class="tab-content active">
            <div class="info-panel">
                <h3>Employee Schedule View</h3>
                <p>Shows the daily schedule for each employee across all teams. Each bar represents a task with job details and duration.</p>
                <p><strong>Teams:</strong> Red (Design & CAD), Blue (Manufacturing), Green (Quality Control)</p>
            </div>
            <iframe src="gantt_by_employee.html" class="viz-frame"></iframe>
        </div>
        
        <div id="job" class="tab-content">
            <div class="info-panel">
                <h3>Job Progress View</h3>
                <p>Tracks each job through its complete workflow from design to delivery. Shows the sequential Red → Blue → Green process.</p>
                <p><strong>Job Types:</strong> Standard Implant, Custom Implant, Abutment Set</p>
            </div>
            <iframe src="gantt_by_job.html" class="viz-frame"></iframe>
        </div>
        
        <div id="workload" class="tab-content">
            <div class="info-panel">
                <h3>Team Workload Distribution</h3>
                <p>Daily workload comparison across teams to identify bottlenecks and optimize resource allocation.</p>
                <p><strong>Capacity:</strong> Red: 16h/day, Blue: 24h/day, Green: 16h/day</p>
            </div>
            <iframe src="team_workload.html" class="viz-frame"></iframe>
        </div>
        
        <div id="dashboard" class="tab-content">
            <div class="info-panel">
                <h3>Executive Dashboard</h3>
                <p>High-level overview of job priorities, types, completion timeline, and team utilization metrics.</p>
                <p><strong>Key Metrics:</strong> Priority distribution, job types, completion status, resource utilization</p>
            </div>
            <iframe src="job_priority_dashboard.html" class="viz-frame"></iframe>
        </div>
    </div>
    
    <div class="footer">
        <p>Dental Implant Manufacturing Schedule System | Powered by OR-Tools & Plotly</p>
        <p>📧 For support or questions, contact your system administrator</p>
    </div>
    
    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
        
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Dashboard loaded successfully!');
            
            // Add keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key >= '1' && e.key <= '4') {
                    const tabs = ['employee', 'job', 'workload', 'dashboard'];
                    const tabIndex = parseInt(e.key) - 1;
                    if (tabIndex < tabs.length) {
                        showTab(tabs[tabIndex]);
                        document.querySelectorAll('.nav-tab')[tabIndex].classList.add('active');
                    }
                }
            });
        });
    </script>
</body>
</html>
