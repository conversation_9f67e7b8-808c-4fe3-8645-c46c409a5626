import numpy
from typing import ClassVar

class SimpleMaxFlow:
    class Status:
        __members__: ClassVar[dict] = ...  # read-only
        BAD_INPUT: ClassVar[SimpleMaxFlow.Status] = ...
        BAD_RESULT: ClassVar[SimpleMaxFlow.Status] = ...
        OPTIMAL: ClassVar[SimpleMaxFlow.Status] = ...
        POSSIBLE_OVERFLOW: ClassVar[SimpleMaxFlow.Status] = ...
        __entries: ClassVar[dict] = ...
        def __init__(self, value: int) -> None: ...
        def __eq__(self, other: object) -> bool: ...
        def __hash__(self) -> int: ...
        def __index__(self) -> int: ...
        def __int__(self) -> int: ...
        def __ne__(self, other: object) -> bool: ...
        @property
        def name(self) -> str: ...
        @property
        def value(self) -> int: ...
    BAD_INPUT: ClassVar[SimpleMaxFlow.Status] = ...
    BAD_RESULT: ClassVar[SimpleMaxFlow.Status] = ...
    OPTIMAL: ClassVar[SimpleMaxFlow.Status] = ...
    POSSIBLE_OVERFLOW: ClassVar[SimpleMaxFlow.Status] = ...
    def __init__(self) -> None: ...
    def add_arc_with_capacity(self, tail: int, head: int, capacity: int) -> int: ...
    def add_arcs_with_capacity(self, arg0: numpy.ndarray[numpy.int32], arg1: numpy.ndarray[numpy.int32], arg2: numpy.ndarray[numpy.int64]) -> object: ...
    def capacity(self, arc: int) -> int: ...
    def flow(self, arc: int) -> int: ...
    def flows(self, arg0: numpy.ndarray[numpy.int32]) -> object: ...
    def get_sink_side_min_cut(self) -> list[int]: ...
    def get_source_side_min_cut(self) -> list[int]: ...
    def head(self, arc: int) -> int: ...
    def num_arcs(self) -> int: ...
    def num_nodes(self) -> int: ...
    def optimal_flow(self) -> int: ...
    def set_arc_capacity(self, arc: int, capacity: int) -> None: ...
    def set_arcs_capacity(self, arg0: numpy.ndarray[numpy.int32], arg1: numpy.ndarray[numpy.int64]) -> object: ...
    def solve(self, *args, **kwargs): ...
    def tail(self, arc: int) -> int: ...
