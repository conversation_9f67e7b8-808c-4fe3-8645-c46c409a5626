# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/scheduling/course_scheduling.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/scheduling/course_scheduling.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*ortools/scheduling/course_scheduling.proto\x12\x13operations_research\"\x98\x02\n\x15\x43ourseSchedulingModel\x12\x14\n\x0c\x64isplay_name\x18\x01 \x01(\t\x12\x12\n\ndays_count\x18\x02 \x01(\x05\x12\x1d\n\x15\x64\x61ily_time_slot_count\x18\x03 \x01(\x05\x12,\n\x07\x63ourses\x18\x04 \x03(\x0b\x32\x1b.operations_research.Course\x12.\n\x08teachers\x18\x05 \x03(\x0b\x32\x1c.operations_research.Teacher\x12.\n\x08students\x18\x06 \x03(\x0b\x32\x1c.operations_research.Student\x12(\n\x05rooms\x18\x07 \x03(\x0b\x32\x19.operations_research.Room\"\xf9\x01\n\x16\x43ourseSchedulingResult\x12\x0f\n\x07message\x18\x01 \x01(\t\x12H\n\rsolver_status\x18\x02 \x01(\x0e\x32\x31.operations_research.CourseSchedulingResultStatus\x12?\n\x11\x63lass_assignments\x18\x03 \x03(\x0b\x32$.operations_research.ClassAssignment\x12\x43\n\x13student_assignments\x18\x04 \x03(\x0b\x32&.operations_research.StudentAssignment\"i\n\x0f\x43lassAssignment\x12\x14\n\x0c\x63ourse_index\x18\x01 \x01(\x05\x12\x16\n\x0esection_number\x18\x02 \x01(\x05\x12\x12\n\ntime_slots\x18\x03 \x03(\x05\x12\x14\n\x0croom_indices\x18\x04 \x03(\x05\"[\n\x11StudentAssignment\x12\x15\n\rstudent_index\x18\x01 \x01(\x05\x12\x16\n\x0e\x63ourse_indices\x18\x02 \x03(\x05\x12\x17\n\x0fsection_indices\x18\x03 \x03(\x05\"\xd2\x01\n\x06\x43ourse\x12\x14\n\x0c\x64isplay_name\x18\x01 \x01(\t\x12\x16\n\x0emeetings_count\x18\x02 \x01(\x05\x12\x14\n\x0cmax_capacity\x18\x03 \x01(\x05\x12\x14\n\x0cmin_capacity\x18\x04 \x01(\x05\x12\x1f\n\x17\x63onsecutive_slots_count\x18\x05 \x01(\x05\x12\x17\n\x0fteacher_indices\x18\x06 \x03(\x05\x12\x1e\n\x16teacher_section_counts\x18\x07 \x03(\x05\x12\x14\n\x0croom_indices\x18\x08 \x03(\x05\">\n\x07Teacher\x12\x14\n\x0c\x64isplay_name\x18\x01 \x01(\t\x12\x1d\n\x15restricted_time_slots\x18\x02 \x03(\x05\"7\n\x07Student\x12\x14\n\x0c\x64isplay_name\x18\x01 \x01(\t\x12\x16\n\x0e\x63ourse_indices\x18\x02 \x03(\x05\".\n\x04Room\x12\x14\n\x0c\x64isplay_name\x18\x01 \x01(\t\x12\x10\n\x08\x63\x61pacity\x18\x02 \x01(\x05*\xce\x01\n\x1c\x43ourseSchedulingResultStatus\x12/\n+COURSE_SCHEDULING_RESULT_STATUS_UNSPECIFIED\x10\x00\x12\x13\n\x0fSOLVER_FEASIBLE\x10\x01\x12\x12\n\x0eSOLVER_OPTIMAL\x10\x02\x12\x15\n\x11SOLVER_INFEASIBLE\x10\x03\x12\x18\n\x14SOLVER_MODEL_INVALID\x10\x04\x12\x15\n\x11SOLVER_NOT_SOLVED\x10\x05\x12\x0c\n\x08\x41\x42NORMAL\x10\x06\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.scheduling.course_scheduling_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_COURSESCHEDULINGRESULTSTATUS']._serialized_start=1185
  _globals['_COURSESCHEDULINGRESULTSTATUS']._serialized_end=1391
  _globals['_COURSESCHEDULINGMODEL']._serialized_start=68
  _globals['_COURSESCHEDULINGMODEL']._serialized_end=348
  _globals['_COURSESCHEDULINGRESULT']._serialized_start=351
  _globals['_COURSESCHEDULINGRESULT']._serialized_end=600
  _globals['_CLASSASSIGNMENT']._serialized_start=602
  _globals['_CLASSASSIGNMENT']._serialized_end=707
  _globals['_STUDENTASSIGNMENT']._serialized_start=709
  _globals['_STUDENTASSIGNMENT']._serialized_end=800
  _globals['_COURSE']._serialized_start=803
  _globals['_COURSE']._serialized_end=1013
  _globals['_TEACHER']._serialized_start=1015
  _globals['_TEACHER']._serialized_end=1077
  _globals['_STUDENT']._serialized_start=1079
  _globals['_STUDENT']._serialized_end=1134
  _globals['_ROOM']._serialized_start=1136
  _globals['_ROOM']._serialized_end=1182
# @@protoc_insertion_point(module_scope)
