class CppBridge:
    def __init__(self, *args, **kwargs) -> None: ...
    @staticmethod
    def delete_byte_array(buffer: int) -> None: ...
    @staticmethod
    def init_logging(usage: str) -> None: ...
    @staticmethod
    def load_gurobi_shared_library(full_library_path: str) -> bool: ...
    @staticmethod
    def set_flags(flags: CppFlags) -> None: ...
    @staticmethod
    def shutdown_logging() -> None: ...

class CppFlags:
    cp_model_dump_models: bool
    cp_model_dump_prefix: str
    cp_model_dump_response: bool
    cp_model_dump_submodels: bool
    log_prefix: bool
    stderrthreshold: int
    def __init__(self) -> None: ...

class OrToolsVersion:
    def __init__(self, *args, **kwargs) -> None: ...
    @staticmethod
    def major_number() -> int: ...
    @staticmethod
    def minor_number() -> int: ...
    @staticmethod
    def patch_number() -> int: ...
    @staticmethod
    def version_string() -> str: ...
