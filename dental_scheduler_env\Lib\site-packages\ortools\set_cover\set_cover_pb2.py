# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/set_cover/set_cover.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/set_cover/set_cover.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.util import int128_pb2 as ortools_dot_util_dot_int128__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!ortools/set_cover/set_cover.proto\x12\x13operations_research\x1a\x19ortools/util/int128.proto\"\xe8\x01\n\rSetCoverProto\x12\x39\n\x06subset\x18\x01 \x03(\x0b\x32).operations_research.SetCoverProto.Subset\x12\x11\n\x04name\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x35\n\x0b\x66ingerprint\x18\x03 \x01(\x0b\x32\x1b.operations_research.Int128H\x01\x88\x01\x01\x1a\x39\n\x06Subset\x12\x11\n\x04\x63ost\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x13\n\x07\x65lement\x18\x02 \x03(\x03\x42\x02\x10\x01\x42\x07\n\x05_costB\x07\n\x05_nameB\x0e\n\x0c_fingerprint\"\xe9\x03\n\x18SetCoverSolutionResponse\x12I\n\x06status\x18\x01 \x01(\x0e\x32\x34.operations_research.SetCoverSolutionResponse.StatusH\x00\x88\x01\x01\x12\x18\n\x0bnum_subsets\x18\x02 \x01(\x03H\x01\x88\x01\x01\x12\x12\n\x06subset\x18\x03 \x03(\x03\x42\x02\x10\x01\x12\x11\n\x04\x63ost\x18\x04 \x01(\x01H\x02\x88\x01\x01\x12\x1d\n\x10\x63ost_lower_bound\x18\x05 \x01(\x01H\x03\x88\x01\x01\x12\x35\n\x0b\x66ingerprint\x18\x06 \x01(\x0b\x32\x1b.operations_research.Int128H\x04\x88\x01\x01\x12;\n\x11model_fingerprint\x18\x07 \x01(\x0b\x32\x1b.operations_research.Int128H\x05\x88\x01\x01\"O\n\x06Status\x12\r\n\tUNDEFINED\x10\x00\x12\x0b\n\x07OPTIMAL\x10\x01\x12\x0c\n\x08\x46\x45\x41SIBLE\x10\x02\x12\x0e\n\nINFEASIBLE\x10\x03\x12\x0b\n\x07INVALID\x10\x04\x42\t\n\x07_statusB\x0e\n\x0c_num_subsetsB\x07\n\x05_costB\x13\n\x11_cost_lower_boundB\x0e\n\x0c_fingerprintB\x14\n\x12_model_fingerprintB!\n\x1d\x63om.google.ortools.algorithmsP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.set_cover.set_cover_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.google.ortools.algorithmsP\001'
  _globals['_SETCOVERPROTO_SUBSET'].fields_by_name['element']._loaded_options = None
  _globals['_SETCOVERPROTO_SUBSET'].fields_by_name['element']._serialized_options = b'\020\001'
  _globals['_SETCOVERSOLUTIONRESPONSE'].fields_by_name['subset']._loaded_options = None
  _globals['_SETCOVERSOLUTIONRESPONSE'].fields_by_name['subset']._serialized_options = b'\020\001'
  _globals['_SETCOVERPROTO']._serialized_start=86
  _globals['_SETCOVERPROTO']._serialized_end=318
  _globals['_SETCOVERPROTO_SUBSET']._serialized_start=236
  _globals['_SETCOVERPROTO_SUBSET']._serialized_end=293
  _globals['_SETCOVERSOLUTIONRESPONSE']._serialized_start=321
  _globals['_SETCOVERSOLUTIONRESPONSE']._serialized_end=810
  _globals['_SETCOVERSOLUTIONRESPONSE_STATUS']._serialized_start=636
  _globals['_SETCOVERSOLUTIONRESPONSE_STATUS']._serialized_end=715
# @@protoc_insertion_point(module_scope)
