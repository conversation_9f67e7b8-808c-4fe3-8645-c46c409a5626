"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Updates an existing Model proto.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import ortools.math_opt.model_pb2
import ortools.math_opt.sparse_containers_pb2
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class VariableUpdatesProto(google.protobuf.message.Message):
    """Updates to existing variables in a ModelProto.

    Applies only to existing variables in a model, for new variables, see
    ModelUpdateProto.new_variables.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOWER_BOUNDS_FIELD_NUMBER: builtins.int
    UPPER_BOUNDS_FIELD_NUMBER: builtins.int
    INTEGERS_FIELD_NUMBER: builtins.int
    @property
    def lower_bounds(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Updates ModelProto.variables.lower_bounds.

        Requirements:
         * lower_bounds.ids must be from ModelProto.variables.ids.
         * lower_bounds.values must be < infinity.
         * Unset values are unchanged.
        """

    @property
    def upper_bounds(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Updates ModelProto.variables.upper_bounds.

        Requirements:
         * upper_bounds.ids must be from ModelProto.variables.ids.
         * upper_bounds.values must be > -infinity.
         * Unset values are unchanged.
        """

    @property
    def integers(self) -> ortools.math_opt.sparse_containers_pb2.SparseBoolVectorProto:
        """Updates ModelProto.variables.integers.

        Requirements:
         * integers.ids must be from ModelProto.variables.ids.
         * Unset values are unchanged.
        """

    def __init__(
        self,
        *,
        lower_bounds: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        upper_bounds: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        integers: ortools.math_opt.sparse_containers_pb2.SparseBoolVectorProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["integers", b"integers", "lower_bounds", b"lower_bounds", "upper_bounds", b"upper_bounds"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["integers", b"integers", "lower_bounds", b"lower_bounds", "upper_bounds", b"upper_bounds"]) -> None: ...

global___VariableUpdatesProto = VariableUpdatesProto

@typing.final
class ObjectiveUpdatesProto(google.protobuf.message.Message):
    """Updates the objective of a Model, both for existing and new variables."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DIRECTION_UPDATE_FIELD_NUMBER: builtins.int
    OFFSET_UPDATE_FIELD_NUMBER: builtins.int
    LINEAR_COEFFICIENTS_FIELD_NUMBER: builtins.int
    QUADRATIC_COEFFICIENTS_FIELD_NUMBER: builtins.int
    PRIORITY_UPDATE_FIELD_NUMBER: builtins.int
    direction_update: builtins.bool
    """Not set indicates no change, false is minimize, true is maximize."""
    offset_update: builtins.float
    """Not set indicates not change, otherwise the new offset."""
    priority_update: builtins.int
    """Not set indicates no change, otherwise the new priority. If set, the value
    must be nonnegative. Furthermore, each objective priority must be distinct
    at solve time; this condition is not validated at the proto level, so
    models may temporarily have objectives with the same priority.
    """
    @property
    def linear_coefficients(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Updates ObjectiveProto.linear_coefficients.

        Requirements:
         * linear_coefficients.ids must be variable ids, either existing one (from
           ModelProto.variables.ids) or new ones (from
           ModelUpdateProto.new_variables.ids).
         * linear_coefficients.values must be finite
         * Unset values are unchanged.
         * The value 0.0 removes a variable from the linear objective. This
           value should only be used for existing variables.
        """

    @property
    def quadratic_coefficients(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleMatrixProto:
        """Updates ObjectiveProto.quadratic_coefficients

        Requirements in addition to those on SparseDoubleMatrixProto messages:
         * Each element of quadratic_coefficients.row_ids and each element of
           quadratic_coefficients.column_ids must be a variable id, either an
           existing one (from ModelProto.variables.ids) or a new one (from
           ModelUpdateProto.new_variables.ids).
         * The matrix must be upper triangular: for each i,
           quadratic_coefficients.row_ids[i] <=
           quadratic_coefficients.column_ids[i].

        Notes:
         * Unset values are unchanged.
         * The value 0.0 removes a quadratic term (i.e. product of two variables)
           from the quadratic objective. This value should only be used for
           existing quadratic terms appearing in the objective.
        """

    def __init__(
        self,
        *,
        direction_update: builtins.bool | None = ...,
        offset_update: builtins.float | None = ...,
        linear_coefficients: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        quadratic_coefficients: ortools.math_opt.sparse_containers_pb2.SparseDoubleMatrixProto | None = ...,
        priority_update: builtins.int | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_direction_update", b"_direction_update", "_offset_update", b"_offset_update", "_priority_update", b"_priority_update", "direction_update", b"direction_update", "linear_coefficients", b"linear_coefficients", "offset_update", b"offset_update", "priority_update", b"priority_update", "quadratic_coefficients", b"quadratic_coefficients"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_direction_update", b"_direction_update", "_offset_update", b"_offset_update", "_priority_update", b"_priority_update", "direction_update", b"direction_update", "linear_coefficients", b"linear_coefficients", "offset_update", b"offset_update", "priority_update", b"priority_update", "quadratic_coefficients", b"quadratic_coefficients"]) -> None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_direction_update", b"_direction_update"]) -> typing.Literal["direction_update"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_offset_update", b"_offset_update"]) -> typing.Literal["offset_update"] | None: ...
    @typing.overload
    def WhichOneof(self, oneof_group: typing.Literal["_priority_update", b"_priority_update"]) -> typing.Literal["priority_update"] | None: ...

global___ObjectiveUpdatesProto = ObjectiveUpdatesProto

@typing.final
class AuxiliaryObjectivesUpdatesProto(google.protobuf.message.Message):
    """Updates the auxiliary objectives of a Model, both for existing and new
    variables. Auxiliary objectives can be deleted, added, or modified in place.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class NewObjectivesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> ortools.math_opt.model_pb2.ObjectiveProto: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: ortools.math_opt.model_pb2.ObjectiveProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    @typing.final
    class ObjectiveUpdatesEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> global___ObjectiveUpdatesProto: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: global___ObjectiveUpdatesProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    DELETED_OBJECTIVE_IDS_FIELD_NUMBER: builtins.int
    NEW_OBJECTIVES_FIELD_NUMBER: builtins.int
    OBJECTIVE_UPDATES_FIELD_NUMBER: builtins.int
    @property
    def deleted_objective_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Removes auxiliary objectives from the model.

        Each value must be in [0, max(int64)). Values must be in strictly
        increasing order. Applies only to existing auxiliary objective IDs that
        have not yet been deleted.
        """

    @property
    def new_objectives(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, ortools.math_opt.model_pb2.ObjectiveProto]:
        """Add new auxiliary objectives to the model. All keys must be in
        [0, max(int64)), and must be greater than any ids used in the initial model
         and previous updates. All nonempty names should be distinct from existing
        names for the primary and other auxiliary objectives.
        """

    @property
    def objective_updates(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, global___ObjectiveUpdatesProto]:
        """Updates existing auxiliary objectives in the model. All key IDs must be
        existing in the model and not included in `deleted_objective_ids`.
        """

    def __init__(
        self,
        *,
        deleted_objective_ids: collections.abc.Iterable[builtins.int] | None = ...,
        new_objectives: collections.abc.Mapping[builtins.int, ortools.math_opt.model_pb2.ObjectiveProto] | None = ...,
        objective_updates: collections.abc.Mapping[builtins.int, global___ObjectiveUpdatesProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["deleted_objective_ids", b"deleted_objective_ids", "new_objectives", b"new_objectives", "objective_updates", b"objective_updates"]) -> None: ...

global___AuxiliaryObjectivesUpdatesProto = AuxiliaryObjectivesUpdatesProto

@typing.final
class LinearConstraintUpdatesProto(google.protobuf.message.Message):
    """Updates to existing linear constraints in a ModelProto."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    LOWER_BOUNDS_FIELD_NUMBER: builtins.int
    UPPER_BOUNDS_FIELD_NUMBER: builtins.int
    @property
    def lower_bounds(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Updates ModelProto.linear_constraints.lower_bounds.

        Requirements:
         * lower_bounds.ids must be from ModelProto.linear_constraints.ids.
         * lower_bounds.values must be < infinity.
         * Unset values are unchanged.
        """

    @property
    def upper_bounds(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """Updates ModelProto.linear_constraints.upper_bounds.

        Requirements:
         * upper_bounds.ids must be from ModelProto.linear_constraints.ids.
         * upper_bounds.values must be > -infinity.
         * Unset values are unchanged.
        """

    def __init__(
        self,
        *,
        lower_bounds: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        upper_bounds: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["lower_bounds", b"lower_bounds", "upper_bounds", b"upper_bounds"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["lower_bounds", b"lower_bounds", "upper_bounds", b"upper_bounds"]) -> None: ...

global___LinearConstraintUpdatesProto = LinearConstraintUpdatesProto

@typing.final
class QuadraticConstraintUpdatesProto(google.protobuf.message.Message):
    """Updates to quadratic constraints; only addition and deletion, no support for
    in-place constraint updates.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class NewConstraintsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> ortools.math_opt.model_pb2.QuadraticConstraintProto: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: ortools.math_opt.model_pb2.QuadraticConstraintProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    DELETED_CONSTRAINT_IDS_FIELD_NUMBER: builtins.int
    NEW_CONSTRAINTS_FIELD_NUMBER: builtins.int
    @property
    def deleted_constraint_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Removes quadratic constraints from the model.

        Each value must be in [0, max(int64)). Values must be in strictly
        increasing order. Applies only to existing quadratic constraint ids that
        have not yet been deleted.
        """

    @property
    def new_constraints(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, ortools.math_opt.model_pb2.QuadraticConstraintProto]:
        """Add new quadratic constraints to the model. All keys must be in
        [0, max(int64)), and must be greater than any ids used in the initial model
        and previous updates. All nonempty names should be distinct from existing
        names and each other.
        """

    def __init__(
        self,
        *,
        deleted_constraint_ids: collections.abc.Iterable[builtins.int] | None = ...,
        new_constraints: collections.abc.Mapping[builtins.int, ortools.math_opt.model_pb2.QuadraticConstraintProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["deleted_constraint_ids", b"deleted_constraint_ids", "new_constraints", b"new_constraints"]) -> None: ...

global___QuadraticConstraintUpdatesProto = QuadraticConstraintUpdatesProto

@typing.final
class SecondOrderConeConstraintUpdatesProto(google.protobuf.message.Message):
    """Updates to second-order cone constraints; only addition and deletion, no
    support for in-place constraint updates.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class NewConstraintsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> ortools.math_opt.model_pb2.SecondOrderConeConstraintProto: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: ortools.math_opt.model_pb2.SecondOrderConeConstraintProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    DELETED_CONSTRAINT_IDS_FIELD_NUMBER: builtins.int
    NEW_CONSTRAINTS_FIELD_NUMBER: builtins.int
    @property
    def deleted_constraint_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Removes second-order cone constraints from the model.

        Each value must be in [0, max(int64)). Values must be in strictly
        increasing order. Applies only to existing second-order cone constraint ids
        that have not yet been deleted.
        """

    @property
    def new_constraints(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, ortools.math_opt.model_pb2.SecondOrderConeConstraintProto]:
        """Add new second-order cone constraints to the model. All keys must be in
        [0, max(int64)), and must be greater than any ids used in the initial model
        and previous updates. All nonempty names should be distinct from existing
        names and each other.
        """

    def __init__(
        self,
        *,
        deleted_constraint_ids: collections.abc.Iterable[builtins.int] | None = ...,
        new_constraints: collections.abc.Mapping[builtins.int, ortools.math_opt.model_pb2.SecondOrderConeConstraintProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["deleted_constraint_ids", b"deleted_constraint_ids", "new_constraints", b"new_constraints"]) -> None: ...

global___SecondOrderConeConstraintUpdatesProto = SecondOrderConeConstraintUpdatesProto

@typing.final
class SosConstraintUpdatesProto(google.protobuf.message.Message):
    """Data for updates to SOS1 and SOS2 constraints; only addition and deletion, no
    support for in-place constraint updates.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class NewConstraintsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> ortools.math_opt.model_pb2.SosConstraintProto: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: ortools.math_opt.model_pb2.SosConstraintProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    DELETED_CONSTRAINT_IDS_FIELD_NUMBER: builtins.int
    NEW_CONSTRAINTS_FIELD_NUMBER: builtins.int
    @property
    def deleted_constraint_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Removes SOS constraints from the model.

        Each value must be in [0, max(int64)). Values must be in strictly
        increasing order. Applies only to existing SOS constraint ids that have not
        yet been deleted.
        """

    @property
    def new_constraints(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, ortools.math_opt.model_pb2.SosConstraintProto]:
        """Add new SOS constraints to the model. All keys must be in [0, max(int64)),
        and must be greater than any ids used in the initial model and previous
        updates. All nonempty names should be distinct from existing names and each
        other.
        """

    def __init__(
        self,
        *,
        deleted_constraint_ids: collections.abc.Iterable[builtins.int] | None = ...,
        new_constraints: collections.abc.Mapping[builtins.int, ortools.math_opt.model_pb2.SosConstraintProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["deleted_constraint_ids", b"deleted_constraint_ids", "new_constraints", b"new_constraints"]) -> None: ...

global___SosConstraintUpdatesProto = SosConstraintUpdatesProto

@typing.final
class IndicatorConstraintUpdatesProto(google.protobuf.message.Message):
    """Data for updates to indicator constraints; only addition and deletion, no
    support for in-place constraint updates.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class NewConstraintsEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.int
        @property
        def value(self) -> ortools.math_opt.model_pb2.IndicatorConstraintProto: ...
        def __init__(
            self,
            *,
            key: builtins.int = ...,
            value: ortools.math_opt.model_pb2.IndicatorConstraintProto | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["value", b"value"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    DELETED_CONSTRAINT_IDS_FIELD_NUMBER: builtins.int
    NEW_CONSTRAINTS_FIELD_NUMBER: builtins.int
    @property
    def deleted_constraint_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Removes indicator constraints from the model.

        Each value must be in [0, max(int64)). Values must be in strictly
        increasing order. Applies only to existing indicator constraint ids that
        have not yet been deleted.
        """

    @property
    def new_constraints(self) -> google.protobuf.internal.containers.MessageMap[builtins.int, ortools.math_opt.model_pb2.IndicatorConstraintProto]:
        """Add new indicator constraints to the model. All keys must be in
        [0, max(int64)), and must be greater than any ids used in the initial model
        and previous updates. All nonempty names should be distinct from existing
        names and each other.
        """

    def __init__(
        self,
        *,
        deleted_constraint_ids: collections.abc.Iterable[builtins.int] | None = ...,
        new_constraints: collections.abc.Mapping[builtins.int, ortools.math_opt.model_pb2.IndicatorConstraintProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["deleted_constraint_ids", b"deleted_constraint_ids", "new_constraints", b"new_constraints"]) -> None: ...

global___IndicatorConstraintUpdatesProto = IndicatorConstraintUpdatesProto

@typing.final
class ModelUpdateProto(google.protobuf.message.Message):
    """Updates to a ModelProto."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DELETED_VARIABLE_IDS_FIELD_NUMBER: builtins.int
    DELETED_LINEAR_CONSTRAINT_IDS_FIELD_NUMBER: builtins.int
    VARIABLE_UPDATES_FIELD_NUMBER: builtins.int
    LINEAR_CONSTRAINT_UPDATES_FIELD_NUMBER: builtins.int
    NEW_VARIABLES_FIELD_NUMBER: builtins.int
    NEW_LINEAR_CONSTRAINTS_FIELD_NUMBER: builtins.int
    OBJECTIVE_UPDATES_FIELD_NUMBER: builtins.int
    AUXILIARY_OBJECTIVES_UPDATES_FIELD_NUMBER: builtins.int
    LINEAR_CONSTRAINT_MATRIX_UPDATES_FIELD_NUMBER: builtins.int
    QUADRATIC_CONSTRAINT_UPDATES_FIELD_NUMBER: builtins.int
    SECOND_ORDER_CONE_CONSTRAINT_UPDATES_FIELD_NUMBER: builtins.int
    SOS1_CONSTRAINT_UPDATES_FIELD_NUMBER: builtins.int
    SOS2_CONSTRAINT_UPDATES_FIELD_NUMBER: builtins.int
    INDICATOR_CONSTRAINT_UPDATES_FIELD_NUMBER: builtins.int
    @property
    def deleted_variable_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Removes variables from the model.

        Values must be in strictly increasing order. Apply only to existing
        variable ids that have not yet been deleted. The ids of deleted variables
        should not appear in other fields (e.g. variable_updates,
        objective_updates, linear_constraint_matrix_updates).
        """

    @property
    def deleted_linear_constraint_ids(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Removes linear constraints from the model.

        Values must be in strictly increasing order. Apply only to existing
        linear constraint ids that have not yet been deleted. The ids of deleted
        linear constraints should not appear in other fields (e.g.
        linear_constraint_updates, linear_constraint_matrix_updates).
        """

    @property
    def variable_updates(self) -> global___VariableUpdatesProto:
        """Updates properties of existing variables. Should not contain any deleted
        variable ids.
        """

    @property
    def linear_constraint_updates(self) -> global___LinearConstraintUpdatesProto:
        """Updates properties of existing linear constraints. Should not contain any
        deleted linear constraints ids.
        """

    @property
    def new_variables(self) -> ortools.math_opt.model_pb2.VariablesProto:
        """Add new variables to the model. All new_variables.ids must be greater than
        any ids used in the initial model and previous updates. All nonempty names
        should be distinct from existing names.
        """

    @property
    def new_linear_constraints(self) -> ortools.math_opt.model_pb2.LinearConstraintsProto:
        """Add new linear constraints to the model. All new_linear_constraints.ids
        must be greater than any ids used in the initial model and previous
        updates. All nonempty names should be distinct from existing names.
        """

    @property
    def objective_updates(self) -> global___ObjectiveUpdatesProto:
        """Updates the primary objective, both for existing and new variables."""

    @property
    def auxiliary_objectives_updates(self) -> global___AuxiliaryObjectivesUpdatesProto:
        """Updates the auxiliary objectives, both for existing and new variables."""

    @property
    def linear_constraint_matrix_updates(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleMatrixProto:
        """Updates the linear constraint matrix, both for existing and new
        variables/linear constraints.

        Requirements:
         * linear_constraint_matrix_updates.row_ids are linear constraint ids,
           either existing or new.
         * linear_constraint_matrix_updates.column_ids are variables ids, either
           existing or new.
         * Matrix entries are unchanged if the (constraint, variable) pair is
           existing and unset.
         * Matrix entries are zero if either the constraint or variable is new and
           the (constraint, variable) pair is unset.
         * Zero values delete existing entries, and have no effect for new entries.
         * linear_constraint_matrix.values must all be finite.
        """

    @property
    def quadratic_constraint_updates(self) -> global___QuadraticConstraintUpdatesProto:
        """Updates the quadratic constraints (addition and deletion only)."""

    @property
    def second_order_cone_constraint_updates(self) -> global___SecondOrderConeConstraintUpdatesProto:
        """Updates the second-order cone constraints (addition and deletion only)."""

    @property
    def sos1_constraint_updates(self) -> global___SosConstraintUpdatesProto:
        """Updates the general constraints (addition and deletion only)."""

    @property
    def sos2_constraint_updates(self) -> global___SosConstraintUpdatesProto: ...
    @property
    def indicator_constraint_updates(self) -> global___IndicatorConstraintUpdatesProto: ...
    def __init__(
        self,
        *,
        deleted_variable_ids: collections.abc.Iterable[builtins.int] | None = ...,
        deleted_linear_constraint_ids: collections.abc.Iterable[builtins.int] | None = ...,
        variable_updates: global___VariableUpdatesProto | None = ...,
        linear_constraint_updates: global___LinearConstraintUpdatesProto | None = ...,
        new_variables: ortools.math_opt.model_pb2.VariablesProto | None = ...,
        new_linear_constraints: ortools.math_opt.model_pb2.LinearConstraintsProto | None = ...,
        objective_updates: global___ObjectiveUpdatesProto | None = ...,
        auxiliary_objectives_updates: global___AuxiliaryObjectivesUpdatesProto | None = ...,
        linear_constraint_matrix_updates: ortools.math_opt.sparse_containers_pb2.SparseDoubleMatrixProto | None = ...,
        quadratic_constraint_updates: global___QuadraticConstraintUpdatesProto | None = ...,
        second_order_cone_constraint_updates: global___SecondOrderConeConstraintUpdatesProto | None = ...,
        sos1_constraint_updates: global___SosConstraintUpdatesProto | None = ...,
        sos2_constraint_updates: global___SosConstraintUpdatesProto | None = ...,
        indicator_constraint_updates: global___IndicatorConstraintUpdatesProto | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["auxiliary_objectives_updates", b"auxiliary_objectives_updates", "indicator_constraint_updates", b"indicator_constraint_updates", "linear_constraint_matrix_updates", b"linear_constraint_matrix_updates", "linear_constraint_updates", b"linear_constraint_updates", "new_linear_constraints", b"new_linear_constraints", "new_variables", b"new_variables", "objective_updates", b"objective_updates", "quadratic_constraint_updates", b"quadratic_constraint_updates", "second_order_cone_constraint_updates", b"second_order_cone_constraint_updates", "sos1_constraint_updates", b"sos1_constraint_updates", "sos2_constraint_updates", b"sos2_constraint_updates", "variable_updates", b"variable_updates"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["auxiliary_objectives_updates", b"auxiliary_objectives_updates", "deleted_linear_constraint_ids", b"deleted_linear_constraint_ids", "deleted_variable_ids", b"deleted_variable_ids", "indicator_constraint_updates", b"indicator_constraint_updates", "linear_constraint_matrix_updates", b"linear_constraint_matrix_updates", "linear_constraint_updates", b"linear_constraint_updates", "new_linear_constraints", b"new_linear_constraints", "new_variables", b"new_variables", "objective_updates", b"objective_updates", "quadratic_constraint_updates", b"quadratic_constraint_updates", "second_order_cone_constraint_updates", b"second_order_cone_constraint_updates", "sos1_constraint_updates", b"sos1_constraint_updates", "sos2_constraint_updates", b"sos2_constraint_updates", "variable_updates", b"variable_updates"]) -> None: ...

global___ModelUpdateProto = ModelUpdateProto
