# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/constraint_solver/routing_enums.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/constraint_solver/routing_enums.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n-ortools/constraint_solver/routing_enums.proto\x12\x13operations_research\"\xc5\x03\n\x15\x46irstSolutionStrategy\"\xab\x03\n\x05Value\x12\t\n\x05UNSET\x10\x00\x12\r\n\tAUTOMATIC\x10\x0f\x12\x15\n\x11PATH_CHEAPEST_ARC\x10\x03\x12\x1d\n\x19PATH_MOST_CONSTRAINED_ARC\x10\x04\x12\x16\n\x12\x45VALUATOR_STRATEGY\x10\x05\x12\x0b\n\x07SAVINGS\x10\n\x12\x14\n\x10PARALLEL_SAVINGS\x10\x11\x12\t\n\x05SWEEP\x10\x0b\x12\x10\n\x0c\x43HRISTOFIDES\x10\r\x12\x13\n\x0f\x41LL_UNPERFORMED\x10\x06\x12\x12\n\x0e\x42\x45ST_INSERTION\x10\x07\x12\x1f\n\x1bPARALLEL_CHEAPEST_INSERTION\x10\x08\x12!\n\x1dSEQUENTIAL_CHEAPEST_INSERTION\x10\x0e\x12\x1c\n\x18LOCAL_CHEAPEST_INSERTION\x10\t\x12!\n\x1dLOCAL_CHEAPEST_COST_INSERTION\x10\x10\x12\x17\n\x13GLOBAL_CHEAPEST_ARC\x10\x01\x12\x16\n\x12LOCAL_CHEAPEST_ARC\x10\x02\x12\x1b\n\x17\x46IRST_UNBOUND_MIN_VALUE\x10\x0c\"\xae\x01\n\x18LocalSearchMetaheuristic\"\x91\x01\n\x05Value\x12\t\n\x05UNSET\x10\x00\x12\r\n\tAUTOMATIC\x10\x06\x12\x12\n\x0eGREEDY_DESCENT\x10\x01\x12\x17\n\x13GUIDED_LOCAL_SEARCH\x10\x02\x12\x17\n\x13SIMULATED_ANNEALING\x10\x03\x12\x0f\n\x0bTABU_SEARCH\x10\x04\x12\x17\n\x13GENERIC_TABU_SEARCH\x10\x05\"\xf1\x01\n\x13RoutingSearchStatus\"\xd9\x01\n\x05Value\x12\x16\n\x12ROUTING_NOT_SOLVED\x10\x00\x12\x13\n\x0fROUTING_SUCCESS\x10\x01\x12\x35\n1ROUTING_PARTIAL_SUCCESS_LOCAL_OPTIMUM_NOT_REACHED\x10\x02\x12\x10\n\x0cROUTING_FAIL\x10\x03\x12\x18\n\x14ROUTING_FAIL_TIMEOUT\x10\x04\x12\x13\n\x0fROUTING_INVALID\x10\x05\x12\x16\n\x12ROUTING_INFEASIBLE\x10\x06\x12\x13\n\x0fROUTING_OPTIMAL\x10\x07\x42I\n#com.google.ortools.constraintsolverP\x01\xaa\x02\x1fGoogle.OrTools.ConstraintSolverb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.constraint_solver.routing_enums_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n#com.google.ortools.constraintsolverP\001\252\002\037Google.OrTools.ConstraintSolver'
  _globals['_FIRSTSOLUTIONSTRATEGY']._serialized_start=71
  _globals['_FIRSTSOLUTIONSTRATEGY']._serialized_end=524
  _globals['_FIRSTSOLUTIONSTRATEGY_VALUE']._serialized_start=97
  _globals['_FIRSTSOLUTIONSTRATEGY_VALUE']._serialized_end=524
  _globals['_LOCALSEARCHMETAHEURISTIC']._serialized_start=527
  _globals['_LOCALSEARCHMETAHEURISTIC']._serialized_end=701
  _globals['_LOCALSEARCHMETAHEURISTIC_VALUE']._serialized_start=556
  _globals['_LOCALSEARCHMETAHEURISTIC_VALUE']._serialized_end=701
  _globals['_ROUTINGSEARCHSTATUS']._serialized_start=704
  _globals['_ROUTINGSEARCHSTATUS']._serialized_end=945
  _globals['_ROUTINGSEARCHSTATUS_VALUE']._serialized_start=728
  _globals['_ROUTINGSEARCHSTATUS_VALUE']._serialized_end=945
# @@protoc_insertion_point(module_scope)
