from _typeshed import Incomplete
from ortools.linear_solver.python.linear_solver_natural_api import LinearConstraint as LinearConstraint, LinearExpr as LinearExpr, OFFSET_KEY as OFFSET_KEY, ProductCst as Product<PERSON>t, Sum as Sum, SumArray as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as Sum<PERSON><PERSON>, VariableExpr as VariableExpr, inf as inf

class _SwigNonDynamicMeta(type):
    __setattr__: Incomplete

class Solver:
    thisown: Incomplete
    CLP_LINEAR_PROGRAMMING: Incomplete
    GLPK_LINEAR_PROGRAMMING: Incomplete
    GLOP_LINEAR_PROGRAMMING: Incomplete
    PDLP_LINEAR_PROGRAMMING: Incomplete
    SCIP_MIXED_INTEGER_PROGRAMMING: Incomplete
    GLPK_MIXED_INTEGER_PROGRAMMING: Incomplete
    CBC_MIXED_INTEGER_PROGRAMMING: Incomplete
    BOP_INTEGER_PROGRAMMING: Incomplete
    SAT_INTEGER_PROGRAMMING: Incomplete
    GUROBI_LINEAR_PROGRAMMING: Incomplete
    GUROBI_MIXED_INTEGER_PROGRAMMING: Incomplete
    CPLEX_LINEAR_PROGRAMMING: Incomplete
    CPLEX_MIXED_INTEGER_PROGRAMMING: Incomplete
    XPRESS_LINEAR_PROGRAMMING: Incomplete
    XPRESS_MIXED_INTEGER_PROGRAMMING: Incomplete
    def __init__(self, name, problem_type) -> None: ...
    __swig_destroy__: Incomplete
    @staticmethod
    def CreateSolver(solver_id): ...
    @staticmethod
    def SupportsProblemType(problem_type): ...
    def IsMip(self): ...
    def Clear(self): ...
    def NumVariables(self): ...
    def variables(self): ...
    def variable(self, index): ...
    def LookupVariable(self, var_name): ...
    def Var(self, lb, ub, integer, name): ...
    def NumVar(self, lb, ub, name): ...
    def IntVar(self, lb, ub, name): ...
    def BoolVar(self, name): ...
    def NumConstraints(self): ...
    def constraints(self): ...
    def constraint(self, index): ...
    def LookupConstraint(self, constraint_name): ...
    def Constraint(self, *args): ...
    def Objective(self): ...
    OPTIMAL: Incomplete
    FEASIBLE: Incomplete
    INFEASIBLE: Incomplete
    UNBOUNDED: Incomplete
    ABNORMAL: Incomplete
    MODEL_INVALID: Incomplete
    NOT_SOLVED: Incomplete
    def Solve(self, *args): ...
    def Write(self, file_name): ...
    def ComputeConstraintActivities(self): ...
    def VerifySolution(self, tolerance, log_errors): ...
    def InterruptSolve(self): ...
    def FillSolutionResponseProto(self, response): ...
    @staticmethod
    def SolveWithProto(model_request, response, interrupt: Incomplete | None = None): ...
    def ExportModelToProto(self, output_model): ...
    def SetSolverSpecificParametersAsString(self, parameters): ...
    FREE: Incomplete
    AT_LOWER_BOUND: Incomplete
    AT_UPPER_BOUND: Incomplete
    FIXED_VALUE: Incomplete
    BASIC: Incomplete
    @staticmethod
    def infinity(): ...
    def EnableOutput(self): ...
    def SuppressOutput(self): ...
    def iterations(self): ...
    def nodes(self): ...
    def SolverVersion(self): ...
    def ComputeExactConditionNumber(self): ...
    def NextSolution(self): ...
    def set_time_limit(self, time_limit_milliseconds): ...
    def wall_time(self): ...
    def LoadModelFromProto(self, input_model): ...
    def LoadModelFromProtoKeepNames(self, input_model): ...
    def LoadModelFromProtoWithUniqueNamesOrDie(self, input_model): ...
    def LoadSolutionFromProto(self, *args): ...
    def ExportModelAsLpFormat(self, obfuscate): ...
    def ExportModelAsMpsFormat(self, fixed_format, obfuscate): ...
    def WriteModelToMpsFile(self, filename, fixed_format, obfuscate): ...
    def SetHint(self, variables, values): ...
    def SetNumThreads(self, num_theads): ...
    def Add(self, constraint, name: str = ''): ...
    def Sum(self, expr_array): ...
    def RowConstraint(self, *args): ...
    def Minimize(self, expr) -> None: ...
    def Maximize(self, expr) -> None: ...
    @staticmethod
    def Infinity(): ...
    def SetTimeLimit(self, x): ...
    def WallTime(self): ...
    def Iterations(self): ...

def __lshift__(*args): ...

class Objective:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def Clear(self): ...
    def SetCoefficient(self, var, coeff): ...
    def GetCoefficient(self, var): ...
    def SetOffset(self, value): ...
    def offset(self): ...
    def SetOptimizationDirection(self, maximize): ...
    def SetMinimization(self): ...
    def SetMaximization(self): ...
    def maximization(self): ...
    def minimization(self): ...
    def Value(self): ...
    def BestBound(self): ...
    def Offset(self): ...
    __swig_destroy__: Incomplete

class Variable:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def name(self): ...
    def SetInteger(self, integer): ...
    def integer(self): ...
    def solution_value(self): ...
    def index(self): ...
    def lb(self): ...
    def ub(self): ...
    def SetBounds(self, lb, ub): ...
    def reduced_cost(self): ...
    def basis_status(self): ...
    def branching_priority(self): ...
    def SetBranchingPriority(self, priority): ...
    def __getattr__(self, name): ...
    def SolutionValue(self): ...
    def Integer(self): ...
    def Lb(self): ...
    def Ub(self): ...
    def SetLb(self, x): ...
    def SetUb(self, x): ...
    def ReducedCost(self): ...
    __swig_destroy__: Incomplete

class Constraint:
    thisown: Incomplete
    def __init__(self, *args, **kwargs) -> None: ...
    def name(self): ...
    def Clear(self): ...
    def SetCoefficient(self, var, coeff): ...
    def GetCoefficient(self, var): ...
    def lb(self): ...
    def ub(self): ...
    def SetBounds(self, lb, ub): ...
    def set_is_lazy(self, laziness): ...
    def index(self): ...
    def dual_value(self): ...
    def basis_status(self): ...
    def Lb(self): ...
    def Ub(self): ...
    def SetLb(self, x): ...
    def SetUb(self, x): ...
    def DualValue(self): ...
    __swig_destroy__: Incomplete

class MPSolverParameters:
    thisown: Incomplete
    RELATIVE_MIP_GAP: Incomplete
    PRIMAL_TOLERANCE: Incomplete
    DUAL_TOLERANCE: Incomplete
    PRESOLVE: Incomplete
    LP_ALGORITHM: Incomplete
    INCREMENTALITY: Incomplete
    SCALING: Incomplete
    PRESOLVE_OFF: Incomplete
    PRESOLVE_ON: Incomplete
    DUAL: Incomplete
    PRIMAL: Incomplete
    BARRIER: Incomplete
    INCREMENTALITY_OFF: Incomplete
    INCREMENTALITY_ON: Incomplete
    SCALING_OFF: Incomplete
    SCALING_ON: Incomplete
    def __init__(self) -> None: ...
    def SetDoubleParam(self, param, value): ...
    def SetIntegerParam(self, param, value): ...
    def GetDoubleParam(self, param): ...
    def GetIntegerParam(self, param): ...
    __swig_destroy__: Incomplete

cvar: Incomplete

class ModelExportOptions:
    thisown: Incomplete
    def __init__(self) -> None: ...
    __swig_destroy__: Incomplete

def ExportModelAsLpFormat(*args): ...
def ExportModelAsMpsFormat(*args): ...
def FindErrorInModelProto(input_model): ...
def setup_variable_operator(opname): ...
