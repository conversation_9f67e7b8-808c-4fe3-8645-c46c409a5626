#!/usr/bin/env python3
"""
Build script for creating the Dental Scheduler Windows installer
This script automates the entire build process from Python source to .exe installer
"""
import os
import sys
import subprocess
import shutil
import time
from pathlib import Path

def print_header(message):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {message}")
    print("=" * 60)

def print_step(step, message):
    """Print a formatted step"""
    print(f"\n[Step {step}] {message}")

def print_success(message):
    """Print success message"""
    print(f"✓ {message}")

def print_error(message):
    """Print error message"""
    print(f"✗ ERROR: {message}")

def print_warning(message):
    """Print warning message"""
    print(f"⚠ WARNING: {message}")

def run_command(command, description, check_result=True):
    """Run a command and handle errors"""
    print(f"Running: {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if check_result and result.returncode != 0:
            print_error(f"Failed to {description.lower()}")
            print(f"Command: {command}")
            print(f"Error output: {result.stderr}")
            return False
        if result.stdout:
            print(result.stdout)
        print_success(f"{description} completed")
        return True
    except Exception as e:
        print_error(f"Failed to {description.lower()}: {e}")
        return False

def check_prerequisites():
    """Check if all required tools are installed"""
    print_step(1, "Checking Prerequisites")
    
    # Check Python
    try:
        python_version = subprocess.run([sys.executable, "--version"], 
                                      capture_output=True, text=True)
        print_success(f"Python found: {python_version.stdout.strip()}")
    except:
        print_error("Python not found")
        return False
    
    # Check PyInstaller
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "show", "pyinstaller"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print_warning("PyInstaller not found, installing...")
            if not run_command(f"{sys.executable} -m pip install pyinstaller", 
                             "Installing PyInstaller"):
                return False
        else:
            print_success("PyInstaller found")
    except:
        print_error("Failed to check PyInstaller")
        return False
    
    # Check if Inno Setup is available (optional)
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe",
    ]
    
    inno_found = False
    for path in inno_paths:
        if os.path.exists(path):
            print_success(f"Inno Setup found: {path}")
            inno_found = True
            break
    
    if not inno_found:
        print_warning("Inno Setup not found")
        print("Please download and install Inno Setup from: https://jrsoftware.org/isinfo.php")
        print("The script will create the PyInstaller build, but you'll need to run Inno Setup manually")
    
    return True

def create_icon():
    """Create application icon"""
    print_step(2, "Creating Application Icon")
    
    try:
        # Try to create icon with PIL
        result = subprocess.run([sys.executable, "create_icon.py"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print_success("Icon created successfully")
            return True
        else:
            print_warning("Failed to create icon with PIL, using placeholder")
    except:
        print_warning("Could not create icon")
    
    # Create a simple placeholder icon file
    if not os.path.exists("icon.ico"):
        print("Creating placeholder icon...")
        # For now, we'll proceed without an icon
        # In a real scenario, you'd want to include a proper .ico file
    
    return True

def install_dependencies():
    """Install all required dependencies"""
    print_step(3, "Installing Dependencies")
    
    # Install from requirements.txt
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", 
                      "Installing project dependencies"):
        return False
    
    # Install additional build dependencies
    build_deps = ["pyinstaller", "pillow"]
    for dep in build_deps:
        if not run_command(f"{sys.executable} -m pip install {dep}", 
                          f"Installing {dep}"):
            print_warning(f"Failed to install {dep}, continuing...")
    
    return True

def clean_build_directories():
    """Clean previous build artifacts"""
    print_step(4, "Cleaning Build Directories")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            try:
                shutil.rmtree(dir_name)
                print_success(f"Cleaned {dir_name}/")
            except Exception as e:
                print_warning(f"Could not clean {dir_name}/: {e}")
    
    return True

def build_with_pyinstaller():
    """Build the application with PyInstaller"""
    print_step(5, "Building Application with PyInstaller")
    
    # Build using the spec file
    if not run_command(f"{sys.executable} -m PyInstaller dental_scheduler.spec --clean", 
                      "Building with PyInstaller"):
        return False
    
    # Check if build was successful
    if not os.path.exists("dist/DentalScheduler"):
        print_error("PyInstaller build failed - dist directory not found")
        return False
    
    print_success("PyInstaller build completed successfully")
    
    # List the contents of the dist directory
    print("\nBuild output contents:")
    try:
        for item in os.listdir("dist/DentalScheduler"):
            size = "DIR" if os.path.isdir(f"dist/DentalScheduler/{item}") else f"{os.path.getsize(f'dist/DentalScheduler/{item}'):,} bytes"
            print(f"  {item:<30} {size}")
    except Exception as e:
        print_warning(f"Could not list build contents: {e}")
    
    return True

def create_installer():
    """Create the Windows installer with Inno Setup"""
    print_step(6, "Creating Windows Installer")
    
    # Find Inno Setup compiler
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe",
    ]
    
    iscc_path = None
    for path in inno_paths:
        if os.path.exists(path):
            iscc_path = path
            break
    
    if not iscc_path:
        print_error("Inno Setup not found")
        print("Please install Inno Setup from: https://jrsoftware.org/isinfo.php")
        print("Then run the installer script manually:")
        print(f"  {inno_paths[0]} installer_script.iss")
        return False
    
    # Create installer output directory
    os.makedirs("installer_output", exist_ok=True)
    
    # Run Inno Setup compiler
    if not run_command(f'"{iscc_path}" installer_script.iss', 
                      "Creating installer with Inno Setup"):
        return False
    
    # Check if installer was created
    installer_files = [f for f in os.listdir("installer_output") if f.endswith(".exe")]
    if installer_files:
        installer_file = installer_files[0]
        installer_path = os.path.join("installer_output", installer_file)
        file_size = os.path.getsize(installer_path)
        print_success(f"Installer created: {installer_file} ({file_size:,} bytes)")
        return True
    else:
        print_error("Installer file not found in installer_output/")
        return False

def test_build():
    """Test the built application"""
    print_step(7, "Testing Build")
    
    gui_exe = "dist/DentalScheduler/DentalSchedulerGUI.exe"
    cli_exe = "dist/DentalScheduler/DentalSchedulerCLI.exe"
    
    if not os.path.exists(gui_exe):
        print_error(f"GUI executable not found: {gui_exe}")
        return False
    
    if not os.path.exists(cli_exe):
        print_error(f"CLI executable not found: {cli_exe}")
        return False
    
    print_success("All executable files found")
    
    # Try to run the GUI app briefly to test it loads
    try:
        print("Testing GUI application startup...")
        # Start the process but don't wait for it to finish
        process = subprocess.Popen([gui_exe], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        time.sleep(3)  # Let it start up
        process.terminate()  # Close it
        process.wait(timeout=5)  # Wait for clean shutdown
        print_success("GUI application test passed")
    except Exception as e:
        print_warning(f"GUI test failed: {e}")
    
    return True

def print_summary():
    """Print build summary"""
    print_header("Build Summary")
    
    print("\n📁 Build Output:")
    if os.path.exists("dist/DentalScheduler"):
        print(f"  Application files: dist/DentalScheduler/")
        
    if os.path.exists("installer_output"):
        installer_files = [f for f in os.listdir("installer_output") if f.endswith(".exe")]
        if installer_files:
            for installer in installer_files:
                size = os.path.getsize(f"installer_output/{installer}")
                print(f"  Installer: installer_output/{installer} ({size:,} bytes)")
    
    print("\n🚀 Next Steps:")
    print("  1. Test the installer on a clean Windows system")
    print("  2. Distribute the installer file to users")
    print("  3. Users can double-click the installer to install")
    
    print("\n📋 Files to distribute:")
    if os.path.exists("installer_output"):
        installer_files = [f for f in os.listdir("installer_output") if f.endswith(".exe")]
        for installer in installer_files:
            print(f"  • installer_output/{installer}")
    else:
        print("  • dist/DentalScheduler/ (entire folder)")

def main():
    """Main build process"""
    print_header("Dental Scheduler - Windows Installer Builder")
    
    start_time = time.time()
    
    # Run build steps
    steps = [
        check_prerequisites,
        create_icon,
        install_dependencies,
        clean_build_directories,
        build_with_pyinstaller,
        create_installer,
        test_build,
    ]
    
    for i, step in enumerate(steps, 1):
        try:
            if not step():
                print_error(f"Build failed at step {i}")
                return False
        except KeyboardInterrupt:
            print_error("Build cancelled by user")
            return False
        except Exception as e:
            print_error(f"Unexpected error in step {i}: {e}")
            return False
    
    # Calculate build time
    build_time = time.time() - start_time
    minutes = int(build_time // 60)
    seconds = int(build_time % 60)
    
    print_header("Build Completed Successfully! 🎉")
    print(f"Total build time: {minutes}m {seconds}s")
    
    print_summary()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nBuild cancelled by user")
        sys.exit(1)
