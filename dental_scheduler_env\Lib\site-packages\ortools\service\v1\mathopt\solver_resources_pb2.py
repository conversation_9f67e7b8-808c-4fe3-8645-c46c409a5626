# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/service/v1/mathopt/solver_resources.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/service/v1/mathopt/solver_resources.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n1ortools/service/v1/mathopt/solver_resources.proto\x12&operations_research.service.v1.mathopt\"J\n\x14SolverResourcesProto\x12\x10\n\x03\x63pu\x18\x01 \x01(\x01H\x00\x88\x01\x01\x12\x10\n\x03ram\x18\x02 \x01(\x01H\x01\x88\x01\x01\x42\x06\n\x04_cpuB\x06\n\x04_ramBB\n%com.google.ortools.service.v1.mathoptP\x01\xaa\x02\x16Google.OrTools.Serviceb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.service.v1.mathopt.solver_resources_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.google.ortools.service.v1.mathoptP\001\252\002\026Google.OrTools.Service'
  _globals['_SOLVERRESOURCESPROTO']._serialized_start=93
  _globals['_SOLVERRESOURCESPROTO']._serialized_end=167
# @@protoc_insertion_point(module_scope)
