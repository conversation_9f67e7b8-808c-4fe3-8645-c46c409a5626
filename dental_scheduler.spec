# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from pathlib import Path

# Get the directory containing this spec file
spec_root = os.path.dirname(os.path.abspath(SPEC))

# Define application details
app_name = "DentalScheduler"
app_version = "1.0.0"
app_description = "Dental Implant Manufacturing Scheduler"

# Data files to include
datas = [
    ('Job_Upload_Template.xlsx', '.'),
    ('README.md', '.'),
    ('PROJECT_STRUCTURE.md', '.'),
    ('INSTALLATION_GUIDE.md', '.'),
    ('requirements.txt', '.'),
]

# Hidden imports (modules that PyInstaller might miss)
hiddenimports = [
    'ortools',
    'ortools.sat',
    'ortools.sat.python',
    'ortools.sat.python.cp_model',
    'pandas',
    'plotly',
    'plotly.graph_objects',
    'plotly.express',
    'plotly.subplots',
    'customtkinter',
    'tkinter',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'openpyxl',
    'openpyxl.styles',
    'openpyxl.utils',
    'openpyxl.utils.dataframe',
    'openpyxl.worksheet',
    'openpyxl.worksheet.datavalidation',
    'collections',
    'datetime',
    'threading',
    'webbrowser',
    'subprocess',
    'json',
    'io',
    'contextlib',
    'traceback',
]

# GUI Application Analysis
gui_a = Analysis(
    ['dental_scheduler_gui.py'],
    pathex=[spec_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',  # We removed this dependency
        'numpy.random._pickle',  # Reduce size
        'numpy.random._bounded_integers',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# CLI Application Analysis  
cli_a = Analysis(
    ['Main.py'],
    pathex=[spec_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy.random._pickle',
        'numpy.random._bounded_integers',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Template Creator Analysis
template_a = Analysis(
    ['create_job_template.py'],
    pathex=[spec_root],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pandas',
        'openpyxl',
        'openpyxl.styles',
        'openpyxl.utils.dataframe',
        'datetime',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Merge all analyses
MERGE((gui_a, 'dental_scheduler_gui', 'dental_scheduler_gui'),
      (cli_a, 'dental_scheduler_cli', 'dental_scheduler_cli'),
      (template_a, 'template_creator', 'template_creator'))

# GUI Application PYZ and EXE
gui_pyz = PYZ(gui_a.pure, gui_a.zipped_data, cipher=None)

gui_exe = EXE(
    gui_pyz,
    gui_a.scripts,
    [],
    exclude_binaries=True,
    name='DentalSchedulerGUI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # No console window for GUI
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)

# CLI Application PYZ and EXE
cli_pyz = PYZ(cli_a.pure, cli_a.zipped_data, cipher=None)

cli_exe = EXE(
    cli_pyz,
    cli_a.scripts,
    [],
    exclude_binaries=True,
    name='DentalSchedulerCLI',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,  # Console window for CLI
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)

# Template Creator EXE
template_pyz = PYZ(template_a.pure, template_a.zipped_data, cipher=None)

template_exe = EXE(
    template_pyz,
    template_a.scripts,
    [],
    exclude_binaries=True,
    name='TemplateCreator',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# Collect all files into distribution directory
coll = COLLECT(
    gui_exe,
    gui_a.binaries,
    gui_a.zipfiles,
    gui_a.datas,
    cli_exe,
    cli_a.binaries,
    cli_a.zipfiles,
    cli_a.datas,
    template_exe,
    template_a.binaries,
    template_a.zipfiles,
    template_a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='DentalScheduler',
)
