"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Solver-Callback handling protos:
Callback functions allow for a fine-grain control, and ongoing interaction
with the solver during the solve process. The overall architecture is that
upon solve invocation the user will select what type of interactions to have
with the solver, and whenever the solver offers an interaction enabled by the
user, return the statistics collected up to that point, and any additional
information required by the user. Once done, the callback function must
return a message to the solver to continue with the solve process.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.duration_pb2
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import ortools.math_opt.sparse_containers_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _CallbackEventProto:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _CallbackEventProtoEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_CallbackEventProto.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    CALLBACK_EVENT_UNSPECIFIED: _CallbackEventProto.ValueType  # 0
    CALLBACK_EVENT_PRESOLVE: _CallbackEventProto.ValueType  # 1
    """The solver is currently running presolve.

    This event is supported by SOLVER_TYPE_GUROBI only.
    """
    CALLBACK_EVENT_SIMPLEX: _CallbackEventProto.ValueType  # 2
    """The solver is currently running the simplex method.

    This event is supported by SOLVER_TYPE_GUROBI only.
    """
    CALLBACK_EVENT_MIP: _CallbackEventProto.ValueType  # 3
    """The solver is in the MIP loop (called periodically before starting a new
    node). Useful for early termination. Note that this event does not provide
    information on LP relaxations nor about new incumbent solutions.

    This event is supported for MIP models by SOLVER_TYPE_GUROBI only.
    """
    CALLBACK_EVENT_MIP_SOLUTION: _CallbackEventProto.ValueType  # 4
    """Called every time a new MIP incumbent is found.

    This event is fully supported for MIP models by SOLVER_TYPE_GUROBI.
    SOLVER_TYPE_CP_SAT has partial support: you can view the solutions and
    request termination, but you cannot add lazy constraints. Other solvers
    don't support this event.
    """
    CALLBACK_EVENT_MIP_NODE: _CallbackEventProto.ValueType  # 5
    """Called inside a MIP node. Note that there is no guarantee that the
    callback function will be called on every node. That behavior is
    solver-dependent.

    Disabling cuts using SolveParametersProto may interfere with this event
    being called and/or adding cuts at this event, the behavior is solver
    specific.

    This event is supported for MIP models by SOLVER_TYPE_GUROBI only.
    """
    CALLBACK_EVENT_BARRIER: _CallbackEventProto.ValueType  # 6
    """Called in each iterate of an interior point/barrier method.

    This event is supported for SOLVER_TYPE_GUROBI only.
    """

class CallbackEventProto(_CallbackEventProto, metaclass=_CallbackEventProtoEnumTypeWrapper):
    """The supported events during a solve for callbacks."""

CALLBACK_EVENT_UNSPECIFIED: CallbackEventProto.ValueType  # 0
CALLBACK_EVENT_PRESOLVE: CallbackEventProto.ValueType  # 1
"""The solver is currently running presolve.

This event is supported by SOLVER_TYPE_GUROBI only.
"""
CALLBACK_EVENT_SIMPLEX: CallbackEventProto.ValueType  # 2
"""The solver is currently running the simplex method.

This event is supported by SOLVER_TYPE_GUROBI only.
"""
CALLBACK_EVENT_MIP: CallbackEventProto.ValueType  # 3
"""The solver is in the MIP loop (called periodically before starting a new
node). Useful for early termination. Note that this event does not provide
information on LP relaxations nor about new incumbent solutions.

This event is supported for MIP models by SOLVER_TYPE_GUROBI only.
"""
CALLBACK_EVENT_MIP_SOLUTION: CallbackEventProto.ValueType  # 4
"""Called every time a new MIP incumbent is found.

This event is fully supported for MIP models by SOLVER_TYPE_GUROBI.
SOLVER_TYPE_CP_SAT has partial support: you can view the solutions and
request termination, but you cannot add lazy constraints. Other solvers
don't support this event.
"""
CALLBACK_EVENT_MIP_NODE: CallbackEventProto.ValueType  # 5
"""Called inside a MIP node. Note that there is no guarantee that the
callback function will be called on every node. That behavior is
solver-dependent.

Disabling cuts using SolveParametersProto may interfere with this event
being called and/or adding cuts at this event, the behavior is solver
specific.

This event is supported for MIP models by SOLVER_TYPE_GUROBI only.
"""
CALLBACK_EVENT_BARRIER: CallbackEventProto.ValueType  # 6
"""Called in each iterate of an interior point/barrier method.

This event is supported for SOLVER_TYPE_GUROBI only.
"""
global___CallbackEventProto = CallbackEventProto

@typing.final
class CallbackDataProto(google.protobuf.message.Message):
    """The callback function input data.
    Note that depending on the event, some information might be unavailable.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class PresolveStats(google.protobuf.message.Message):
        """Presolve stats. Only available during CALLBACK_EVENT_PRESOLVE."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        REMOVED_VARIABLES_FIELD_NUMBER: builtins.int
        REMOVED_CONSTRAINTS_FIELD_NUMBER: builtins.int
        BOUND_CHANGES_FIELD_NUMBER: builtins.int
        COEFFICIENT_CHANGES_FIELD_NUMBER: builtins.int
        removed_variables: builtins.int
        removed_constraints: builtins.int
        bound_changes: builtins.int
        coefficient_changes: builtins.int
        def __init__(
            self,
            *,
            removed_variables: builtins.int | None = ...,
            removed_constraints: builtins.int | None = ...,
            bound_changes: builtins.int | None = ...,
            coefficient_changes: builtins.int | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["_bound_changes", b"_bound_changes", "_coefficient_changes", b"_coefficient_changes", "_removed_constraints", b"_removed_constraints", "_removed_variables", b"_removed_variables", "bound_changes", b"bound_changes", "coefficient_changes", b"coefficient_changes", "removed_constraints", b"removed_constraints", "removed_variables", b"removed_variables"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["_bound_changes", b"_bound_changes", "_coefficient_changes", b"_coefficient_changes", "_removed_constraints", b"_removed_constraints", "_removed_variables", b"_removed_variables", "bound_changes", b"bound_changes", "coefficient_changes", b"coefficient_changes", "removed_constraints", b"removed_constraints", "removed_variables", b"removed_variables"]) -> None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_bound_changes", b"_bound_changes"]) -> typing.Literal["bound_changes"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_coefficient_changes", b"_coefficient_changes"]) -> typing.Literal["coefficient_changes"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_removed_constraints", b"_removed_constraints"]) -> typing.Literal["removed_constraints"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_removed_variables", b"_removed_variables"]) -> typing.Literal["removed_variables"] | None: ...

    @typing.final
    class SimplexStats(google.protobuf.message.Message):
        """Simplex stats. Only available during CALLBACK_EVENT_SIMPLEX."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        ITERATION_COUNT_FIELD_NUMBER: builtins.int
        OBJECTIVE_VALUE_FIELD_NUMBER: builtins.int
        PRIMAL_INFEASIBILITY_FIELD_NUMBER: builtins.int
        DUAL_INFEASIBILITY_FIELD_NUMBER: builtins.int
        IS_PERTUBATED_FIELD_NUMBER: builtins.int
        iteration_count: builtins.int
        objective_value: builtins.float
        primal_infeasibility: builtins.float
        dual_infeasibility: builtins.float
        is_pertubated: builtins.bool
        def __init__(
            self,
            *,
            iteration_count: builtins.int | None = ...,
            objective_value: builtins.float | None = ...,
            primal_infeasibility: builtins.float | None = ...,
            dual_infeasibility: builtins.float | None = ...,
            is_pertubated: builtins.bool | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["_dual_infeasibility", b"_dual_infeasibility", "_is_pertubated", b"_is_pertubated", "_iteration_count", b"_iteration_count", "_objective_value", b"_objective_value", "_primal_infeasibility", b"_primal_infeasibility", "dual_infeasibility", b"dual_infeasibility", "is_pertubated", b"is_pertubated", "iteration_count", b"iteration_count", "objective_value", b"objective_value", "primal_infeasibility", b"primal_infeasibility"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["_dual_infeasibility", b"_dual_infeasibility", "_is_pertubated", b"_is_pertubated", "_iteration_count", b"_iteration_count", "_objective_value", b"_objective_value", "_primal_infeasibility", b"_primal_infeasibility", "dual_infeasibility", b"dual_infeasibility", "is_pertubated", b"is_pertubated", "iteration_count", b"iteration_count", "objective_value", b"objective_value", "primal_infeasibility", b"primal_infeasibility"]) -> None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_dual_infeasibility", b"_dual_infeasibility"]) -> typing.Literal["dual_infeasibility"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_is_pertubated", b"_is_pertubated"]) -> typing.Literal["is_pertubated"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_iteration_count", b"_iteration_count"]) -> typing.Literal["iteration_count"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_objective_value", b"_objective_value"]) -> typing.Literal["objective_value"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_primal_infeasibility", b"_primal_infeasibility"]) -> typing.Literal["primal_infeasibility"] | None: ...

    @typing.final
    class BarrierStats(google.protobuf.message.Message):
        """Barrier stats. Only available during CALLBACK_EVENT_BARRIER."""

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        ITERATION_COUNT_FIELD_NUMBER: builtins.int
        PRIMAL_OBJECTIVE_FIELD_NUMBER: builtins.int
        DUAL_OBJECTIVE_FIELD_NUMBER: builtins.int
        COMPLEMENTARITY_FIELD_NUMBER: builtins.int
        PRIMAL_INFEASIBILITY_FIELD_NUMBER: builtins.int
        DUAL_INFEASIBILITY_FIELD_NUMBER: builtins.int
        iteration_count: builtins.int
        primal_objective: builtins.float
        dual_objective: builtins.float
        complementarity: builtins.float
        primal_infeasibility: builtins.float
        dual_infeasibility: builtins.float
        def __init__(
            self,
            *,
            iteration_count: builtins.int | None = ...,
            primal_objective: builtins.float | None = ...,
            dual_objective: builtins.float | None = ...,
            complementarity: builtins.float | None = ...,
            primal_infeasibility: builtins.float | None = ...,
            dual_infeasibility: builtins.float | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["_complementarity", b"_complementarity", "_dual_infeasibility", b"_dual_infeasibility", "_dual_objective", b"_dual_objective", "_iteration_count", b"_iteration_count", "_primal_infeasibility", b"_primal_infeasibility", "_primal_objective", b"_primal_objective", "complementarity", b"complementarity", "dual_infeasibility", b"dual_infeasibility", "dual_objective", b"dual_objective", "iteration_count", b"iteration_count", "primal_infeasibility", b"primal_infeasibility", "primal_objective", b"primal_objective"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["_complementarity", b"_complementarity", "_dual_infeasibility", b"_dual_infeasibility", "_dual_objective", b"_dual_objective", "_iteration_count", b"_iteration_count", "_primal_infeasibility", b"_primal_infeasibility", "_primal_objective", b"_primal_objective", "complementarity", b"complementarity", "dual_infeasibility", b"dual_infeasibility", "dual_objective", b"dual_objective", "iteration_count", b"iteration_count", "primal_infeasibility", b"primal_infeasibility", "primal_objective", b"primal_objective"]) -> None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_complementarity", b"_complementarity"]) -> typing.Literal["complementarity"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_dual_infeasibility", b"_dual_infeasibility"]) -> typing.Literal["dual_infeasibility"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_dual_objective", b"_dual_objective"]) -> typing.Literal["dual_objective"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_iteration_count", b"_iteration_count"]) -> typing.Literal["iteration_count"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_primal_infeasibility", b"_primal_infeasibility"]) -> typing.Literal["primal_infeasibility"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_primal_objective", b"_primal_objective"]) -> typing.Literal["primal_objective"] | None: ...

    @typing.final
    class MipStats(google.protobuf.message.Message):
        """MIP B&B stats. Only available during CALLBACK_EVENT_MIPxxxx events.
        Not supported for CP-SAT.
        """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        PRIMAL_BOUND_FIELD_NUMBER: builtins.int
        DUAL_BOUND_FIELD_NUMBER: builtins.int
        EXPLORED_NODES_FIELD_NUMBER: builtins.int
        OPEN_NODES_FIELD_NUMBER: builtins.int
        SIMPLEX_ITERATIONS_FIELD_NUMBER: builtins.int
        NUMBER_OF_SOLUTIONS_FOUND_FIELD_NUMBER: builtins.int
        CUTTING_PLANES_IN_LP_FIELD_NUMBER: builtins.int
        primal_bound: builtins.float
        dual_bound: builtins.float
        explored_nodes: builtins.int
        open_nodes: builtins.int
        simplex_iterations: builtins.int
        number_of_solutions_found: builtins.int
        cutting_planes_in_lp: builtins.int
        def __init__(
            self,
            *,
            primal_bound: builtins.float | None = ...,
            dual_bound: builtins.float | None = ...,
            explored_nodes: builtins.int | None = ...,
            open_nodes: builtins.int | None = ...,
            simplex_iterations: builtins.int | None = ...,
            number_of_solutions_found: builtins.int | None = ...,
            cutting_planes_in_lp: builtins.int | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["_cutting_planes_in_lp", b"_cutting_planes_in_lp", "_dual_bound", b"_dual_bound", "_explored_nodes", b"_explored_nodes", "_number_of_solutions_found", b"_number_of_solutions_found", "_open_nodes", b"_open_nodes", "_primal_bound", b"_primal_bound", "_simplex_iterations", b"_simplex_iterations", "cutting_planes_in_lp", b"cutting_planes_in_lp", "dual_bound", b"dual_bound", "explored_nodes", b"explored_nodes", "number_of_solutions_found", b"number_of_solutions_found", "open_nodes", b"open_nodes", "primal_bound", b"primal_bound", "simplex_iterations", b"simplex_iterations"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["_cutting_planes_in_lp", b"_cutting_planes_in_lp", "_dual_bound", b"_dual_bound", "_explored_nodes", b"_explored_nodes", "_number_of_solutions_found", b"_number_of_solutions_found", "_open_nodes", b"_open_nodes", "_primal_bound", b"_primal_bound", "_simplex_iterations", b"_simplex_iterations", "cutting_planes_in_lp", b"cutting_planes_in_lp", "dual_bound", b"dual_bound", "explored_nodes", b"explored_nodes", "number_of_solutions_found", b"number_of_solutions_found", "open_nodes", b"open_nodes", "primal_bound", b"primal_bound", "simplex_iterations", b"simplex_iterations"]) -> None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_cutting_planes_in_lp", b"_cutting_planes_in_lp"]) -> typing.Literal["cutting_planes_in_lp"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_dual_bound", b"_dual_bound"]) -> typing.Literal["dual_bound"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_explored_nodes", b"_explored_nodes"]) -> typing.Literal["explored_nodes"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_number_of_solutions_found", b"_number_of_solutions_found"]) -> typing.Literal["number_of_solutions_found"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_open_nodes", b"_open_nodes"]) -> typing.Literal["open_nodes"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_primal_bound", b"_primal_bound"]) -> typing.Literal["primal_bound"] | None: ...
        @typing.overload
        def WhichOneof(self, oneof_group: typing.Literal["_simplex_iterations", b"_simplex_iterations"]) -> typing.Literal["simplex_iterations"] | None: ...

    EVENT_FIELD_NUMBER: builtins.int
    PRIMAL_SOLUTION_VECTOR_FIELD_NUMBER: builtins.int
    RUNTIME_FIELD_NUMBER: builtins.int
    PRESOLVE_STATS_FIELD_NUMBER: builtins.int
    SIMPLEX_STATS_FIELD_NUMBER: builtins.int
    BARRIER_STATS_FIELD_NUMBER: builtins.int
    MIP_STATS_FIELD_NUMBER: builtins.int
    event: global___CallbackEventProto.ValueType
    @property
    def primal_solution_vector(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
        """if event == CALLBACK_EVENT_MIP_NODE, the primal_solution_vector contains
           the variable values of the primal solution for the current LP-node
           relaxation. In some cases, no solution will be available (e.g. because
           LP was infeasible or the solve was imprecise).
        if event == CALLBACK_EVENT_MIP_SOLUTION, the primal_solution_vector
           contains variable values for the newly found primal (integer) feasible
           solution.
        Otherwise, the primal_solution_vector is not available.

        Note that, because of variable filters, it is possible that when a solution
        is found, it is empty. The message will be set but left empty in this case,
        while it will be unset when no solution is available.
        """

    @property
    def runtime(self) -> google.protobuf.duration_pb2.Duration:
        """Running time since the `Solve` call."""

    @property
    def presolve_stats(self) -> global___CallbackDataProto.PresolveStats: ...
    @property
    def simplex_stats(self) -> global___CallbackDataProto.SimplexStats: ...
    @property
    def barrier_stats(self) -> global___CallbackDataProto.BarrierStats: ...
    @property
    def mip_stats(self) -> global___CallbackDataProto.MipStats: ...
    def __init__(
        self,
        *,
        event: global___CallbackEventProto.ValueType = ...,
        primal_solution_vector: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
        runtime: google.protobuf.duration_pb2.Duration | None = ...,
        presolve_stats: global___CallbackDataProto.PresolveStats | None = ...,
        simplex_stats: global___CallbackDataProto.SimplexStats | None = ...,
        barrier_stats: global___CallbackDataProto.BarrierStats | None = ...,
        mip_stats: global___CallbackDataProto.MipStats | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["barrier_stats", b"barrier_stats", "mip_stats", b"mip_stats", "presolve_stats", b"presolve_stats", "primal_solution_vector", b"primal_solution_vector", "runtime", b"runtime", "simplex_stats", b"simplex_stats"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["barrier_stats", b"barrier_stats", "event", b"event", "mip_stats", b"mip_stats", "presolve_stats", b"presolve_stats", "primal_solution_vector", b"primal_solution_vector", "runtime", b"runtime", "simplex_stats", b"simplex_stats"]) -> None: ...

global___CallbackDataProto = CallbackDataProto

@typing.final
class CallbackResultProto(google.protobuf.message.Message):
    """Return value of a callback function."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class GeneratedLinearConstraint(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        LINEAR_EXPRESSION_FIELD_NUMBER: builtins.int
        LOWER_BOUND_FIELD_NUMBER: builtins.int
        UPPER_BOUND_FIELD_NUMBER: builtins.int
        IS_LAZY_FIELD_NUMBER: builtins.int
        lower_bound: builtins.float
        upper_bound: builtins.float
        is_lazy: builtins.bool
        """Two types of generated linear constraints are supported based on is_lazy:
          * The "lazy constraint" can remove integer points from the feasible
            region and can be added at event CALLBACK_EVENT_MIP_NODE or
            CALLBACK_EVENT_MIP_SOLUTION
          * The "user cut" (on is_lazy=false) strengthens the LP without removing
            integer points. It can only be added at CALLBACK_EVENT_MIP_NODE.
        """
        @property
        def linear_expression(self) -> ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto:
            """This message encode linear constraints of the form
            lower_bound <= linear_expression <= upper_bound
            """

        def __init__(
            self,
            *,
            linear_expression: ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto | None = ...,
            lower_bound: builtins.float = ...,
            upper_bound: builtins.float = ...,
            is_lazy: builtins.bool = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["linear_expression", b"linear_expression"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["is_lazy", b"is_lazy", "linear_expression", b"linear_expression", "lower_bound", b"lower_bound", "upper_bound", b"upper_bound"]) -> None: ...

    TERMINATE_FIELD_NUMBER: builtins.int
    CUTS_FIELD_NUMBER: builtins.int
    SUGGESTED_SOLUTIONS_FIELD_NUMBER: builtins.int
    terminate: builtins.bool
    """When true it tells the solver to interrupt the solve as soon as possible.

    It can be set from any event. This is equivalent to using a
    SolveInterrupter and triggering it from the callback.

    Some solvers don't support interruption, in that case this is simply
    ignored and the solve terminates as usual. On top of that solvers may not
    immediately stop the solve. Thus the user should expect the callback to
    still be called after they set `terminate` to true in a previous
    call. Returning with `terminate` false after having previously returned
    true won't cancel the interruption.
    """
    @property
    def cuts(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___CallbackResultProto.GeneratedLinearConstraint]:
        """TODO(b/172214608): SCIP allows to reject a feasible solution without
        providing a cut. This is something we might support at a later stage.

        Dynamically generated linear constraints to add to the MIP. See
        GeneratedLinearConstraint::is_lazy for details.
        """

    @property
    def suggested_solutions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto]:
        """Use only for CALLBACK_EVENT_MIP_NODE.

        Note that some solvers (e.g. Gurobi) support partially-defined solutions.
        The most common use case is to specify a value for each variable in the
        model. If a variable is not present in the primal solution, its value is
        taken to be undefined, and is up to the underlying solver to deal with it.
        For example, Gurobi will try to solve a Sub-MIP to get a fully feasible
        solution if necessary.
        """

    def __init__(
        self,
        *,
        terminate: builtins.bool = ...,
        cuts: collections.abc.Iterable[global___CallbackResultProto.GeneratedLinearConstraint] | None = ...,
        suggested_solutions: collections.abc.Iterable[ortools.math_opt.sparse_containers_pb2.SparseDoubleVectorProto] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["cuts", b"cuts", "suggested_solutions", b"suggested_solutions", "terminate", b"terminate"]) -> None: ...

global___CallbackResultProto = CallbackResultProto

@typing.final
class CallbackRegistrationProto(google.protobuf.message.Message):
    """Provided with a callback at the start of a Solve() to inform the solver:
      * what information the callback needs,
      * how the callback might alter the solve process.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REQUEST_REGISTRATION_FIELD_NUMBER: builtins.int
    MIP_SOLUTION_FILTER_FIELD_NUMBER: builtins.int
    MIP_NODE_FILTER_FIELD_NUMBER: builtins.int
    ADD_CUTS_FIELD_NUMBER: builtins.int
    ADD_LAZY_CONSTRAINTS_FIELD_NUMBER: builtins.int
    add_cuts: builtins.bool
    """////////////////////////////////////////////////////////////////////////////
    What might you do in your callback (typically some solver features need
    to be disabled before the solve starts to support these features).
    ////////////////////////////////////////////////////////////////////////////

    Dynamically add linear constraints that strength the formulation but do not
    exclude integer points during CALLBACK_EVENT_MIP_NODE events.
    """
    add_lazy_constraints: builtins.bool
    """Dynamically add linear constraints that exclude integer points during
    CALLBACK_EVENT_MIP_NODE and/or CALLBACK_EVENT_MIP_SOLUTION events.
    """
    @property
    def request_registration(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[global___CallbackEventProto.ValueType]:
        """The events the solver should invoke the callback at.

        When a solver is called with registered events that are not supported,
        an InvalidArgument is returned. The supported events may depend on the
        model. For example registering for CALLBACK_EVENT_MIP with a model that
        only contains continuous variables will fail for most solvers. See the
        documentation of each event to see their supported solvers/model types.
        """

    @property
    def mip_solution_filter(self) -> ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto:
        """If CALLBACK_EVENT_MIP_SOLUTION is in `request_registration`, then
        the returned primal_solution information will be filtered according to
        this rule.
        """

    @property
    def mip_node_filter(self) -> ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto:
        """If CALLBCK_EVENT_MIP_NODE is in `request_registration`, then the
        returned primal_solution information will be filtered according to this
        rule.
        """

    def __init__(
        self,
        *,
        request_registration: collections.abc.Iterable[global___CallbackEventProto.ValueType] | None = ...,
        mip_solution_filter: ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto | None = ...,
        mip_node_filter: ortools.math_opt.sparse_containers_pb2.SparseVectorFilterProto | None = ...,
        add_cuts: builtins.bool = ...,
        add_lazy_constraints: builtins.bool = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["mip_node_filter", b"mip_node_filter", "mip_solution_filter", b"mip_solution_filter"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["add_cuts", b"add_cuts", "add_lazy_constraints", b"add_lazy_constraints", "mip_node_filter", b"mip_node_filter", "mip_solution_filter", b"mip_solution_filter", "request_registration", b"request_registration"]) -> None: ...

global___CallbackRegistrationProto = CallbackRegistrationProto
