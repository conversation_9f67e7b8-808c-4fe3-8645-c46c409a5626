"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
Copyright 2010-2025 Google LLC
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import ortools.glop.parameters_pb2
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

class _OptimalityNorm:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _OptimalityNormEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_OptimalityNorm.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    OPTIMALITY_NORM_UNSPECIFIED: _OptimalityNorm.ValueType  # 0
    OPTIMALITY_NORM_L_INF: _OptimalityNorm.ValueType  # 1
    """The infinity norm."""
    OPTIMALITY_NORM_L2: _OptimalityNorm.ValueType  # 2
    """The Euclidean norm."""
    OPTIMALITY_NORM_L_INF_COMPONENTWISE: _OptimalityNorm.ValueType  # 3
    """The infinity norm of component-wise relative errors offset by the ratio of
    the absolute and relative error tolerances, i.e., the l_∞ norm of
    [residual / (eps_ratio + |bound|)], where eps_ratio =
    eps_optimal_{X}_residual_absolute / eps_optimal_{X}_residual_relative
    where {X} is either primal or dual, and bound is the corresponding primal
    or dual bound (that is, the violated constraint bound for primal residuals,
    and the objective coefficient for dual residuals).
    Using eps_ratio in this norm means that if the norm is <=
    eps_optimal_{X}_residual_relative, then the residuals satisfy
    residual <= eps_optimal_{X}_residual_absolute
              + eps_optimal_{X}_residual_relative * |bound|
    """

class OptimalityNorm(_OptimalityNorm, metaclass=_OptimalityNormEnumTypeWrapper): ...

OPTIMALITY_NORM_UNSPECIFIED: OptimalityNorm.ValueType  # 0
OPTIMALITY_NORM_L_INF: OptimalityNorm.ValueType  # 1
"""The infinity norm."""
OPTIMALITY_NORM_L2: OptimalityNorm.ValueType  # 2
"""The Euclidean norm."""
OPTIMALITY_NORM_L_INF_COMPONENTWISE: OptimalityNorm.ValueType  # 3
"""The infinity norm of component-wise relative errors offset by the ratio of
the absolute and relative error tolerances, i.e., the l_∞ norm of
[residual / (eps_ratio + |bound|)], where eps_ratio =
eps_optimal_{X}_residual_absolute / eps_optimal_{X}_residual_relative
where {X} is either primal or dual, and bound is the corresponding primal
or dual bound (that is, the violated constraint bound for primal residuals,
and the objective coefficient for dual residuals).
Using eps_ratio in this norm means that if the norm is <=
eps_optimal_{X}_residual_relative, then the residuals satisfy
residual <= eps_optimal_{X}_residual_absolute
          + eps_optimal_{X}_residual_relative * |bound|
"""
global___OptimalityNorm = OptimalityNorm

class _SchedulerType:
    ValueType = typing.NewType("ValueType", builtins.int)
    V: typing_extensions.TypeAlias = ValueType

class _SchedulerTypeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[_SchedulerType.ValueType], builtins.type):
    DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
    SCHEDULER_TYPE_UNSPECIFIED: _SchedulerType.ValueType  # 0
    SCHEDULER_TYPE_GOOGLE_THREADPOOL: _SchedulerType.ValueType  # 1
    """Google ThreadPool with barrier synchronization."""
    SCHEDULER_TYPE_EIGEN_THREADPOOL: _SchedulerType.ValueType  # 3
    """Eigen non-blocking ThreadPool with barrier synchronization (see
    <Eigen/ThreadPool>).
    """

class SchedulerType(_SchedulerType, metaclass=_SchedulerTypeEnumTypeWrapper):
    """The type of system used to schedule CPU threads to do work in parallel."""

SCHEDULER_TYPE_UNSPECIFIED: SchedulerType.ValueType  # 0
SCHEDULER_TYPE_GOOGLE_THREADPOOL: SchedulerType.ValueType  # 1
"""Google ThreadPool with barrier synchronization."""
SCHEDULER_TYPE_EIGEN_THREADPOOL: SchedulerType.ValueType  # 3
"""Eigen non-blocking ThreadPool with barrier synchronization (see
<Eigen/ThreadPool>).
"""
global___SchedulerType = SchedulerType

@typing.final
class TerminationCriteria(google.protobuf.message.Message):
    """A description of solver termination criteria. The criteria are defined in
    terms of the quantities recorded in IterationStats in solve_log.proto.

    Relevant readings on infeasibility certificates:
    (1) https://docs.mosek.com/modeling-cookbook/qcqo.html provides references
    explaining why the primal rays imply dual infeasibility and dual rays imply
    primal infeasibility.
    (2) The termination criteria for Mosek's linear programming optimizer
    https://docs.mosek.com/9.0/pythonfusion/solving-linear.html.
    (3) The termination criteria for OSQP is in section 3.3 of
    https://web.stanford.edu/~boyd/papers/pdf/osqp.pdf.
    (4) The termination criteria for SCS is in section 3.5 of
    https://arxiv.org/pdf/1312.3039.pdf.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class SimpleOptimalityCriteria(google.protobuf.message.Message):
        """When using DetailedOptimalityCriteria the conditions to declare a solution
        optimal are:
        | primal_objective - dual_objective | <=
            eps_optimal_objective_gap_absolute +
            eps_optimal_objective_gap_relative *
                ( | primal_objective | + | dual_objective | )
        If optimality_norm is OPTIMALITY_NORM_L_INF or OPTIMALITY_NORM_L2 (where
        norm(x, p) is the l_∞ or l_2 norm):
        norm(primal_residual, p) <=
            eps_optimal_primal_residual_absolute +
            eps_optimal_primal_residual_relative * norm(b^c, p)
        norm(dual_residual, p) <=
            eps_optimal_dual_residual_absolute +
            eps_optimal_dual_residual_relative * norm(c, p)
        Otherwise, if optimality_norm is OPTIMALITY_NORM_L_INF_COMPONENTWISE, then,
        for all i:
        primal_residual[i] <=
            eps_optimal_primal_residual_absolute +
            eps_optimal_primal_residual_relative * |violated_bound(l^c, u^c, i)|
        dual_residual[i] <=
            eps_optimal_dual_residual_absolute +
            eps_optimal_dual_residual_relative * |c[i]|
        It is possible to prove that a solution satisfying the above conditions
        for L_INF and L_2 norms also satisfies SCS's optimality conditions (see
        link above) with
        ϵ_pri = ϵ_dual = ϵ_gap = eps_optimal_*_absolute = eps_optimal_*_relative.
        (ϵ_pri, ϵ_dual, and ϵ_gap are SCS's parameters).
        When using SimpleOptimalityCriteria all the eps_optimal_*_absolute have the
        same value eps_optimal_absolute and all the eps_optimal_*_relative have the
        same value eps_optimal_relative.
        """

        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        EPS_OPTIMAL_ABSOLUTE_FIELD_NUMBER: builtins.int
        EPS_OPTIMAL_RELATIVE_FIELD_NUMBER: builtins.int
        eps_optimal_absolute: builtins.float
        """Absolute tolerance on the primal residual, dual residual, and objective
        gap.
        """
        eps_optimal_relative: builtins.float
        """Relative tolerance on the primal residual, dual residual, and objective
        gap.
        """
        def __init__(
            self,
            *,
            eps_optimal_absolute: builtins.float | None = ...,
            eps_optimal_relative: builtins.float | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["eps_optimal_absolute", b"eps_optimal_absolute", "eps_optimal_relative", b"eps_optimal_relative"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["eps_optimal_absolute", b"eps_optimal_absolute", "eps_optimal_relative", b"eps_optimal_relative"]) -> None: ...

    @typing.final
    class DetailedOptimalityCriteria(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        EPS_OPTIMAL_PRIMAL_RESIDUAL_ABSOLUTE_FIELD_NUMBER: builtins.int
        EPS_OPTIMAL_PRIMAL_RESIDUAL_RELATIVE_FIELD_NUMBER: builtins.int
        EPS_OPTIMAL_DUAL_RESIDUAL_ABSOLUTE_FIELD_NUMBER: builtins.int
        EPS_OPTIMAL_DUAL_RESIDUAL_RELATIVE_FIELD_NUMBER: builtins.int
        EPS_OPTIMAL_OBJECTIVE_GAP_ABSOLUTE_FIELD_NUMBER: builtins.int
        EPS_OPTIMAL_OBJECTIVE_GAP_RELATIVE_FIELD_NUMBER: builtins.int
        eps_optimal_primal_residual_absolute: builtins.float
        """Absolute tolerance on the primal residual."""
        eps_optimal_primal_residual_relative: builtins.float
        """Relative tolerance on the primal residual."""
        eps_optimal_dual_residual_absolute: builtins.float
        """Absolute tolerance on the dual residual."""
        eps_optimal_dual_residual_relative: builtins.float
        """Relative tolerance on the dual residual."""
        eps_optimal_objective_gap_absolute: builtins.float
        """Absolute tolerance on the objective gap."""
        eps_optimal_objective_gap_relative: builtins.float
        """Relative tolerance on the objective gap."""
        def __init__(
            self,
            *,
            eps_optimal_primal_residual_absolute: builtins.float | None = ...,
            eps_optimal_primal_residual_relative: builtins.float | None = ...,
            eps_optimal_dual_residual_absolute: builtins.float | None = ...,
            eps_optimal_dual_residual_relative: builtins.float | None = ...,
            eps_optimal_objective_gap_absolute: builtins.float | None = ...,
            eps_optimal_objective_gap_relative: builtins.float | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["eps_optimal_dual_residual_absolute", b"eps_optimal_dual_residual_absolute", "eps_optimal_dual_residual_relative", b"eps_optimal_dual_residual_relative", "eps_optimal_objective_gap_absolute", b"eps_optimal_objective_gap_absolute", "eps_optimal_objective_gap_relative", b"eps_optimal_objective_gap_relative", "eps_optimal_primal_residual_absolute", b"eps_optimal_primal_residual_absolute", "eps_optimal_primal_residual_relative", b"eps_optimal_primal_residual_relative"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["eps_optimal_dual_residual_absolute", b"eps_optimal_dual_residual_absolute", "eps_optimal_dual_residual_relative", b"eps_optimal_dual_residual_relative", "eps_optimal_objective_gap_absolute", b"eps_optimal_objective_gap_absolute", "eps_optimal_objective_gap_relative", b"eps_optimal_objective_gap_relative", "eps_optimal_primal_residual_absolute", b"eps_optimal_primal_residual_absolute", "eps_optimal_primal_residual_relative", b"eps_optimal_primal_residual_relative"]) -> None: ...

    OPTIMALITY_NORM_FIELD_NUMBER: builtins.int
    SIMPLE_OPTIMALITY_CRITERIA_FIELD_NUMBER: builtins.int
    DETAILED_OPTIMALITY_CRITERIA_FIELD_NUMBER: builtins.int
    EPS_OPTIMAL_ABSOLUTE_FIELD_NUMBER: builtins.int
    EPS_OPTIMAL_RELATIVE_FIELD_NUMBER: builtins.int
    EPS_PRIMAL_INFEASIBLE_FIELD_NUMBER: builtins.int
    EPS_DUAL_INFEASIBLE_FIELD_NUMBER: builtins.int
    TIME_SEC_LIMIT_FIELD_NUMBER: builtins.int
    ITERATION_LIMIT_FIELD_NUMBER: builtins.int
    KKT_MATRIX_PASS_LIMIT_FIELD_NUMBER: builtins.int
    optimality_norm: global___OptimalityNorm.ValueType
    """The norm that we are measuring the optimality criteria in."""
    eps_optimal_absolute: builtins.float
    """Absolute tolerance on primal residual, dual residual, and the objective
    gap.
    Deprecated, use simple_optimality_criteria instead.
    TODO(b/241462829) delete this deprecated field.
    """
    eps_optimal_relative: builtins.float
    """Relative tolerance on primal residual, dual residual, and the objective
    gap.
    Deprecated, use simple_optimality_criteria instead.
    TODO(b/241462829) delete this deprecated field.
    """
    eps_primal_infeasible: builtins.float
    """If the following two conditions hold we say that we have obtained an
    approximate dual ray, which is an approximate certificate of primal
    infeasibility.
    (1) dual_ray_objective > 0,
    (2) max_dual_ray_infeasibility / dual_ray_objective <=
    eps_primal_infeasible.
    """
    eps_dual_infeasible: builtins.float
    """If the following three conditions hold we say we have obtained an
    approximate primal ray, which is an approximate certificate of dual
    infeasibility.
    (1) primal_ray_linear_objective < 0,
    (2) max_primal_ray_infeasibility / (-primal_ray_linear_objective) <=
    eps_dual_infeasible
    (3) primal_ray_quadratic_norm / (-primal_ray_linear_objective) <=
    eps_dual_infeasible.
    """
    time_sec_limit: builtins.float
    """If termination_reason = TERMINATION_REASON_TIME_LIMIT then the solver has
    taken at least time_sec_limit time.
    """
    iteration_limit: builtins.int
    """If termination_reason = TERMINATION_REASON_ITERATION_LIMIT then the solver
    has taken at least iterations_limit iterations.
    """
    kkt_matrix_pass_limit: builtins.float
    """If termination_reason = TERMINATION_REASON_KKT_MATRIX_PASS_LIMIT then
    cumulative_kkt_matrix_passes is at least kkt_pass_limit.
    """
    @property
    def simple_optimality_criteria(self) -> global___TerminationCriteria.SimpleOptimalityCriteria: ...
    @property
    def detailed_optimality_criteria(self) -> global___TerminationCriteria.DetailedOptimalityCriteria: ...
    def __init__(
        self,
        *,
        optimality_norm: global___OptimalityNorm.ValueType | None = ...,
        simple_optimality_criteria: global___TerminationCriteria.SimpleOptimalityCriteria | None = ...,
        detailed_optimality_criteria: global___TerminationCriteria.DetailedOptimalityCriteria | None = ...,
        eps_optimal_absolute: builtins.float | None = ...,
        eps_optimal_relative: builtins.float | None = ...,
        eps_primal_infeasible: builtins.float | None = ...,
        eps_dual_infeasible: builtins.float | None = ...,
        time_sec_limit: builtins.float | None = ...,
        iteration_limit: builtins.int | None = ...,
        kkt_matrix_pass_limit: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["detailed_optimality_criteria", b"detailed_optimality_criteria", "eps_dual_infeasible", b"eps_dual_infeasible", "eps_optimal_absolute", b"eps_optimal_absolute", "eps_optimal_relative", b"eps_optimal_relative", "eps_primal_infeasible", b"eps_primal_infeasible", "iteration_limit", b"iteration_limit", "kkt_matrix_pass_limit", b"kkt_matrix_pass_limit", "optimality_criteria", b"optimality_criteria", "optimality_norm", b"optimality_norm", "simple_optimality_criteria", b"simple_optimality_criteria", "time_sec_limit", b"time_sec_limit"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["detailed_optimality_criteria", b"detailed_optimality_criteria", "eps_dual_infeasible", b"eps_dual_infeasible", "eps_optimal_absolute", b"eps_optimal_absolute", "eps_optimal_relative", b"eps_optimal_relative", "eps_primal_infeasible", b"eps_primal_infeasible", "iteration_limit", b"iteration_limit", "kkt_matrix_pass_limit", b"kkt_matrix_pass_limit", "optimality_criteria", b"optimality_criteria", "optimality_norm", b"optimality_norm", "simple_optimality_criteria", b"simple_optimality_criteria", "time_sec_limit", b"time_sec_limit"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["optimality_criteria", b"optimality_criteria"]) -> typing.Literal["simple_optimality_criteria", "detailed_optimality_criteria"] | None: ...

global___TerminationCriteria = TerminationCriteria

@typing.final
class AdaptiveLinesearchParams(google.protobuf.message.Message):
    """At the end of each iteration, regardless of whether the step was accepted
    or not, the adaptive rule updates the step_size as the minimum of two
    potential step sizes defined by the following two exponents.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STEP_SIZE_REDUCTION_EXPONENT_FIELD_NUMBER: builtins.int
    STEP_SIZE_GROWTH_EXPONENT_FIELD_NUMBER: builtins.int
    step_size_reduction_exponent: builtins.float
    """The step size reduction exponent defines a step size given by
    (1 - (total_steps_attempted + 1)^(-step_size_reduction_exponent)) *
    step_size_limit where step_size_limit is the maximum allowed step size at
    the current iteration. This should be between 0.1 and 1.
    """
    step_size_growth_exponent: builtins.float
    """The step size growth exponent defines a step size given by (1 +
    (total_steps_attempted + 1)^(-step_size_growth_exponent)) * step_size_.
    This should be between 0.1 and 1.
    """
    def __init__(
        self,
        *,
        step_size_reduction_exponent: builtins.float | None = ...,
        step_size_growth_exponent: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["step_size_growth_exponent", b"step_size_growth_exponent", "step_size_reduction_exponent", b"step_size_reduction_exponent"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["step_size_growth_exponent", b"step_size_growth_exponent", "step_size_reduction_exponent", b"step_size_reduction_exponent"]) -> None: ...

global___AdaptiveLinesearchParams = AdaptiveLinesearchParams

@typing.final
class MalitskyPockParams(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STEP_SIZE_DOWNSCALING_FACTOR_FIELD_NUMBER: builtins.int
    LINESEARCH_CONTRACTION_FACTOR_FIELD_NUMBER: builtins.int
    STEP_SIZE_INTERPOLATION_FIELD_NUMBER: builtins.int
    step_size_downscaling_factor: builtins.float
    """At every inner iteration the algorithm can decide to accept the step size
    or to update it to step_size = step_size_downscaling_factor * step_size.
    This parameter should lie between 0 and 1. The default is the value used in
    Malitsky and Pock (2016).
    """
    linesearch_contraction_factor: builtins.float
    """Contraction factor used in the linesearch condition of Malitsky and Pock.
    A step size is accepted if primal_weight * primal_stepsize *
    norm(constraint_matrix' * (next_dual - current_dual)) is less
    than linesearch_contraction_factor * norm(next_dual - current_dual).
    The default is the value used in Malitsky and Pock (2016).
    """
    step_size_interpolation: builtins.float
    """Malitsky and Pock linesearch rule permits an arbitrary choice of the first
    step size guess within an interval [m, M]. This parameter determines where
    in that interval to pick the step size. In particular, the next stepsize is
    given by m + step_size_interpolation*(M - m). The default is the value used
    in Malitsky and Pock (2016).
    """
    def __init__(
        self,
        *,
        step_size_downscaling_factor: builtins.float | None = ...,
        linesearch_contraction_factor: builtins.float | None = ...,
        step_size_interpolation: builtins.float | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["linesearch_contraction_factor", b"linesearch_contraction_factor", "step_size_downscaling_factor", b"step_size_downscaling_factor", "step_size_interpolation", b"step_size_interpolation"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["linesearch_contraction_factor", b"linesearch_contraction_factor", "step_size_downscaling_factor", b"step_size_downscaling_factor", "step_size_interpolation", b"step_size_interpolation"]) -> None: ...

global___MalitskyPockParams = MalitskyPockParams

@typing.final
class PrimalDualHybridGradientParams(google.protobuf.message.Message):
    """Parameters for PrimalDualHybridGradient() in primal_dual_hybrid_gradient.h.
    While the defaults are generally good, it is usually worthwhile to perform a
    parameter sweep to find good settings for a particular family of problems.
    The following parameters should be considered for tuning:
    - restart_strategy (jointly with major_iteration_frequency)
    - primal_weight_update_smoothing (jointly with initial_primal_weight)
    - presolve_options.use_glop
    - l_inf_ruiz_iterations
    - l2_norm_rescaling
    In addition, tune num_threads to speed up the solve.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _RestartStrategy:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _RestartStrategyEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[PrimalDualHybridGradientParams._RestartStrategy.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        RESTART_STRATEGY_UNSPECIFIED: PrimalDualHybridGradientParams._RestartStrategy.ValueType  # 0
        NO_RESTARTS: PrimalDualHybridGradientParams._RestartStrategy.ValueType  # 1
        """No restarts are performed. The average solution is cleared every major
        iteration, but the current solution is not changed.
        """
        EVERY_MAJOR_ITERATION: PrimalDualHybridGradientParams._RestartStrategy.ValueType  # 2
        """On every major iteration, the current solution is reset to the average
        since the last major iteration.
        """
        ADAPTIVE_HEURISTIC: PrimalDualHybridGradientParams._RestartStrategy.ValueType  # 3
        """A heuristic that adaptively decides on every major iteration whether to
        restart (this is forced approximately on increasing powers-of-two
        iterations), and if so to the current or to the average, based on
        reduction in a potential function. The rule more or less follows the
        description of the adaptive restart scheme in
        https://arxiv.org/pdf/2106.04756.pdf.
        """
        ADAPTIVE_DISTANCE_BASED: PrimalDualHybridGradientParams._RestartStrategy.ValueType  # 4
        """A distance-based restarting scheme that restarts the algorithm whenever
        an appropriate potential function is reduced sufficiently. This check
        happens at every major iteration.
        TODO(user): Cite paper for the restart strategy and definition of the
        potential function, when available.
        """

    class RestartStrategy(_RestartStrategy, metaclass=_RestartStrategyEnumTypeWrapper): ...
    RESTART_STRATEGY_UNSPECIFIED: PrimalDualHybridGradientParams.RestartStrategy.ValueType  # 0
    NO_RESTARTS: PrimalDualHybridGradientParams.RestartStrategy.ValueType  # 1
    """No restarts are performed. The average solution is cleared every major
    iteration, but the current solution is not changed.
    """
    EVERY_MAJOR_ITERATION: PrimalDualHybridGradientParams.RestartStrategy.ValueType  # 2
    """On every major iteration, the current solution is reset to the average
    since the last major iteration.
    """
    ADAPTIVE_HEURISTIC: PrimalDualHybridGradientParams.RestartStrategy.ValueType  # 3
    """A heuristic that adaptively decides on every major iteration whether to
    restart (this is forced approximately on increasing powers-of-two
    iterations), and if so to the current or to the average, based on
    reduction in a potential function. The rule more or less follows the
    description of the adaptive restart scheme in
    https://arxiv.org/pdf/2106.04756.pdf.
    """
    ADAPTIVE_DISTANCE_BASED: PrimalDualHybridGradientParams.RestartStrategy.ValueType  # 4
    """A distance-based restarting scheme that restarts the algorithm whenever
    an appropriate potential function is reduced sufficiently. This check
    happens at every major iteration.
    TODO(user): Cite paper for the restart strategy and definition of the
    potential function, when available.
    """

    class _LinesearchRule:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _LinesearchRuleEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[PrimalDualHybridGradientParams._LinesearchRule.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        LINESEARCH_RULE_UNSPECIFIED: PrimalDualHybridGradientParams._LinesearchRule.ValueType  # 0
        ADAPTIVE_LINESEARCH_RULE: PrimalDualHybridGradientParams._LinesearchRule.ValueType  # 1
        """Applies the heuristic rule presented in Section 3.1 of
        https://arxiv.org/pdf/2106.04756.pdf (further generalized to QP). There
        is not a proof of convergence for it. It is usually the fastest in
        practice but sometimes behaves poorly.
        """
        MALITSKY_POCK_LINESEARCH_RULE: PrimalDualHybridGradientParams._LinesearchRule.ValueType  # 2
        """Applies Malitsky & Pock linesearch rule. This guarantees an
        ergodic O(1/N) convergence rate https://arxiv.org/pdf/1608.08883.pdf.
        This is provably convergent but doesn't usually work as well in practice
        as ADAPTIVE_LINESEARCH_RULE.
        """
        CONSTANT_STEP_SIZE_RULE: PrimalDualHybridGradientParams._LinesearchRule.ValueType  # 3
        """Uses a constant step size corresponding to an estimate of the maximum
        singular value of the constraint matrix.
        """

    class LinesearchRule(_LinesearchRule, metaclass=_LinesearchRuleEnumTypeWrapper): ...
    LINESEARCH_RULE_UNSPECIFIED: PrimalDualHybridGradientParams.LinesearchRule.ValueType  # 0
    ADAPTIVE_LINESEARCH_RULE: PrimalDualHybridGradientParams.LinesearchRule.ValueType  # 1
    """Applies the heuristic rule presented in Section 3.1 of
    https://arxiv.org/pdf/2106.04756.pdf (further generalized to QP). There
    is not a proof of convergence for it. It is usually the fastest in
    practice but sometimes behaves poorly.
    """
    MALITSKY_POCK_LINESEARCH_RULE: PrimalDualHybridGradientParams.LinesearchRule.ValueType  # 2
    """Applies Malitsky & Pock linesearch rule. This guarantees an
    ergodic O(1/N) convergence rate https://arxiv.org/pdf/1608.08883.pdf.
    This is provably convergent but doesn't usually work as well in practice
    as ADAPTIVE_LINESEARCH_RULE.
    """
    CONSTANT_STEP_SIZE_RULE: PrimalDualHybridGradientParams.LinesearchRule.ValueType  # 3
    """Uses a constant step size corresponding to an estimate of the maximum
    singular value of the constraint matrix.
    """

    @typing.final
    class PresolveOptions(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        USE_GLOP_FIELD_NUMBER: builtins.int
        GLOP_PARAMETERS_FIELD_NUMBER: builtins.int
        use_glop: builtins.bool
        """If true runs Glop's presolver on the given instance prior to solving.
        Note that convergence criteria are still interpreted with respect to the
        original problem. Certificates may not be available if presolve detects
        infeasibility. Glop's presolver cannot apply to problems with quadratic
        objectives or problems with more than 2^31 variables or constraints. It's
        often beneficial to enable the presolver, especially on medium-sized
        problems. At some larger scales, the presolver can become a serial
        bottleneck.
        """
        @property
        def glop_parameters(self) -> ortools.glop.parameters_pb2.GlopParameters:
            """Parameters to control glop's presolver. Only used when use_glop is true.
            These are merged with and override PDLP's defaults.
            """

        def __init__(
            self,
            *,
            use_glop: builtins.bool | None = ...,
            glop_parameters: ortools.glop.parameters_pb2.GlopParameters | None = ...,
        ) -> None: ...
        def HasField(self, field_name: typing.Literal["glop_parameters", b"glop_parameters", "use_glop", b"use_glop"]) -> builtins.bool: ...
        def ClearField(self, field_name: typing.Literal["glop_parameters", b"glop_parameters", "use_glop", b"use_glop"]) -> None: ...

    TERMINATION_CRITERIA_FIELD_NUMBER: builtins.int
    NUM_THREADS_FIELD_NUMBER: builtins.int
    NUM_SHARDS_FIELD_NUMBER: builtins.int
    SCHEDULER_TYPE_FIELD_NUMBER: builtins.int
    RECORD_ITERATION_STATS_FIELD_NUMBER: builtins.int
    VERBOSITY_LEVEL_FIELD_NUMBER: builtins.int
    LOG_INTERVAL_SECONDS_FIELD_NUMBER: builtins.int
    MAJOR_ITERATION_FREQUENCY_FIELD_NUMBER: builtins.int
    TERMINATION_CHECK_FREQUENCY_FIELD_NUMBER: builtins.int
    RESTART_STRATEGY_FIELD_NUMBER: builtins.int
    PRIMAL_WEIGHT_UPDATE_SMOOTHING_FIELD_NUMBER: builtins.int
    INITIAL_PRIMAL_WEIGHT_FIELD_NUMBER: builtins.int
    PRESOLVE_OPTIONS_FIELD_NUMBER: builtins.int
    L_INF_RUIZ_ITERATIONS_FIELD_NUMBER: builtins.int
    L2_NORM_RESCALING_FIELD_NUMBER: builtins.int
    SUFFICIENT_REDUCTION_FOR_RESTART_FIELD_NUMBER: builtins.int
    NECESSARY_REDUCTION_FOR_RESTART_FIELD_NUMBER: builtins.int
    LINESEARCH_RULE_FIELD_NUMBER: builtins.int
    ADAPTIVE_LINESEARCH_PARAMETERS_FIELD_NUMBER: builtins.int
    MALITSKY_POCK_PARAMETERS_FIELD_NUMBER: builtins.int
    INITIAL_STEP_SIZE_SCALING_FIELD_NUMBER: builtins.int
    RANDOM_PROJECTION_SEEDS_FIELD_NUMBER: builtins.int
    INFINITE_CONSTRAINT_BOUND_THRESHOLD_FIELD_NUMBER: builtins.int
    HANDLE_SOME_PRIMAL_GRADIENTS_ON_FINITE_BOUNDS_AS_RESIDUALS_FIELD_NUMBER: builtins.int
    USE_DIAGONAL_QP_TRUST_REGION_SOLVER_FIELD_NUMBER: builtins.int
    DIAGONAL_QP_TRUST_REGION_SOLVER_TOLERANCE_FIELD_NUMBER: builtins.int
    USE_FEASIBILITY_POLISHING_FIELD_NUMBER: builtins.int
    APPLY_FEASIBILITY_POLISHING_AFTER_LIMITS_REACHED_FIELD_NUMBER: builtins.int
    APPLY_FEASIBILITY_POLISHING_IF_SOLVER_IS_INTERRUPTED_FIELD_NUMBER: builtins.int
    num_threads: builtins.int
    """The number of threads to use. Must be positive.
    Try various values of num_threads, up to the number of physical cores.
    Performance may not be monotonically increasing with the number of threads
    because of memory bandwidth limitations.
    """
    num_shards: builtins.int
    """For more efficient parallel computation, the matrices and vectors are
    divided (virtually) into num_shards shards. Results are computed
    independently for each shard and then combined. As a consequence, the order
    of computation, and hence floating point roundoff, depends on the number of
    shards so reproducible results require using the same value for num_shards.
    However, for efficiency num_shards should a be at least num_threads, and
    preferably at least 4*num_threads to allow better load balancing. If
    num_shards is positive, the computation will use that many shards.
    Otherwise a default that depends on num_threads will be used.
    """
    scheduler_type: global___SchedulerType.ValueType
    """The type of scheduler used for CPU multi-threading. See the documentation
    of the corresponding enum for more details.
    """
    record_iteration_stats: builtins.bool
    """If true, the iteration_stats field of the SolveLog output will be populated
    at every iteration. Note that we only compute solution statistics at
    termination checks. Setting this parameter to true may substantially
    increase the size of the output.
    """
    verbosity_level: builtins.int
    """The verbosity of logging.
    0: No informational logging. (Errors are logged.)
    1: Summary statistics only. No iteration-level details.
    2: A table of iteration-level statistics is logged.
       (See ToShortString() in primal_dual_hybrid_gradient.cc).
    3: A more detailed table of iteration-level statistics is logged.
       (See ToString() in primal_dual_hybrid_gradient.cc).
    4: For iteration-level details, prints the statistics of both the average
       (prefixed with A) and the current iterate (prefixed with C). Also prints
       internal algorithmic state and details.
    Logging at levels 2-4 also includes messages from level 1.
    """
    log_interval_seconds: builtins.float
    """Time between iteration-level statistics logging (if `verbosity_level > 1`).
    Since iteration-level statistics are only generated when performing
    termination checks, logs will be generated from next termination check
    after `log_interval_seconds` have elapsed. Should be >= 0.0. 0.0 (the
    default) means log statistics at every termination check.
    """
    major_iteration_frequency: builtins.int
    """The frequency at which extra work is performed to make major algorithmic
    decisions, e.g., performing restarts and updating the primal weight. Major
    iterations also trigger a termination check. For best performance using the
    NO_RESTARTS or EVERY_MAJOR_ITERATION rule, one should perform a log-scale
    grid search over this parameter, for example, over powers of two.
    ADAPTIVE_HEURISTIC is mostly insensitive to this value.
    """
    termination_check_frequency: builtins.int
    """The frequency (based on a counter reset every major iteration) to check for
    termination (involves extra work) and log iteration stats. Termination
    checks do not affect algorithmic progress unless termination is triggered.
    """
    restart_strategy: global___PrimalDualHybridGradientParams.RestartStrategy.ValueType
    """NO_RESTARTS and EVERY_MAJOR_ITERATION occasionally outperform the default.
    If using a strategy other than ADAPTIVE_HEURISTIC, you must also tune
    major_iteration_frequency.
    """
    primal_weight_update_smoothing: builtins.float
    """This parameter controls exponential smoothing of log(primal_weight) when a
    primal weight update occurs (i.e., when the ratio of primal and dual step
    sizes is adjusted). At 0.0, the primal weight will be frozen at its initial
    value and there will be no dynamic updates in the algorithm. At 1.0, there
    is no smoothing in the updates. The default of 0.5 generally performs well,
    but has been observed on occasion to trigger unstable swings in the primal
    weight. We recommend also trying 0.0 (disabling primal weight updates), in
    which case you must also tune initial_primal_weight.
    """
    initial_primal_weight: builtins.float
    """The initial value of the primal weight (i.e., the ratio of primal and dual
    step sizes). The primal weight remains fixed throughout the solve if
    primal_weight_update_smoothing = 0.0. If unset, the default is the ratio of
    the norm of the objective vector to the L2 norm of the combined constraint
    bounds vector (as defined above). If this ratio is not finite and positive,
    then the default is 1.0 instead. For tuning, try powers of 10, for example,
    from 10^{-6} to 10^6.
    """
    l_inf_ruiz_iterations: builtins.int
    """Number of L_infinity Ruiz rescaling iterations to apply to the constraint
    matrix. Zero disables this rescaling pass. Recommended values to try when
    tuning are 0, 5, and 10.
    """
    l2_norm_rescaling: builtins.bool
    """If true, applies L_2 norm rescaling after the Ruiz rescaling. Heuristically
    this has been found to help convergence.
    """
    sufficient_reduction_for_restart: builtins.float
    """For ADAPTIVE_HEURISTIC and ADAPTIVE_DISTANCE_BASED only: A relative
    reduction in the potential function by this amount always triggers a
    restart. Must be between 0.0 and 1.0.
    """
    necessary_reduction_for_restart: builtins.float
    """For ADAPTIVE_HEURISTIC only: A relative reduction in the potential function
    by this amount triggers a restart if, additionally, the quality of the
    iterates appears to be getting worse. The value must be in the interval
    [sufficient_reduction_for_restart, 1). Smaller values make restarts less
    frequent, and larger values make them more frequent.
    """
    linesearch_rule: global___PrimalDualHybridGradientParams.LinesearchRule.ValueType
    """Linesearch rule applied at each major iteration."""
    initial_step_size_scaling: builtins.float
    """Scaling factor applied to the initial step size (all step sizes if
    linesearch_rule == CONSTANT_STEP_SIZE_RULE).
    """
    infinite_constraint_bound_threshold: builtins.float
    """Constraint bounds with absolute value at least this threshold are replaced
    with infinities.
    NOTE: This primarily affects the relative convergence criteria. A smaller
    value makes the relative convergence criteria stronger. It also affects the
    problem statistics LOG()ed at the start of the run, and the default initial
    primal weight, since that is based on the norm of the bounds.
    """
    handle_some_primal_gradients_on_finite_bounds_as_residuals: builtins.bool
    """See
    https://developers.google.com/optimization/lp/pdlp_math#treating_some_variable_bounds_as_infinite
    for a description of this flag.
    """
    use_diagonal_qp_trust_region_solver: builtins.bool
    """When solving QPs with diagonal objective matrices, this option can be
    turned on to enable an experimental solver that avoids linearization of the
    quadratic term. The `diagonal_qp_solver_accuracy` parameter controls the
    solve accuracy.
    TODO(user): Turn this option on by default for quadratic
    programs after numerical evaluation.
    """
    diagonal_qp_trust_region_solver_tolerance: builtins.float
    """The solve tolerance of the experimental trust region solver for diagonal
    QPs, controlling the accuracy of binary search over a one-dimensional
    scaling parameter. Smaller values imply smaller relative error of the final
    solution vector.
    TODO(user): Find an expression for the final relative error.
    """
    use_feasibility_polishing: builtins.bool
    """If true, periodically runs feasibility polishing, which attempts to move
    from latest average iterate to one that is closer to feasibility (i.e., has
    smaller primal and dual residuals) while probably increasing the objective
    gap. This is useful primarily when the feasibility tolerances are fairly
    tight and the objective gap tolerance is somewhat looser. Note that this
    does not change the termination criteria, but rather can help achieve the
    termination criteria more quickly when the objective gap is not as
    important as feasibility.

    `use_feasibility_polishing` cannot be used with glop presolve, and requires
    `handle_some_primal_gradients_on_finite_bounds_as_residuals == false`.
    `use_feasibility_polishing` can only be used with linear programs.

    Feasibility polishing runs two separate phases, primal feasibility and dual
    feasibility. The primal feasibility phase runs PDHG on the primal
    feasibility problem (obtained by changing the objective vector to all
    zeros), using the average primal iterate and zero dual (which is optimal
    for the primal feasibility problem) as the initial solution. The dual
    feasibility phase runs PDHG on the dual feasibility problem (obtained by
    changing all finite variable and constraint bounds to zero), using the
    average dual iterate and zero primal (which is optimal for the dual
    feasibility problem) as the initial solution. The primal solution from the
    primal feasibility phase and dual solution from the dual feasibility phase
    are then combined (forming a solution of type
    `POINT_TYPE_FEASIBILITY_POLISHING_SOLUTION`) and checked against the
    termination criteria.
    """
    apply_feasibility_polishing_after_limits_reached: builtins.bool
    """If true, feasibility polishing will be applied after the iteration limit,
    kkt limit, or time limit is reached. This can result in a solution that is
    closer to feasibility, at the expense of violating the limit by a moderate
    amount.
    """
    apply_feasibility_polishing_if_solver_is_interrupted: builtins.bool
    """If true, feasibility polishing will be applied after the solver is
    interrupted. This can result in a solution that is closer to feasibility,
    at the expense of not stopping as promptly when interrupted.
    """
    @property
    def termination_criteria(self) -> global___TerminationCriteria: ...
    @property
    def presolve_options(self) -> global___PrimalDualHybridGradientParams.PresolveOptions: ...
    @property
    def adaptive_linesearch_parameters(self) -> global___AdaptiveLinesearchParams: ...
    @property
    def malitsky_pock_parameters(self) -> global___MalitskyPockParams: ...
    @property
    def random_projection_seeds(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[builtins.int]:
        """Seeds for generating (pseudo-)random projections of iterates during
        termination checks. For each seed, the projection of the primal and dual
        solutions onto random planes in primal and dual space will be computed and
        added the IterationStats if record_iteration_stats is true. The random
        planes generated will be determined by the seeds, the primal and dual
        dimensions, and num_threads.
        """

    def __init__(
        self,
        *,
        termination_criteria: global___TerminationCriteria | None = ...,
        num_threads: builtins.int | None = ...,
        num_shards: builtins.int | None = ...,
        scheduler_type: global___SchedulerType.ValueType | None = ...,
        record_iteration_stats: builtins.bool | None = ...,
        verbosity_level: builtins.int | None = ...,
        log_interval_seconds: builtins.float | None = ...,
        major_iteration_frequency: builtins.int | None = ...,
        termination_check_frequency: builtins.int | None = ...,
        restart_strategy: global___PrimalDualHybridGradientParams.RestartStrategy.ValueType | None = ...,
        primal_weight_update_smoothing: builtins.float | None = ...,
        initial_primal_weight: builtins.float | None = ...,
        presolve_options: global___PrimalDualHybridGradientParams.PresolveOptions | None = ...,
        l_inf_ruiz_iterations: builtins.int | None = ...,
        l2_norm_rescaling: builtins.bool | None = ...,
        sufficient_reduction_for_restart: builtins.float | None = ...,
        necessary_reduction_for_restart: builtins.float | None = ...,
        linesearch_rule: global___PrimalDualHybridGradientParams.LinesearchRule.ValueType | None = ...,
        adaptive_linesearch_parameters: global___AdaptiveLinesearchParams | None = ...,
        malitsky_pock_parameters: global___MalitskyPockParams | None = ...,
        initial_step_size_scaling: builtins.float | None = ...,
        random_projection_seeds: collections.abc.Iterable[builtins.int] | None = ...,
        infinite_constraint_bound_threshold: builtins.float | None = ...,
        handle_some_primal_gradients_on_finite_bounds_as_residuals: builtins.bool | None = ...,
        use_diagonal_qp_trust_region_solver: builtins.bool | None = ...,
        diagonal_qp_trust_region_solver_tolerance: builtins.float | None = ...,
        use_feasibility_polishing: builtins.bool | None = ...,
        apply_feasibility_polishing_after_limits_reached: builtins.bool | None = ...,
        apply_feasibility_polishing_if_solver_is_interrupted: builtins.bool | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["adaptive_linesearch_parameters", b"adaptive_linesearch_parameters", "apply_feasibility_polishing_after_limits_reached", b"apply_feasibility_polishing_after_limits_reached", "apply_feasibility_polishing_if_solver_is_interrupted", b"apply_feasibility_polishing_if_solver_is_interrupted", "diagonal_qp_trust_region_solver_tolerance", b"diagonal_qp_trust_region_solver_tolerance", "handle_some_primal_gradients_on_finite_bounds_as_residuals", b"handle_some_primal_gradients_on_finite_bounds_as_residuals", "infinite_constraint_bound_threshold", b"infinite_constraint_bound_threshold", "initial_primal_weight", b"initial_primal_weight", "initial_step_size_scaling", b"initial_step_size_scaling", "l2_norm_rescaling", b"l2_norm_rescaling", "l_inf_ruiz_iterations", b"l_inf_ruiz_iterations", "linesearch_rule", b"linesearch_rule", "log_interval_seconds", b"log_interval_seconds", "major_iteration_frequency", b"major_iteration_frequency", "malitsky_pock_parameters", b"malitsky_pock_parameters", "necessary_reduction_for_restart", b"necessary_reduction_for_restart", "num_shards", b"num_shards", "num_threads", b"num_threads", "presolve_options", b"presolve_options", "primal_weight_update_smoothing", b"primal_weight_update_smoothing", "record_iteration_stats", b"record_iteration_stats", "restart_strategy", b"restart_strategy", "scheduler_type", b"scheduler_type", "sufficient_reduction_for_restart", b"sufficient_reduction_for_restart", "termination_check_frequency", b"termination_check_frequency", "termination_criteria", b"termination_criteria", "use_diagonal_qp_trust_region_solver", b"use_diagonal_qp_trust_region_solver", "use_feasibility_polishing", b"use_feasibility_polishing", "verbosity_level", b"verbosity_level"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["adaptive_linesearch_parameters", b"adaptive_linesearch_parameters", "apply_feasibility_polishing_after_limits_reached", b"apply_feasibility_polishing_after_limits_reached", "apply_feasibility_polishing_if_solver_is_interrupted", b"apply_feasibility_polishing_if_solver_is_interrupted", "diagonal_qp_trust_region_solver_tolerance", b"diagonal_qp_trust_region_solver_tolerance", "handle_some_primal_gradients_on_finite_bounds_as_residuals", b"handle_some_primal_gradients_on_finite_bounds_as_residuals", "infinite_constraint_bound_threshold", b"infinite_constraint_bound_threshold", "initial_primal_weight", b"initial_primal_weight", "initial_step_size_scaling", b"initial_step_size_scaling", "l2_norm_rescaling", b"l2_norm_rescaling", "l_inf_ruiz_iterations", b"l_inf_ruiz_iterations", "linesearch_rule", b"linesearch_rule", "log_interval_seconds", b"log_interval_seconds", "major_iteration_frequency", b"major_iteration_frequency", "malitsky_pock_parameters", b"malitsky_pock_parameters", "necessary_reduction_for_restart", b"necessary_reduction_for_restart", "num_shards", b"num_shards", "num_threads", b"num_threads", "presolve_options", b"presolve_options", "primal_weight_update_smoothing", b"primal_weight_update_smoothing", "random_projection_seeds", b"random_projection_seeds", "record_iteration_stats", b"record_iteration_stats", "restart_strategy", b"restart_strategy", "scheduler_type", b"scheduler_type", "sufficient_reduction_for_restart", b"sufficient_reduction_for_restart", "termination_check_frequency", b"termination_check_frequency", "termination_criteria", b"termination_criteria", "use_diagonal_qp_trust_region_solver", b"use_diagonal_qp_trust_region_solver", "use_feasibility_polishing", b"use_feasibility_polishing", "verbosity_level", b"verbosity_level"]) -> None: ...

global___PrimalDualHybridGradientParams = PrimalDualHybridGradientParams
