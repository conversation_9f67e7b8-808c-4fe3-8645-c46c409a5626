# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/service/v1/mathopt/model.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/service/v1/mathopt/model.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.service.v1.mathopt import sparse_containers_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_sparse__containers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n&ortools/service/v1/mathopt/model.proto\x12&operations_research.service.v1.mathopt\x1a\x32ortools/service/v1/mathopt/sparse_containers.proto\"j\n\x0eVariablesProto\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\x14\n\x0clower_bounds\x18\x02 \x03(\x01\x12\x14\n\x0cupper_bounds\x18\x03 \x03(\x01\x12\x10\n\x08integers\x18\x04 \x03(\x08\x12\r\n\x05names\x18\x05 \x03(\t\"\x91\x02\n\x0eObjectiveProto\x12\x10\n\x08maximize\x18\x01 \x01(\x08\x12\x0e\n\x06offset\x18\x02 \x01(\x01\x12\\\n\x13linear_coefficients\x18\x03 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\x12_\n\x16quadratic_coefficients\x18\x04 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleMatrixProto\x12\x0c\n\x04name\x18\x05 \x01(\t\x12\x10\n\x08priority\x18\x06 \x01(\x03\"`\n\x16LinearConstraintsProto\x12\x0b\n\x03ids\x18\x01 \x03(\x03\x12\x14\n\x0clower_bounds\x18\x02 \x03(\x01\x12\x14\n\x0cupper_bounds\x18\x03 \x03(\x01\x12\r\n\x05names\x18\x04 \x03(\t\"\x83\x02\n\x18QuadraticConstraintProto\x12U\n\x0clinear_terms\x18\x01 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\x12X\n\x0fquadratic_terms\x18\x02 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleMatrixProto\x12\x13\n\x0blower_bound\x18\x03 \x01(\x01\x12\x13\n\x0bupper_bound\x18\x04 \x01(\x01\x12\x0c\n\x04name\x18\x05 \x01(\t\"\xdc\x01\n\x1eSecondOrderConeConstraintProto\x12R\n\x0bupper_bound\x18\x01 \x01(\x0b\x32=.operations_research.service.v1.mathopt.LinearExpressionProto\x12X\n\x11\x61rguments_to_norm\x18\x02 \x03(\x0b\x32=.operations_research.service.v1.mathopt.LinearExpressionProto\x12\x0c\n\x04name\x18\x03 \x01(\t\"\x87\x01\n\x12SosConstraintProto\x12R\n\x0b\x65xpressions\x18\x01 \x03(\x0b\x32=.operations_research.service.v1.mathopt.LinearExpressionProto\x12\x0f\n\x07weights\x18\x02 \x03(\x01\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xed\x01\n\x18IndicatorConstraintProto\x12\x19\n\x0cindicator_id\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x18\n\x10\x61\x63tivate_on_zero\x18\x06 \x01(\x08\x12S\n\nexpression\x18\x02 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleVectorProto\x12\x13\n\x0blower_bound\x18\x03 \x01(\x01\x12\x13\n\x0bupper_bound\x18\x04 \x01(\x01\x12\x0c\n\x04name\x18\x05 \x01(\tB\x0f\n\r_indicator_id\"\xdb\r\n\nModelProto\x12\x0c\n\x04name\x18\x01 \x01(\t\x12I\n\tvariables\x18\x02 \x01(\x0b\x32\x36.operations_research.service.v1.mathopt.VariablesProto\x12I\n\tobjective\x18\x03 \x01(\x0b\x32\x36.operations_research.service.v1.mathopt.ObjectiveProto\x12i\n\x14\x61uxiliary_objectives\x18\n \x03(\x0b\x32K.operations_research.service.v1.mathopt.ModelProto.AuxiliaryObjectivesEntry\x12Z\n\x12linear_constraints\x18\x04 \x01(\x0b\x32>.operations_research.service.v1.mathopt.LinearConstraintsProto\x12\x61\n\x18linear_constraint_matrix\x18\x05 \x01(\x0b\x32?.operations_research.service.v1.mathopt.SparseDoubleMatrixProto\x12k\n\x15quadratic_constraints\x18\x06 \x03(\x0b\x32L.operations_research.service.v1.mathopt.ModelProto.QuadraticConstraintsEntry\x12y\n\x1dsecond_order_cone_constraints\x18\x0b \x03(\x0b\x32R.operations_research.service.v1.mathopt.ModelProto.SecondOrderConeConstraintsEntry\x12\x61\n\x10sos1_constraints\x18\x07 \x03(\x0b\x32G.operations_research.service.v1.mathopt.ModelProto.Sos1ConstraintsEntry\x12\x61\n\x10sos2_constraints\x18\x08 \x03(\x0b\x32G.operations_research.service.v1.mathopt.ModelProto.Sos2ConstraintsEntry\x12k\n\x15indicator_constraints\x18\t \x03(\x0b\x32L.operations_research.service.v1.mathopt.ModelProto.IndicatorConstraintsEntry\x1ar\n\x18\x41uxiliaryObjectivesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x45\n\x05value\x18\x02 \x01(\x0b\x32\x36.operations_research.service.v1.mathopt.ObjectiveProto:\x02\x38\x01\x1a}\n\x19QuadraticConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12O\n\x05value\x18\x02 \x01(\x0b\x32@.operations_research.service.v1.mathopt.QuadraticConstraintProto:\x02\x38\x01\x1a\x89\x01\n\x1fSecondOrderConeConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12U\n\x05value\x18\x02 \x01(\x0b\x32\x46.operations_research.service.v1.mathopt.SecondOrderConeConstraintProto:\x02\x38\x01\x1ar\n\x14Sos1ConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12I\n\x05value\x18\x02 \x01(\x0b\x32:.operations_research.service.v1.mathopt.SosConstraintProto:\x02\x38\x01\x1ar\n\x14Sos2ConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12I\n\x05value\x18\x02 \x01(\x0b\x32:.operations_research.service.v1.mathopt.SosConstraintProto:\x02\x38\x01\x1a}\n\x19IndicatorConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12O\n\x05value\x18\x02 \x01(\x0b\x32@.operations_research.service.v1.mathopt.IndicatorConstraintProto:\x02\x38\x01\x42\x42\n%com.google.ortools.service.v1.mathoptP\x01\xaa\x02\x16Google.OrTools.Serviceb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.service.v1.mathopt.model_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.google.ortools.service.v1.mathoptP\001\252\002\026Google.OrTools.Service'
  _globals['_MODELPROTO_AUXILIARYOBJECTIVESENTRY']._loaded_options = None
  _globals['_MODELPROTO_AUXILIARYOBJECTIVESENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_QUADRATICCONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_SECONDORDERCONECONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_SECONDORDERCONECONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_SOS1CONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_SOS1CONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_SOS2CONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_SOS2CONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_MODELPROTO_INDICATORCONSTRAINTSENTRY']._loaded_options = None
  _globals['_MODELPROTO_INDICATORCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_VARIABLESPROTO']._serialized_start=134
  _globals['_VARIABLESPROTO']._serialized_end=240
  _globals['_OBJECTIVEPROTO']._serialized_start=243
  _globals['_OBJECTIVEPROTO']._serialized_end=516
  _globals['_LINEARCONSTRAINTSPROTO']._serialized_start=518
  _globals['_LINEARCONSTRAINTSPROTO']._serialized_end=614
  _globals['_QUADRATICCONSTRAINTPROTO']._serialized_start=617
  _globals['_QUADRATICCONSTRAINTPROTO']._serialized_end=876
  _globals['_SECONDORDERCONECONSTRAINTPROTO']._serialized_start=879
  _globals['_SECONDORDERCONECONSTRAINTPROTO']._serialized_end=1099
  _globals['_SOSCONSTRAINTPROTO']._serialized_start=1102
  _globals['_SOSCONSTRAINTPROTO']._serialized_end=1237
  _globals['_INDICATORCONSTRAINTPROTO']._serialized_start=1240
  _globals['_INDICATORCONSTRAINTPROTO']._serialized_end=1477
  _globals['_MODELPROTO']._serialized_start=1480
  _globals['_MODELPROTO']._serialized_end=3235
  _globals['_MODELPROTO_AUXILIARYOBJECTIVESENTRY']._serialized_start=2495
  _globals['_MODELPROTO_AUXILIARYOBJECTIVESENTRY']._serialized_end=2609
  _globals['_MODELPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_start=2611
  _globals['_MODELPROTO_QUADRATICCONSTRAINTSENTRY']._serialized_end=2736
  _globals['_MODELPROTO_SECONDORDERCONECONSTRAINTSENTRY']._serialized_start=2739
  _globals['_MODELPROTO_SECONDORDERCONECONSTRAINTSENTRY']._serialized_end=2876
  _globals['_MODELPROTO_SOS1CONSTRAINTSENTRY']._serialized_start=2878
  _globals['_MODELPROTO_SOS1CONSTRAINTSENTRY']._serialized_end=2992
  _globals['_MODELPROTO_SOS2CONSTRAINTSENTRY']._serialized_start=2994
  _globals['_MODELPROTO_SOS2CONSTRAINTSENTRY']._serialized_end=3108
  _globals['_MODELPROTO_INDICATORCONSTRAINTSENTRY']._serialized_start=3110
  _globals['_MODELPROTO_INDICATORCONSTRAINTSENTRY']._serialized_end=3235
# @@protoc_insertion_point(module_scope)
