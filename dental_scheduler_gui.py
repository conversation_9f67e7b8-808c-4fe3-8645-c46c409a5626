"""
Modern GUI for Dental Implant Manufacturing Scheduler
Using CustomTkinter for modern dark theme interface
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import filedialog, messagebox
import threading
import webbrowser
import os
from datetime import datetime

# Import our scheduling modules
from job_loader import load_jobs_from_excel, validate_jobs_data
from schedule_visualizer import create_schedule_visualizations
from create_dashboard import create_unified_dashboard

# Set appearance mode and color theme
ctk.set_appearance_mode("dark")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("blue")  # Themes: "blue" (standard), "green", "dark-blue"

class DentalSchedulerGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🦷 Dental Implant Manufacturing Scheduler")
        self.root.geometry("1400x900")
        
        # Make window fullscreen
        self.root.state('zoomed')  # Windows fullscreen
        
        # Configure grid weight
        self.root.grid_columnconfigure(1, weight=1)
        self.root.grid_rowconfigure(0, weight=1)
        
        # Variables
        self.excel_file_path = tk.StringVar(value="Job_Upload_Template.xlsx")
        self.jobs_data = None
        self.schedule_data = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main user interface"""
        
        # Left sidebar
        self.sidebar_frame = ctk.CTkFrame(self.root, width=300, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=4, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(4, weight=1)
        
        # Logo and title
        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame, 
            text="🦷 Dental Scheduler", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        self.subtitle_label = ctk.CTkLabel(
            self.sidebar_frame, 
            text="Manufacturing Schedule Optimizer", 
            font=ctk.CTkFont(size=14)
        )
        self.subtitle_label.grid(row=1, column=0, padx=20, pady=(0, 20))
        
        # File selection section
        self.file_frame = ctk.CTkFrame(self.sidebar_frame)
        self.file_frame.grid(row=2, column=0, padx=20, pady=10, sticky="ew")
        
        self.file_label = ctk.CTkLabel(self.file_frame, text="Excel Job File:", font=ctk.CTkFont(weight="bold"))
        self.file_label.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")
        
        self.file_entry = ctk.CTkEntry(self.file_frame, textvariable=self.excel_file_path, width=200)
        self.file_entry.grid(row=1, column=0, padx=10, pady=5, sticky="ew")
        
        self.browse_button = ctk.CTkButton(
            self.file_frame, 
            text="Browse", 
            command=self.browse_file,
            width=80
        )
        self.browse_button.grid(row=1, column=1, padx=(5, 10), pady=5)
        
        self.file_frame.grid_columnconfigure(0, weight=1)
        
        # Action buttons
        self.load_button = ctk.CTkButton(
            self.sidebar_frame,
            text="📊 Load & Process Jobs",
            command=self.load_jobs,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.load_button.grid(row=3, column=0, padx=20, pady=10, sticky="ew")
        
        self.visualize_button = ctk.CTkButton(
            self.sidebar_frame,
            text="📈 Create Visualizations",
            command=self.create_visualizations,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold"),
            state="disabled"
        )
        self.visualize_button.grid(row=4, column=0, padx=20, pady=10, sticky="ew")
        
        self.dashboard_button = ctk.CTkButton(
            self.sidebar_frame,
            text="🌐 Open Dashboard",
            command=self.open_dashboard,
            height=40,
            font=ctk.CTkFont(size=16, weight="bold"),
            state="disabled"
        )
        self.dashboard_button.grid(row=5, column=0, padx=20, pady=10, sticky="ew")
        
        # Status section
        self.status_frame = ctk.CTkFrame(self.sidebar_frame)
        self.status_frame.grid(row=6, column=0, padx=20, pady=20, sticky="ew")
        
        self.status_label = ctk.CTkLabel(self.status_frame, text="Status:", font=ctk.CTkFont(weight="bold"))
        self.status_label.grid(row=0, column=0, padx=10, pady=(10, 5), sticky="w")
        
        self.status_text = ctk.CTkLabel(
            self.status_frame, 
            text="Ready to load jobs", 
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.status_text.grid(row=1, column=0, padx=10, pady=(0, 10), sticky="w")
        
        # Main content area
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.grid(row=0, column=1, padx=(20, 20), pady=20, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=1)
        
        # Main title
        self.main_title = ctk.CTkLabel(
            self.main_frame,
            text="Dental Implant Manufacturing Scheduler",
            font=ctk.CTkFont(size=28, weight="bold")
        )
        self.main_title.grid(row=0, column=0, padx=20, pady=20)
        
        # Content area with tabs
        self.tabview = ctk.CTkTabview(self.main_frame, width=800, height=600)
        self.tabview.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        
        # Add tabs
        self.tabview.add("📋 Job Summary")
        self.tabview.add("📊 Schedule Results")
        self.tabview.add("🌐 Web Dashboard")
        
        # Job Summary tab
        self.setup_job_summary_tab()
        
        # Schedule Results tab
        self.setup_schedule_results_tab()
        
        # Web Dashboard tab
        self.setup_web_dashboard_tab()
        
    def setup_job_summary_tab(self):
        """Setup the job summary tab"""
        tab = self.tabview.tab("📋 Job Summary")
        
        # Job summary text area
        self.job_summary_text = ctk.CTkTextbox(tab, width=700, height=500)
        self.job_summary_text.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        
        # Initial content
        self.job_summary_text.insert("0.0", 
            "Welcome to the Dental Implant Manufacturing Scheduler!\n\n"
            "📋 Features:\n"
            "• Load jobs from Excel templates\n"
            "• Optimize scheduling across Red, Blue, and Green teams\n"
            "• Generate interactive visualizations\n"
            "• Track job progress and deadlines\n\n"
            "🚀 Getting Started:\n"
            "1. Select your Excel job file (or use the default template)\n"
            "2. Click 'Load & Process Jobs' to import and validate jobs\n"
            "3. Click 'Create Visualizations' to generate schedule\n"
            "4. Click 'Open Dashboard' to view interactive charts\n\n"
            "📊 Teams:\n"
            "• Red Team (John, Adam): Design & CAD Engineering\n"
            "• Blue Team (Bob, Fred, Ted): Manufacturing & Production\n"
            "• Green Team (Jane, Bea): Quality Control & Finishing\n\n"
            "⏰ Constraints:\n"
            "• Working hours: 9:00 AM - 5:00 PM\n"
            "• Maximum 8 hours per employee per day\n"
            "• Sequential processing: Red → Blue → Green\n"
            "• All jobs must meet deadlines"
        )
        
        tab.grid_columnconfigure(0, weight=1)
        tab.grid_rowconfigure(0, weight=1)
        
    def setup_schedule_results_tab(self):
        """Setup the schedule results tab"""
        tab = self.tabview.tab("📊 Schedule Results")
        
        # Schedule results text area
        self.schedule_results_text = ctk.CTkTextbox(tab, width=700, height=500)
        self.schedule_results_text.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        
        self.schedule_results_text.insert("0.0", 
            "Schedule results will appear here after processing jobs.\n\n"
            "The results will include:\n"
            "• Daily schedules by employee\n"
            "• Job completion status\n"
            "• Team workload distribution\n"
            "• Optimization statistics"
        )
        
        tab.grid_columnconfigure(0, weight=1)
        tab.grid_rowconfigure(0, weight=1)
        
    def setup_web_dashboard_tab(self):
        """Setup the web dashboard tab"""
        tab = self.tabview.tab("🌐 Web Dashboard")
        
        # Info frame
        info_frame = ctk.CTkFrame(tab)
        info_frame.grid(row=0, column=0, padx=20, pady=20, sticky="ew")
        
        info_label = ctk.CTkLabel(
            info_frame,
            text="Interactive Web Dashboard",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        info_label.grid(row=0, column=0, padx=20, pady=10)
        
        info_text = ctk.CTkLabel(
            info_frame,
            text="The web dashboard provides interactive Gantt charts and analytics.\n"
                 "Click 'Open Dashboard' to launch in your default web browser.",
            font=ctk.CTkFont(size=14)
        )
        info_text.grid(row=1, column=0, padx=20, pady=(0, 20))
        
        # Dashboard preview (placeholder)
        self.dashboard_frame = ctk.CTkFrame(tab)
        self.dashboard_frame.grid(row=1, column=0, padx=20, pady=(0, 20), sticky="nsew")
        
        dashboard_placeholder = ctk.CTkLabel(
            self.dashboard_frame,
            text="🌐 Web Dashboard Preview\n\n"
                 "Interactive visualizations will be available\n"
                 "in your web browser after creating visualizations.\n\n"
                 "Features:\n"
                 "• Employee Gantt charts\n"
                 "• Job progress tracking\n"
                 "• Team workload analysis\n"
                 "• Executive dashboard",
            font=ctk.CTkFont(size=16),
            justify="center"
        )
        dashboard_placeholder.grid(row=0, column=0, padx=20, pady=50)
        
        tab.grid_columnconfigure(0, weight=1)
        tab.grid_rowconfigure(1, weight=1)
        self.dashboard_frame.grid_columnconfigure(0, weight=1)
        self.dashboard_frame.grid_rowconfigure(0, weight=1)

    def browse_file(self):
        """Browse for Excel file"""
        filename = filedialog.askopenfilename(
            title="Select Excel Job File",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")],
            initialdir=os.getcwd()
        )
        if filename:
            self.excel_file_path.set(filename)
            self.update_status(f"Selected file: {os.path.basename(filename)}")

    def update_status(self, message, color="gray"):
        """Update status message"""
        self.status_text.configure(text=message, text_color=color)
        self.root.update()

    def load_jobs(self):
        """Load and validate jobs from Excel file"""
        def load_thread():
            try:
                self.update_status("Loading jobs from Excel...", "yellow")

                file_path = self.excel_file_path.get()
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"File not found: {file_path}")

                # Load jobs
                jobs_data = load_jobs_from_excel(file_path)
                if not jobs_data:
                    raise ValueError("No jobs loaded from Excel file")

                # Validate jobs
                if not validate_jobs_data(jobs_data):
                    raise ValueError("Job validation failed")

                self.jobs_data = jobs_data

                # Update job summary
                self.update_job_summary()

                # Enable visualization button
                self.visualize_button.configure(state="normal")

                self.update_status(f"✓ Loaded {len(jobs_data)} jobs successfully", "green")

            except Exception as e:
                self.update_status(f"✗ Error: {str(e)}", "red")
                messagebox.showerror("Error", f"Failed to load jobs:\n{str(e)}")

        # Run in thread to prevent GUI freezing
        threading.Thread(target=load_thread, daemon=True).start()

    def update_job_summary(self):
        """Update the job summary tab with loaded job data"""
        if not self.jobs_data:
            return

        # Clear existing content
        self.job_summary_text.delete("0.0", "end")

        # Generate summary
        summary = f"📋 JOB SUMMARY REPORT\n"
        summary += f"{'=' * 50}\n\n"

        # Overall statistics
        total_jobs = len(self.jobs_data)
        job_types = {}
        priorities = {}
        total_hours = 0

        for job in self.jobs_data:
            job_type = job.get('job_type', 'Unknown')
            priority = job.get('priority', 'Unknown')

            job_types[job_type] = job_types.get(job_type, 0) + 1
            priorities[priority] = priorities.get(priority, 0) + 1

            # Calculate total hours
            for _, hours in job['tasks']:
                total_hours += hours

        summary += f"📊 OVERVIEW:\n"
        summary += f"Total Jobs: {total_jobs}\n"
        summary += f"Total Work Hours: {total_hours}\n"
        summary += f"Average Hours per Job: {total_hours/total_jobs:.1f}\n\n"

        summary += f"🏷️ JOB TYPES:\n"
        for job_type, count in job_types.items():
            summary += f"  {job_type}: {count} jobs\n"
        summary += "\n"

        summary += f"⚡ PRIORITIES:\n"
        for priority, count in priorities.items():
            summary += f"  {priority}: {count} jobs\n"
        summary += "\n"

        summary += f"👥 TEAM WORKLOAD:\n"
        red_hours = sum(task[1] for job in self.jobs_data for task in job['tasks'] if task[0] == 0)
        blue_hours = sum(task[1] for job in self.jobs_data for task in job['tasks'] if task[0] == 1)
        green_hours = sum(task[1] for job in self.jobs_data for task in job['tasks'] if task[0] == 2)

        summary += f"  Red Team (Design): {red_hours} hours\n"
        summary += f"  Blue Team (Manufacturing): {blue_hours} hours\n"
        summary += f"  Green Team (Quality): {green_hours} hours\n\n"

        summary += f"📋 JOB DETAILS:\n"
        summary += f"{'-' * 50}\n"

        for job in self.jobs_data[:10]:  # Show first 10 jobs
            summary += f"\n🔹 {job['job_id']}: {job['job_name']}\n"
            summary += f"   Type: {job.get('job_type', 'Unknown')}\n"
            summary += f"   Priority: {job.get('priority', 'Unknown')}\n"
            summary += f"   Patient: {job.get('patient_id', 'Unknown')}\n"
            summary += f"   Due: {job['completion_date']}\n"

            team_names = ['Red', 'Blue', 'Green']
            for i, (team_id, hours) in enumerate(job['tasks']):
                step_num = i + 1
                team_name = team_names[team_id]
                summary += f"   Step {step_num} - {team_name}: {hours}h\n"

        if len(self.jobs_data) > 10:
            summary += f"\n... and {len(self.jobs_data) - 10} more jobs\n"

        # Insert summary
        self.job_summary_text.insert("0.0", summary)

    def create_visualizations(self):
        """Create schedule visualizations"""
        def visualize_thread():
            try:
                self.update_status("Creating optimized schedule...", "yellow")

                if not self.jobs_data:
                    raise ValueError("No jobs loaded. Please load jobs first.")

                # Import and run the scheduling
                from Main import main as run_scheduler

                # Capture the schedule results
                self.update_status("Running optimization solver...", "yellow")

                # Run the scheduler and capture output
                import io
                import contextlib

                # Redirect stdout to capture the output
                output_buffer = io.StringIO()

                with contextlib.redirect_stdout(output_buffer):
                    # Run scheduler with current jobs
                    run_scheduler(self.excel_file_path.get())

                # Get the captured output
                schedule_output = output_buffer.getvalue()

                # Update schedule results tab
                self.schedule_results_text.delete("0.0", "end")
                self.schedule_results_text.insert("0.0", schedule_output)

                # Enable dashboard button
                self.dashboard_button.configure(state="normal")

                self.update_status("✓ Visualizations created successfully", "green")

                # Switch to schedule results tab
                self.tabview.set("📊 Schedule Results")

            except Exception as e:
                self.update_status(f"✗ Error: {str(e)}", "red")
                messagebox.showerror("Error", f"Failed to create visualizations:\n{str(e)}")

        # Run in thread to prevent GUI freezing
        threading.Thread(target=visualize_thread, daemon=True).start()

    def open_dashboard(self):
        """Open the web dashboard"""
        try:
            dashboard_path = os.path.abspath("schedule_dashboard.html")
            if os.path.exists(dashboard_path):
                webbrowser.open(f"file://{dashboard_path}")
                self.update_status("✓ Dashboard opened in browser", "green")
            else:
                raise FileNotFoundError("Dashboard file not found. Please create visualizations first.")
        except Exception as e:
            self.update_status(f"✗ Error: {str(e)}", "red")
            messagebox.showerror("Error", f"Failed to open dashboard:\n{str(e)}")

    def run(self):
        """Start the GUI application"""
        self.root.mainloop()

def main():
    """Main function to run the GUI"""
    app = DentalSchedulerGUI()
    app.run()

if __name__ == "__main__":
    main()
