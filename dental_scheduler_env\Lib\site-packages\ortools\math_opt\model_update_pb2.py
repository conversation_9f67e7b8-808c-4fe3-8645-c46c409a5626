# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/math_opt/model_update.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/math_opt/model_update.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.math_opt import model_pb2 as ortools_dot_math__opt_dot_model__pb2
from ortools.math_opt import sparse_containers_pb2 as ortools_dot_math__opt_dot_sparse__containers__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#ortools/math_opt/model_update.proto\x12\x1coperations_research.math_opt\x1a\x1cortools/math_opt/model.proto\x1a(ortools/math_opt/sparse_containers.proto\"\xf7\x01\n\x14VariableUpdatesProto\x12K\n\x0clower_bounds\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12K\n\x0cupper_bounds\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12\x45\n\x08integers\x18\x03 \x01(\x0b\x32\x33.operations_research.math_opt.SparseBoolVectorProto\"\xd6\x02\n\x15ObjectiveUpdatesProto\x12\x1d\n\x10\x64irection_update\x18\x01 \x01(\x08H\x00\x88\x01\x01\x12\x1a\n\roffset_update\x18\x02 \x01(\x01H\x01\x88\x01\x01\x12R\n\x13linear_coefficients\x18\x03 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12U\n\x16quadratic_coefficients\x18\x04 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleMatrixProto\x12\x1c\n\x0fpriority_update\x18\x05 \x01(\x03H\x02\x88\x01\x01\x42\x13\n\x11_direction_updateB\x10\n\x0e_offset_updateB\x12\n\x10_priority_update\"\xec\x03\n\x1f\x41uxiliaryObjectivesUpdatesProto\x12\x1d\n\x15\x64\x65leted_objective_ids\x18\x01 \x03(\x03\x12h\n\x0enew_objectives\x18\x02 \x03(\x0b\x32P.operations_research.math_opt.AuxiliaryObjectivesUpdatesProto.NewObjectivesEntry\x12n\n\x11objective_updates\x18\x03 \x03(\x0b\x32S.operations_research.math_opt.AuxiliaryObjectivesUpdatesProto.ObjectiveUpdatesEntry\x1a\x62\n\x12NewObjectivesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12;\n\x05value\x18\x02 \x01(\x0b\x32,.operations_research.math_opt.ObjectiveProto:\x02\x38\x01\x1al\n\x15ObjectiveUpdatesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x42\n\x05value\x18\x02 \x01(\x0b\x32\x33.operations_research.math_opt.ObjectiveUpdatesProto:\x02\x38\x01\"\xb8\x01\n\x1cLinearConstraintUpdatesProto\x12K\n\x0clower_bounds\x18\x01 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\x12K\n\x0cupper_bounds\x18\x02 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleVectorProto\"\x9c\x02\n\x1fQuadraticConstraintUpdatesProto\x12\x1e\n\x16\x64\x65leted_constraint_ids\x18\x01 \x03(\x03\x12j\n\x0fnew_constraints\x18\x02 \x03(\x0b\x32Q.operations_research.math_opt.QuadraticConstraintUpdatesProto.NewConstraintsEntry\x1am\n\x13NewConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x45\n\x05value\x18\x02 \x01(\x0b\x32\x36.operations_research.math_opt.QuadraticConstraintProto:\x02\x38\x01\"\xae\x02\n%SecondOrderConeConstraintUpdatesProto\x12\x1e\n\x16\x64\x65leted_constraint_ids\x18\x01 \x03(\x03\x12p\n\x0fnew_constraints\x18\x02 \x03(\x0b\x32W.operations_research.math_opt.SecondOrderConeConstraintUpdatesProto.NewConstraintsEntry\x1as\n\x13NewConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12K\n\x05value\x18\x02 \x01(\x0b\x32<.operations_research.math_opt.SecondOrderConeConstraintProto:\x02\x38\x01\"\x8a\x02\n\x19SosConstraintUpdatesProto\x12\x1e\n\x16\x64\x65leted_constraint_ids\x18\x01 \x03(\x03\x12\x64\n\x0fnew_constraints\x18\x02 \x03(\x0b\x32K.operations_research.math_opt.SosConstraintUpdatesProto.NewConstraintsEntry\x1ag\n\x13NewConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12?\n\x05value\x18\x02 \x01(\x0b\x32\x30.operations_research.math_opt.SosConstraintProto:\x02\x38\x01\"\x9c\x02\n\x1fIndicatorConstraintUpdatesProto\x12\x1e\n\x16\x64\x65leted_constraint_ids\x18\x01 \x03(\x03\x12j\n\x0fnew_constraints\x18\x02 \x03(\x0b\x32Q.operations_research.math_opt.IndicatorConstraintUpdatesProto.NewConstraintsEntry\x1am\n\x13NewConstraintsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\x45\n\x05value\x18\x02 \x01(\x0b\x32\x36.operations_research.math_opt.IndicatorConstraintProto:\x02\x38\x01\"\xa6\t\n\x10ModelUpdateProto\x12\x1c\n\x14\x64\x65leted_variable_ids\x18\x01 \x03(\x03\x12%\n\x1d\x64\x65leted_linear_constraint_ids\x18\x02 \x03(\x03\x12L\n\x10variable_updates\x18\x03 \x01(\x0b\x32\x32.operations_research.math_opt.VariableUpdatesProto\x12]\n\x19linear_constraint_updates\x18\x04 \x01(\x0b\x32:.operations_research.math_opt.LinearConstraintUpdatesProto\x12\x43\n\rnew_variables\x18\x05 \x01(\x0b\x32,.operations_research.math_opt.VariablesProto\x12T\n\x16new_linear_constraints\x18\x06 \x01(\x0b\x32\x34.operations_research.math_opt.LinearConstraintsProto\x12N\n\x11objective_updates\x18\x07 \x01(\x0b\x32\x33.operations_research.math_opt.ObjectiveUpdatesProto\x12\x63\n\x1c\x61uxiliary_objectives_updates\x18\r \x01(\x0b\x32=.operations_research.math_opt.AuxiliaryObjectivesUpdatesProto\x12_\n linear_constraint_matrix_updates\x18\x08 \x01(\x0b\x32\x35.operations_research.math_opt.SparseDoubleMatrixProto\x12\x63\n\x1cquadratic_constraint_updates\x18\t \x01(\x0b\x32=.operations_research.math_opt.QuadraticConstraintUpdatesProto\x12q\n$second_order_cone_constraint_updates\x18\x0e \x01(\x0b\x32\x43.operations_research.math_opt.SecondOrderConeConstraintUpdatesProto\x12X\n\x17sos1_constraint_updates\x18\n \x01(\x0b\x32\x37.operations_research.math_opt.SosConstraintUpdatesProto\x12X\n\x17sos2_constraint_updates\x18\x0b \x01(\x0b\x32\x37.operations_research.math_opt.SosConstraintUpdatesProto\x12\x63\n\x1cindicator_constraint_updates\x18\x0c \x01(\x0b\x32=.operations_research.math_opt.IndicatorConstraintUpdatesProtoB\x1e\n\x1a\x63om.google.ortools.mathoptP\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.math_opt.model_update_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\032com.google.ortools.mathoptP\001'
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO_NEWOBJECTIVESENTRY']._loaded_options = None
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO_NEWOBJECTIVESENTRY']._serialized_options = b'8\001'
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO_OBJECTIVEUPDATESENTRY']._loaded_options = None
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO_OBJECTIVEUPDATESENTRY']._serialized_options = b'8\001'
  _globals['_QUADRATICCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._loaded_options = None
  _globals['_QUADRATICCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_SECONDORDERCONECONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._loaded_options = None
  _globals['_SECONDORDERCONECONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_SOSCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._loaded_options = None
  _globals['_SOSCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_INDICATORCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._loaded_options = None
  _globals['_INDICATORCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_options = b'8\001'
  _globals['_VARIABLEUPDATESPROTO']._serialized_start=142
  _globals['_VARIABLEUPDATESPROTO']._serialized_end=389
  _globals['_OBJECTIVEUPDATESPROTO']._serialized_start=392
  _globals['_OBJECTIVEUPDATESPROTO']._serialized_end=734
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO']._serialized_start=737
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO']._serialized_end=1229
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO_NEWOBJECTIVESENTRY']._serialized_start=1021
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO_NEWOBJECTIVESENTRY']._serialized_end=1119
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO_OBJECTIVEUPDATESENTRY']._serialized_start=1121
  _globals['_AUXILIARYOBJECTIVESUPDATESPROTO_OBJECTIVEUPDATESENTRY']._serialized_end=1229
  _globals['_LINEARCONSTRAINTUPDATESPROTO']._serialized_start=1232
  _globals['_LINEARCONSTRAINTUPDATESPROTO']._serialized_end=1416
  _globals['_QUADRATICCONSTRAINTUPDATESPROTO']._serialized_start=1419
  _globals['_QUADRATICCONSTRAINTUPDATESPROTO']._serialized_end=1703
  _globals['_QUADRATICCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_start=1594
  _globals['_QUADRATICCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_end=1703
  _globals['_SECONDORDERCONECONSTRAINTUPDATESPROTO']._serialized_start=1706
  _globals['_SECONDORDERCONECONSTRAINTUPDATESPROTO']._serialized_end=2008
  _globals['_SECONDORDERCONECONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_start=1893
  _globals['_SECONDORDERCONECONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_end=2008
  _globals['_SOSCONSTRAINTUPDATESPROTO']._serialized_start=2011
  _globals['_SOSCONSTRAINTUPDATESPROTO']._serialized_end=2277
  _globals['_SOSCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_start=2174
  _globals['_SOSCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_end=2277
  _globals['_INDICATORCONSTRAINTUPDATESPROTO']._serialized_start=2280
  _globals['_INDICATORCONSTRAINTUPDATESPROTO']._serialized_end=2564
  _globals['_INDICATORCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_start=2455
  _globals['_INDICATORCONSTRAINTUPDATESPROTO_NEWCONSTRAINTSENTRY']._serialized_end=2564
  _globals['_MODELUPDATEPROTO']._serialized_start=2567
  _globals['_MODELUPDATEPROTO']._serialized_end=3757
# @@protoc_insertion_point(module_scope)
