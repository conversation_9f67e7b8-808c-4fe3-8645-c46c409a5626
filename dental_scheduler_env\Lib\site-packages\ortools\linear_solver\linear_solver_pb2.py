# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/linear_solver/linear_solver.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/linear_solver/linear_solver.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from ortools.util import optional_boolean_pb2 as ortools_dot_util_dot_optional__boolean__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)ortools/linear_solver/linear_solver.proto\x12\x13operations_research\x1a#ortools/util/optional_boolean.proto\"\xb2\x01\n\x0fMPVariableProto\x12\x19\n\x0blower_bound\x18\x01 \x01(\x01:\x04-inf\x12\x18\n\x0bupper_bound\x18\x02 \x01(\x01:\x03inf\x12 \n\x15objective_coefficient\x18\x03 \x01(\x01:\x01\x30\x12\x19\n\nis_integer\x18\x04 \x01(\x08:\x05\x66\x61lse\x12\x0e\n\x04name\x18\x05 \x01(\t:\x00\x12\x1d\n\x12\x62ranching_priority\x18\x06 \x01(\x05:\x01\x30\"\xa0\x01\n\x11MPConstraintProto\x12\x15\n\tvar_index\x18\x06 \x03(\x05\x42\x02\x10\x01\x12\x17\n\x0b\x63oefficient\x18\x07 \x03(\x01\x42\x02\x10\x01\x12\x19\n\x0blower_bound\x18\x02 \x01(\x01:\x04-inf\x12\x18\n\x0bupper_bound\x18\x03 \x01(\x01:\x03inf\x12\x0e\n\x04name\x18\x04 \x01(\t:\x00\x12\x16\n\x07is_lazy\x18\x05 \x01(\x08:\x05\x66\x61lse\"\xf7\x04\n\x18MPGeneralConstraintProto\x12\x0e\n\x04name\x18\x01 \x01(\t:\x00\x12J\n\x14indicator_constraint\x18\x02 \x01(\x0b\x32*.operations_research.MPIndicatorConstraintH\x00\x12>\n\x0esos_constraint\x18\x03 \x01(\x0b\x32$.operations_research.MPSosConstraintH\x00\x12J\n\x14quadratic_constraint\x18\x04 \x01(\x0b\x32*.operations_research.MPQuadraticConstraintH\x00\x12>\n\x0e\x61\x62s_constraint\x18\x05 \x01(\x0b\x32$.operations_research.MPAbsConstraintH\x00\x12@\n\x0e\x61nd_constraint\x18\x06 \x01(\x0b\x32&.operations_research.MPArrayConstraintH\x00\x12?\n\ror_constraint\x18\x07 \x01(\x0b\x32&.operations_research.MPArrayConstraintH\x00\x12L\n\x0emin_constraint\x18\x08 \x01(\x0b\x32\x32.operations_research.MPArrayWithConstantConstraintH\x00\x12L\n\x0emax_constraint\x18\t \x01(\x0b\x32\x32.operations_research.MPArrayWithConstantConstraintH\x00\x42\x14\n\x12general_constraint\"y\n\x15MPIndicatorConstraint\x12\x11\n\tvar_index\x18\x01 \x01(\x05\x12\x11\n\tvar_value\x18\x02 \x01(\x05\x12:\n\nconstraint\x18\x03 \x01(\x0b\x32&.operations_research.MPConstraintProto\"\x9f\x01\n\x0fMPSosConstraint\x12\x45\n\x04type\x18\x01 \x01(\x0e\x32).operations_research.MPSosConstraint.Type:\x0cSOS1_DEFAULT\x12\x11\n\tvar_index\x18\x02 \x03(\x05\x12\x0e\n\x06weight\x18\x03 \x03(\x01\"\"\n\x04Type\x12\x10\n\x0cSOS1_DEFAULT\x10\x00\x12\x08\n\x04SOS2\x10\x01\"\xb4\x01\n\x15MPQuadraticConstraint\x12\x11\n\tvar_index\x18\x01 \x03(\x05\x12\x13\n\x0b\x63oefficient\x18\x02 \x03(\x01\x12\x13\n\x0bqvar1_index\x18\x03 \x03(\x05\x12\x13\n\x0bqvar2_index\x18\x04 \x03(\x05\x12\x14\n\x0cqcoefficient\x18\x05 \x03(\x01\x12\x19\n\x0blower_bound\x18\x06 \x01(\x01:\x04-inf\x12\x18\n\x0bupper_bound\x18\x07 \x01(\x01:\x03inf\"A\n\x0fMPAbsConstraint\x12\x11\n\tvar_index\x18\x01 \x01(\x05\x12\x1b\n\x13resultant_var_index\x18\x02 \x01(\x05\"C\n\x11MPArrayConstraint\x12\x11\n\tvar_index\x18\x01 \x03(\x05\x12\x1b\n\x13resultant_var_index\x18\x02 \x01(\x05\"a\n\x1dMPArrayWithConstantConstraint\x12\x11\n\tvar_index\x18\x01 \x03(\x05\x12\x10\n\x08\x63onstant\x18\x02 \x01(\x01\x12\x1b\n\x13resultant_var_index\x18\x03 \x01(\x05\"U\n\x14MPQuadraticObjective\x12\x13\n\x0bqvar1_index\x18\x01 \x03(\x05\x12\x13\n\x0bqvar2_index\x18\x02 \x03(\x05\x12\x13\n\x0b\x63oefficient\x18\x03 \x03(\x01\"I\n\x19PartialVariableAssignment\x12\x15\n\tvar_index\x18\x01 \x03(\x05\x42\x02\x10\x01\x12\x15\n\tvar_value\x18\x02 \x03(\x01\x42\x02\x10\x01\"\xe4\x05\n\x0cMPModelProto\x12\x36\n\x08variable\x18\x03 \x03(\x0b\x32$.operations_research.MPVariableProto\x12:\n\nconstraint\x18\x04 \x03(\x0b\x32&.operations_research.MPConstraintProto\x12I\n\x12general_constraint\x18\x07 \x03(\x0b\x32-.operations_research.MPGeneralConstraintProto\x12\x17\n\x08maximize\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x1b\n\x10objective_offset\x18\x02 \x01(\x01:\x01\x30\x12\x46\n\x13quadratic_objective\x18\x08 \x01(\x0b\x32).operations_research.MPQuadraticObjective\x12\x0e\n\x04name\x18\x05 \x01(\t:\x00\x12\x45\n\rsolution_hint\x18\x06 \x01(\x0b\x32..operations_research.PartialVariableAssignment\x12@\n\nannotation\x18\t \x03(\x0b\x32,.operations_research.MPModelProto.Annotation\x1a\xfd\x01\n\nAnnotation\x12L\n\x0btarget_type\x18\x01 \x01(\x0e\x32\x37.operations_research.MPModelProto.Annotation.TargetType\x12\x14\n\x0ctarget_index\x18\x02 \x01(\x05\x12\x13\n\x0btarget_name\x18\x03 \x01(\t\x12\x13\n\x0bpayload_key\x18\x04 \x01(\t\x12\x15\n\rpayload_value\x18\x05 \x01(\t\"J\n\nTargetType\x12\x14\n\x10VARIABLE_DEFAULT\x10\x00\x12\x0e\n\nCONSTRAINT\x10\x01\x12\x16\n\x12GENERAL_CONSTRAINT\x10\x02\"\x1f\n\x0eOptionalDouble\x12\r\n\x05value\x18\x01 \x01(\x01\"\xbd\x04\n\x18MPSolverCommonParameters\x12=\n\x10relative_mip_gap\x18\x01 \x01(\x0b\x32#.operations_research.OptionalDouble\x12=\n\x10primal_tolerance\x18\x02 \x01(\x0b\x32#.operations_research.OptionalDouble\x12;\n\x0e\x64ual_tolerance\x18\x03 \x01(\x0b\x32#.operations_research.OptionalDouble\x12j\n\x0clp_algorithm\x18\x04 \x01(\x0e\x32?.operations_research.MPSolverCommonParameters.LPAlgorithmValues:\x13LP_ALGO_UNSPECIFIED\x12H\n\x08presolve\x18\x05 \x01(\x0e\x32$.operations_research.OptionalBoolean:\x10\x42OOL_UNSPECIFIED\x12G\n\x07scaling\x18\x07 \x01(\x0e\x32$.operations_research.OptionalBoolean:\x10\x42OOL_UNSPECIFIED\"g\n\x11LPAlgorithmValues\x12\x17\n\x13LP_ALGO_UNSPECIFIED\x10\x00\x12\x10\n\x0cLP_ALGO_DUAL\x10\x01\x12\x12\n\x0eLP_ALGO_PRIMAL\x10\x02\x12\x13\n\x0fLP_ALGO_BARRIER\x10\x03\"\xb3\x03\n\x11MPModelDeltaProto\x12 \n\x18\x62\x61seline_model_file_path\x18\x01 \x01(\t\x12Y\n\x12variable_overrides\x18\x02 \x03(\x0b\x32=.operations_research.MPModelDeltaProto.VariableOverridesEntry\x12]\n\x14\x63onstraint_overrides\x18\x03 \x03(\x0b\x32?.operations_research.MPModelDeltaProto.ConstraintOverridesEntry\x1a^\n\x16VariableOverridesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x33\n\x05value\x18\x02 \x01(\x0b\x32$.operations_research.MPVariableProto:\x02\x38\x01\x1a\x62\n\x18\x43onstraintOverridesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x35\n\x05value\x18\x02 \x01(\x0b\x32&.operations_research.MPConstraintProto:\x02\x38\x01\"\x9b\x08\n\x0eMPModelRequest\x12\x30\n\x05model\x18\x01 \x01(\x0b\x32!.operations_research.MPModelProto\x12\\\n\x0bsolver_type\x18\x02 \x01(\x0e\x32..operations_research.MPModelRequest.SolverType:\x17GLOP_LINEAR_PROGRAMMING\x12!\n\x19solver_time_limit_seconds\x18\x03 \x01(\x01\x12,\n\x1d\x65nable_internal_solver_output\x18\x04 \x01(\x08:\x05\x66\x61lse\x12\"\n\x1asolver_specific_parameters\x18\x05 \x01(\t\x12\x38\n)ignore_solver_specific_parameters_failure\x18\t \x01(\x08:\x05\x66\x61lse\x12;\n\x0bmodel_delta\x18\x08 \x01(\x0b\x32&.operations_research.MPModelDeltaProto\x12.\n#populate_additional_solutions_up_to\x18\x0b \x01(\x05:\x01\x30\"\xdc\x04\n\nSolverType\x12\x1a\n\x16\x43LP_LINEAR_PROGRAMMING\x10\x00\x12\x1b\n\x17GLOP_LINEAR_PROGRAMMING\x10\x02\x12\x1b\n\x17GLPK_LINEAR_PROGRAMMING\x10\x01\x12\x1d\n\x19GUROBI_LINEAR_PROGRAMMING\x10\x06\x12\x1d\n\x19XPRESS_LINEAR_PROGRAMMING\x10\x65\x12\x1c\n\x18\x43PLEX_LINEAR_PROGRAMMING\x10\n\x12\x1c\n\x18HIGHS_LINEAR_PROGRAMMING\x10\x0f\x12\"\n\x1eSCIP_MIXED_INTEGER_PROGRAMMING\x10\x03\x12\"\n\x1eGLPK_MIXED_INTEGER_PROGRAMMING\x10\x04\x12!\n\x1d\x43\x42\x43_MIXED_INTEGER_PROGRAMMING\x10\x05\x12$\n GUROBI_MIXED_INTEGER_PROGRAMMING\x10\x07\x12$\n XPRESS_MIXED_INTEGER_PROGRAMMING\x10\x66\x12#\n\x1f\x43PLEX_MIXED_INTEGER_PROGRAMMING\x10\x0b\x12#\n\x1fHIGHS_MIXED_INTEGER_PROGRAMMING\x10\x10\x12\x1b\n\x17\x42OP_INTEGER_PROGRAMMING\x10\x0c\x12\x1b\n\x17SAT_INTEGER_PROGRAMMING\x10\x0e\x12\x1b\n\x17PDLP_LINEAR_PROGRAMMING\x10\x08\x12&\n\"KNAPSACK_MIXED_INTEGER_PROGRAMMING\x10\r\"A\n\nMPSolution\x12\x17\n\x0fobjective_value\x18\x01 \x01(\x01\x12\x1a\n\x0evariable_value\x18\x02 \x03(\x01\x42\x02\x10\x01\"O\n\x0bMPSolveInfo\x12\x1f\n\x17solve_wall_time_seconds\x18\x01 \x01(\x01\x12\x1f\n\x17solve_user_time_seconds\x18\x02 \x01(\x01\"\x96\x03\n\x12MPSolutionResponse\x12T\n\x06status\x18\x01 \x01(\x0e\x32+.operations_research.MPSolverResponseStatus:\x17MPSOLVER_UNKNOWN_STATUS\x12\x12\n\nstatus_str\x18\x07 \x01(\t\x12\x17\n\x0fobjective_value\x18\x02 \x01(\x01\x12\x1c\n\x14\x62\x65st_objective_bound\x18\x05 \x01(\x01\x12\x1a\n\x0evariable_value\x18\x03 \x03(\x01\x42\x02\x10\x01\x12\x34\n\nsolve_info\x18\n \x01(\x0b\x32 .operations_research.MPSolveInfo\x12\x1c\n\x14solver_specific_info\x18\x0b \x01(\x0c\x12\x16\n\ndual_value\x18\x04 \x03(\x01\x42\x02\x10\x01\x12\x18\n\x0creduced_cost\x18\x06 \x03(\x01\x42\x02\x10\x01\x12=\n\x14\x61\x64\x64itional_solutions\x18\x08 \x03(\x0b\x32\x1f.operations_research.MPSolution*\xbd\x03\n\x16MPSolverResponseStatus\x12\x14\n\x10MPSOLVER_OPTIMAL\x10\x00\x12\x15\n\x11MPSOLVER_FEASIBLE\x10\x01\x12\x17\n\x13MPSOLVER_INFEASIBLE\x10\x02\x12\x16\n\x12MPSOLVER_UNBOUNDED\x10\x03\x12\x15\n\x11MPSOLVER_ABNORMAL\x10\x04\x12\x17\n\x13MPSOLVER_NOT_SOLVED\x10\x06\x12\x1b\n\x17MPSOLVER_MODEL_IS_VALID\x10\x61\x12\x1e\n\x1aMPSOLVER_CANCELLED_BY_USER\x10\x62\x12\x1b\n\x17MPSOLVER_UNKNOWN_STATUS\x10\x63\x12\x1a\n\x16MPSOLVER_MODEL_INVALID\x10\x05\x12(\n$MPSOLVER_MODEL_INVALID_SOLUTION_HINT\x10T\x12,\n(MPSOLVER_MODEL_INVALID_SOLVER_PARAMETERS\x10U\x12$\n MPSOLVER_SOLVER_TYPE_UNAVAILABLE\x10\x07\x12!\n\x1dMPSOLVER_INCOMPATIBLE_OPTIONS\x10qB#\n\x1f\x63om.google.ortools.linearsolverP\x01')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.linear_solver.linear_solver_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\037com.google.ortools.linearsolverP\001'
  _globals['_MPCONSTRAINTPROTO'].fields_by_name['var_index']._loaded_options = None
  _globals['_MPCONSTRAINTPROTO'].fields_by_name['var_index']._serialized_options = b'\020\001'
  _globals['_MPCONSTRAINTPROTO'].fields_by_name['coefficient']._loaded_options = None
  _globals['_MPCONSTRAINTPROTO'].fields_by_name['coefficient']._serialized_options = b'\020\001'
  _globals['_PARTIALVARIABLEASSIGNMENT'].fields_by_name['var_index']._loaded_options = None
  _globals['_PARTIALVARIABLEASSIGNMENT'].fields_by_name['var_index']._serialized_options = b'\020\001'
  _globals['_PARTIALVARIABLEASSIGNMENT'].fields_by_name['var_value']._loaded_options = None
  _globals['_PARTIALVARIABLEASSIGNMENT'].fields_by_name['var_value']._serialized_options = b'\020\001'
  _globals['_MPMODELDELTAPROTO_VARIABLEOVERRIDESENTRY']._loaded_options = None
  _globals['_MPMODELDELTAPROTO_VARIABLEOVERRIDESENTRY']._serialized_options = b'8\001'
  _globals['_MPMODELDELTAPROTO_CONSTRAINTOVERRIDESENTRY']._loaded_options = None
  _globals['_MPMODELDELTAPROTO_CONSTRAINTOVERRIDESENTRY']._serialized_options = b'8\001'
  _globals['_MPSOLUTION'].fields_by_name['variable_value']._loaded_options = None
  _globals['_MPSOLUTION'].fields_by_name['variable_value']._serialized_options = b'\020\001'
  _globals['_MPSOLUTIONRESPONSE'].fields_by_name['variable_value']._loaded_options = None
  _globals['_MPSOLUTIONRESPONSE'].fields_by_name['variable_value']._serialized_options = b'\020\001'
  _globals['_MPSOLUTIONRESPONSE'].fields_by_name['dual_value']._loaded_options = None
  _globals['_MPSOLUTIONRESPONSE'].fields_by_name['dual_value']._serialized_options = b'\020\001'
  _globals['_MPSOLUTIONRESPONSE'].fields_by_name['reduced_cost']._loaded_options = None
  _globals['_MPSOLUTIONRESPONSE'].fields_by_name['reduced_cost']._serialized_options = b'\020\001'
  _globals['_MPSOLVERRESPONSESTATUS']._serialized_start=5348
  _globals['_MPSOLVERRESPONSESTATUS']._serialized_end=5793
  _globals['_MPVARIABLEPROTO']._serialized_start=104
  _globals['_MPVARIABLEPROTO']._serialized_end=282
  _globals['_MPCONSTRAINTPROTO']._serialized_start=285
  _globals['_MPCONSTRAINTPROTO']._serialized_end=445
  _globals['_MPGENERALCONSTRAINTPROTO']._serialized_start=448
  _globals['_MPGENERALCONSTRAINTPROTO']._serialized_end=1079
  _globals['_MPINDICATORCONSTRAINT']._serialized_start=1081
  _globals['_MPINDICATORCONSTRAINT']._serialized_end=1202
  _globals['_MPSOSCONSTRAINT']._serialized_start=1205
  _globals['_MPSOSCONSTRAINT']._serialized_end=1364
  _globals['_MPSOSCONSTRAINT_TYPE']._serialized_start=1330
  _globals['_MPSOSCONSTRAINT_TYPE']._serialized_end=1364
  _globals['_MPQUADRATICCONSTRAINT']._serialized_start=1367
  _globals['_MPQUADRATICCONSTRAINT']._serialized_end=1547
  _globals['_MPABSCONSTRAINT']._serialized_start=1549
  _globals['_MPABSCONSTRAINT']._serialized_end=1614
  _globals['_MPARRAYCONSTRAINT']._serialized_start=1616
  _globals['_MPARRAYCONSTRAINT']._serialized_end=1683
  _globals['_MPARRAYWITHCONSTANTCONSTRAINT']._serialized_start=1685
  _globals['_MPARRAYWITHCONSTANTCONSTRAINT']._serialized_end=1782
  _globals['_MPQUADRATICOBJECTIVE']._serialized_start=1784
  _globals['_MPQUADRATICOBJECTIVE']._serialized_end=1869
  _globals['_PARTIALVARIABLEASSIGNMENT']._serialized_start=1871
  _globals['_PARTIALVARIABLEASSIGNMENT']._serialized_end=1944
  _globals['_MPMODELPROTO']._serialized_start=1947
  _globals['_MPMODELPROTO']._serialized_end=2687
  _globals['_MPMODELPROTO_ANNOTATION']._serialized_start=2434
  _globals['_MPMODELPROTO_ANNOTATION']._serialized_end=2687
  _globals['_MPMODELPROTO_ANNOTATION_TARGETTYPE']._serialized_start=2613
  _globals['_MPMODELPROTO_ANNOTATION_TARGETTYPE']._serialized_end=2687
  _globals['_OPTIONALDOUBLE']._serialized_start=2689
  _globals['_OPTIONALDOUBLE']._serialized_end=2720
  _globals['_MPSOLVERCOMMONPARAMETERS']._serialized_start=2723
  _globals['_MPSOLVERCOMMONPARAMETERS']._serialized_end=3296
  _globals['_MPSOLVERCOMMONPARAMETERS_LPALGORITHMVALUES']._serialized_start=3193
  _globals['_MPSOLVERCOMMONPARAMETERS_LPALGORITHMVALUES']._serialized_end=3296
  _globals['_MPMODELDELTAPROTO']._serialized_start=3299
  _globals['_MPMODELDELTAPROTO']._serialized_end=3734
  _globals['_MPMODELDELTAPROTO_VARIABLEOVERRIDESENTRY']._serialized_start=3540
  _globals['_MPMODELDELTAPROTO_VARIABLEOVERRIDESENTRY']._serialized_end=3634
  _globals['_MPMODELDELTAPROTO_CONSTRAINTOVERRIDESENTRY']._serialized_start=3636
  _globals['_MPMODELDELTAPROTO_CONSTRAINTOVERRIDESENTRY']._serialized_end=3734
  _globals['_MPMODELREQUEST']._serialized_start=3737
  _globals['_MPMODELREQUEST']._serialized_end=4788
  _globals['_MPMODELREQUEST_SOLVERTYPE']._serialized_start=4184
  _globals['_MPMODELREQUEST_SOLVERTYPE']._serialized_end=4788
  _globals['_MPSOLUTION']._serialized_start=4790
  _globals['_MPSOLUTION']._serialized_end=4855
  _globals['_MPSOLVEINFO']._serialized_start=4857
  _globals['_MPSOLVEINFO']._serialized_end=4936
  _globals['_MPSOLUTIONRESPONSE']._serialized_start=4939
  _globals['_MPSOLUTIONRESPONSE']._serialized_end=5345
# @@protoc_insertion_point(module_scope)
