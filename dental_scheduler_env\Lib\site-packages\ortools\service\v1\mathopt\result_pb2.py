# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: ortools/service/v1/mathopt/result.proto
# Protobuf Python Version: 6.31.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    6,
    31,
    1,
    '',
    'ortools/service/v1/mathopt/result.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from ortools.service.v1.mathopt import solution_pb2 as ortools_dot_service_dot_v1_dot_mathopt_dot_solution__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\'ortools/service/v1/mathopt/result.proto\x12&operations_research.service.v1.mathopt\x1a\x1egoogle/protobuf/duration.proto\x1a)ortools/service/v1/mathopt/solution.proto\"\xe3\x01\n\x12ProblemStatusProto\x12U\n\rprimal_status\x18\x01 \x01(\x0e\x32>.operations_research.service.v1.mathopt.FeasibilityStatusProto\x12S\n\x0b\x64ual_status\x18\x02 \x01(\x0e\x32>.operations_research.service.v1.mathopt.FeasibilityStatusProto\x12!\n\x19primal_or_dual_infeasible\x18\x03 \x01(\x08\"\x8c\x02\n\x0fSolveStatsProto\x12-\n\nsolve_time\x18\x01 \x01(\x0b\x32\x19.google.protobuf.Duration\x12R\n\x0eproblem_status\x18\x04 \x01(\x0b\x32:.operations_research.service.v1.mathopt.ProblemStatusProto\x12\x1a\n\x12simplex_iterations\x18\x05 \x01(\x03\x12\x1a\n\x12\x62\x61rrier_iterations\x18\x06 \x01(\x03\x12\x1e\n\x16\x66irst_order_iterations\x18\x08 \x01(\x03\x12\x12\n\nnode_count\x18\x07 \x01(\x03J\x04\x08\x02\x10\x03J\x04\x08\x03\x10\x04\"@\n\x14ObjectiveBoundsProto\x12\x14\n\x0cprimal_bound\x18\x02 \x01(\x01\x12\x12\n\ndual_bound\x18\x03 \x01(\x01\"\xe1\x02\n\x10TerminationProto\x12N\n\x06reason\x18\x01 \x01(\x0e\x32>.operations_research.service.v1.mathopt.TerminationReasonProto\x12\x41\n\x05limit\x18\x02 \x01(\x0e\x32\x32.operations_research.service.v1.mathopt.LimitProto\x12\x0e\n\x06\x64\x65tail\x18\x03 \x01(\t\x12R\n\x0eproblem_status\x18\x04 \x01(\x0b\x32:.operations_research.service.v1.mathopt.ProblemStatusProto\x12V\n\x10objective_bounds\x18\x05 \x01(\x0b\x32<.operations_research.service.v1.mathopt.ObjectiveBoundsProto\"\xa7\x03\n\x10SolveResultProto\x12M\n\x0btermination\x18\x02 \x01(\x0b\x32\x38.operations_research.service.v1.mathopt.TerminationProto\x12H\n\tsolutions\x18\x03 \x03(\x0b\x32\x35.operations_research.service.v1.mathopt.SolutionProto\x12K\n\x0bprimal_rays\x18\x04 \x03(\x0b\x32\x36.operations_research.service.v1.mathopt.PrimalRayProto\x12G\n\tdual_rays\x18\x05 \x03(\x0b\x32\x34.operations_research.service.v1.mathopt.DualRayProto\x12L\n\x0bsolve_stats\x18\x06 \x01(\x0b\x32\x37.operations_research.service.v1.mathopt.SolveStatsProtoJ\x04\x08\x07\x10\x08J\x04\x08\x08\x10\tJ\x04\x08\t\x10\nJ\x04\x08\x01\x10\x02*\xa5\x01\n\x16\x46\x65\x61sibilityStatusProto\x12\"\n\x1e\x46\x45\x41SIBILITY_STATUS_UNSPECIFIED\x10\x00\x12#\n\x1f\x46\x45\x41SIBILITY_STATUS_UNDETERMINED\x10\x01\x12\x1f\n\x1b\x46\x45\x41SIBILITY_STATUS_FEASIBLE\x10\x02\x12!\n\x1d\x46\x45\x41SIBILITY_STATUS_INFEASIBLE\x10\x03*\x8a\x03\n\x16TerminationReasonProto\x12\"\n\x1eTERMINATION_REASON_UNSPECIFIED\x10\x00\x12\x1e\n\x1aTERMINATION_REASON_OPTIMAL\x10\x01\x12!\n\x1dTERMINATION_REASON_INFEASIBLE\x10\x02\x12 \n\x1cTERMINATION_REASON_UNBOUNDED\x10\x03\x12.\n*TERMINATION_REASON_INFEASIBLE_OR_UNBOUNDED\x10\x04\x12 \n\x1cTERMINATION_REASON_IMPRECISE\x10\x05\x12\x1f\n\x1bTERMINATION_REASON_FEASIBLE\x10\t\x12(\n$TERMINATION_REASON_NO_SOLUTION_FOUND\x10\x06\x12&\n\"TERMINATION_REASON_NUMERICAL_ERROR\x10\x07\x12\"\n\x1eTERMINATION_REASON_OTHER_ERROR\x10\x08*\x8e\x02\n\nLimitProto\x12\x15\n\x11LIMIT_UNSPECIFIED\x10\x00\x12\x16\n\x12LIMIT_UNDETERMINED\x10\x01\x12\x13\n\x0fLIMIT_ITERATION\x10\x02\x12\x0e\n\nLIMIT_TIME\x10\x03\x12\x0e\n\nLIMIT_NODE\x10\x04\x12\x12\n\x0eLIMIT_SOLUTION\x10\x05\x12\x10\n\x0cLIMIT_MEMORY\x10\x06\x12\x10\n\x0cLIMIT_CUTOFF\x10\x0c\x12\x13\n\x0fLIMIT_OBJECTIVE\x10\x07\x12\x0e\n\nLIMIT_NORM\x10\x08\x12\x15\n\x11LIMIT_INTERRUPTED\x10\t\x12\x17\n\x13LIMIT_SLOW_PROGRESS\x10\n\x12\x0f\n\x0bLIMIT_OTHER\x10\x0b\x42\x42\n%com.google.ortools.service.v1.mathoptP\x01\xaa\x02\x16Google.OrTools.Serviceb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'ortools.service.v1.mathopt.result_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n%com.google.ortools.service.v1.mathoptP\001\252\002\026Google.OrTools.Service'
  _globals['_FEASIBILITYSTATUSPROTO']._serialized_start=1508
  _globals['_FEASIBILITYSTATUSPROTO']._serialized_end=1673
  _globals['_TERMINATIONREASONPROTO']._serialized_start=1676
  _globals['_TERMINATIONREASONPROTO']._serialized_end=2070
  _globals['_LIMITPROTO']._serialized_start=2073
  _globals['_LIMITPROTO']._serialized_end=2343
  _globals['_PROBLEMSTATUSPROTO']._serialized_start=159
  _globals['_PROBLEMSTATUSPROTO']._serialized_end=386
  _globals['_SOLVESTATSPROTO']._serialized_start=389
  _globals['_SOLVESTATSPROTO']._serialized_end=657
  _globals['_OBJECTIVEBOUNDSPROTO']._serialized_start=659
  _globals['_OBJECTIVEBOUNDSPROTO']._serialized_end=723
  _globals['_TERMINATIONPROTO']._serialized_start=726
  _globals['_TERMINATIONPROTO']._serialized_end=1079
  _globals['_SOLVERESULTPROTO']._serialized_start=1082
  _globals['_SOLVERESULTPROTO']._serialized_end=1505
# @@protoc_insertion_point(module_scope)
