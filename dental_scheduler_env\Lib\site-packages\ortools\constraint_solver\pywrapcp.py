# This file was automatically generated by SWIG (https://www.swig.org).
# Version 4.3.0
#
# Do not make changes to this file unless you know what you are doing - modify
# the SWIG interface file instead.

from sys import version_info as _swig_python_version_info
# Import the low-level C/C++ module
if __package__ or "." in __name__:
    from . import _pywrapcp
else:
    import _pywrapcp

try:
    import builtins as __builtin__
except ImportError:
    import __builtin__

def _swig_repr(self):
    try:
        strthis = "proxy of " + self.this.__repr__()
    except __builtin__.Exception:
        strthis = ""
    return "<%s.%s; %s >" % (self.__class__.__module__, self.__class__.__name__, strthis,)


def _swig_setattr_nondynamic_instance_variable(set):
    def set_instance_attr(self, name, value):
        if name == "this":
            set(self, name, value)
        elif name == "thisown":
            self.this.own(value)
        elif hasattr(self, name) and isinstance(getattr(type(self), name), property):
            set(self, name, value)
        else:
            raise AttributeError("You cannot add instance attributes to %s" % self)
    return set_instance_attr


def _swig_setattr_nondynamic_class_variable(set):
    def set_class_attr(cls, name, value):
        if hasattr(cls, name) and not isinstance(getattr(cls, name), property):
            set(cls, name, value)
        else:
            raise AttributeError("You cannot add class attributes to %s" % cls)
    return set_class_attr


def _swig_add_metaclass(metaclass):
    """Class decorator for adding a metaclass to a SWIG wrapped class - a slimmed down version of six.add_metaclass"""
    def wrapper(cls):
        return metaclass(cls.__name__, cls.__bases__, cls.__dict__.copy())
    return wrapper


class _SwigNonDynamicMeta(type):
    """Meta class to enforce nondynamic attributes (no new attributes) for a class"""
    __setattr__ = _swig_setattr_nondynamic_class_variable(type.__setattr__)


import weakref

class DefaultPhaseParameters(object):
    r"""
    This struct holds all parameters for the default search.
    DefaultPhaseParameters is only used by Solver::MakeDefaultPhase methods.
    Note this is for advanced users only.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    CHOOSE_MAX_SUM_IMPACT = _pywrapcp.DefaultPhaseParameters_CHOOSE_MAX_SUM_IMPACT
    CHOOSE_MAX_AVERAGE_IMPACT = _pywrapcp.DefaultPhaseParameters_CHOOSE_MAX_AVERAGE_IMPACT
    CHOOSE_MAX_VALUE_IMPACT = _pywrapcp.DefaultPhaseParameters_CHOOSE_MAX_VALUE_IMPACT
    SELECT_MIN_IMPACT = _pywrapcp.DefaultPhaseParameters_SELECT_MIN_IMPACT
    SELECT_MAX_IMPACT = _pywrapcp.DefaultPhaseParameters_SELECT_MAX_IMPACT
    NONE = _pywrapcp.DefaultPhaseParameters_NONE
    NORMAL = _pywrapcp.DefaultPhaseParameters_NORMAL
    VERBOSE = _pywrapcp.DefaultPhaseParameters_VERBOSE
    var_selection_schema = property(_pywrapcp.DefaultPhaseParameters_var_selection_schema_get, _pywrapcp.DefaultPhaseParameters_var_selection_schema_set, doc=r"""
    This parameter describes how the next variable to instantiate
    will be chosen.
    """)
    value_selection_schema = property(_pywrapcp.DefaultPhaseParameters_value_selection_schema_get, _pywrapcp.DefaultPhaseParameters_value_selection_schema_set, doc=r"""This parameter describes which value to select for a given var.""")
    initialization_splits = property(_pywrapcp.DefaultPhaseParameters_initialization_splits_get, _pywrapcp.DefaultPhaseParameters_initialization_splits_set, doc=r"""
    Maximum number of intervals that the initialization of impacts will scan
    per variable.
    """)
    run_all_heuristics = property(_pywrapcp.DefaultPhaseParameters_run_all_heuristics_get, _pywrapcp.DefaultPhaseParameters_run_all_heuristics_set, doc=r"""
    The default phase will run heuristics periodically. This parameter
    indicates if we should run all heuristics, or a randomly selected
    one.
    """)
    heuristic_period = property(_pywrapcp.DefaultPhaseParameters_heuristic_period_get, _pywrapcp.DefaultPhaseParameters_heuristic_period_set, doc=r"""
    The distance in nodes between each run of the heuristics. A
    negative or null value will mean that we will not run heuristics
    at all.
    """)
    heuristic_num_failures_limit = property(_pywrapcp.DefaultPhaseParameters_heuristic_num_failures_limit_get, _pywrapcp.DefaultPhaseParameters_heuristic_num_failures_limit_set, doc=r"""The failure limit for each heuristic that we run.""")
    persistent_impact = property(_pywrapcp.DefaultPhaseParameters_persistent_impact_get, _pywrapcp.DefaultPhaseParameters_persistent_impact_set, doc=r"""
    Whether to keep the impact from the first search for other searches,
    or to recompute the impact for each new search.
    """)
    random_seed = property(_pywrapcp.DefaultPhaseParameters_random_seed_get, _pywrapcp.DefaultPhaseParameters_random_seed_set, doc=r"""Seed used to initialize the random part in some heuristics.""")
    display_level = property(_pywrapcp.DefaultPhaseParameters_display_level_get, _pywrapcp.DefaultPhaseParameters_display_level_set, doc=r"""
    This represents the amount of information displayed by the default search.
    NONE means no display, VERBOSE means extra information.
    """)
    decision_builder = property(_pywrapcp.DefaultPhaseParameters_decision_builder_get, _pywrapcp.DefaultPhaseParameters_decision_builder_set, doc=r"""When defined, this overrides the default impact based decision builder.""")

    def __init__(self):
        _pywrapcp.DefaultPhaseParameters_swiginit(self, _pywrapcp.new_DefaultPhaseParameters())
    __swig_destroy__ = _pywrapcp.delete_DefaultPhaseParameters

# Register DefaultPhaseParameters in _pywrapcp:
_pywrapcp.DefaultPhaseParameters_swigregister(DefaultPhaseParameters)
class Solver(object):
    r"""
    Solver Class

    A solver represents the main computation engine. It implements the entire
    range of Constraint Programming protocols:
      - Reversibility
      - Propagation
      - Search

    Usually, Constraint Programming code consists of
      - the creation of the Solver,
      - the creation of the decision variables of the model,
      - the creation of the constraints of the model and their addition to the
        solver() through the AddConstraint() method,
      - the creation of the main DecisionBuilder class,
      - the launch of the solve() method with the decision builder.

    For the time being, Solver is neither MT_SAFE nor MT_HOT.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    INT_VAR_DEFAULT = _pywrapcp.Solver_INT_VAR_DEFAULT
    r"""The default behavior is CHOOSE_FIRST_UNBOUND."""
    INT_VAR_SIMPLE = _pywrapcp.Solver_INT_VAR_SIMPLE
    r"""The simple selection is CHOOSE_FIRST_UNBOUND."""
    CHOOSE_FIRST_UNBOUND = _pywrapcp.Solver_CHOOSE_FIRST_UNBOUND
    r"""
    Select the first unbound variable.
    Variables are considered in the order of the vector of IntVars used
    to create the selector.
    """
    CHOOSE_RANDOM = _pywrapcp.Solver_CHOOSE_RANDOM
    r"""Randomly select one of the remaining unbound variables."""
    CHOOSE_MIN_SIZE_LOWEST_MIN = _pywrapcp.Solver_CHOOSE_MIN_SIZE_LOWEST_MIN
    r"""
    Among unbound variables, select the variable with the smallest size,
    i.e., the smallest number of possible values.
    In case of a tie, the selected variables is the one with the lowest min
    value.
    In case of a tie, the first one is selected, first being defined by the
    order in the vector of IntVars used to create the selector.
    """
    CHOOSE_MIN_SIZE_HIGHEST_MIN = _pywrapcp.Solver_CHOOSE_MIN_SIZE_HIGHEST_MIN
    r"""
    Among unbound variables, select the variable with the smallest size,
    i.e., the smallest number of possible values.
    In case of a tie, the selected variable is the one with the highest min
    value.
    In case of a tie, the first one is selected, first being defined by the
    order in the vector of IntVars used to create the selector.
    """
    CHOOSE_MIN_SIZE_LOWEST_MAX = _pywrapcp.Solver_CHOOSE_MIN_SIZE_LOWEST_MAX
    r"""
    Among unbound variables, select the variable with the smallest size,
    i.e., the smallest number of possible values.
    In case of a tie, the selected variables is the one with the lowest max
    value.
    In case of a tie, the first one is selected, first being defined by the
    order in the vector of IntVars used to create the selector.
    """
    CHOOSE_MIN_SIZE_HIGHEST_MAX = _pywrapcp.Solver_CHOOSE_MIN_SIZE_HIGHEST_MAX
    r"""
    Among unbound variables, select the variable with the smallest size,
    i.e., the smallest number of possible values.
    In case of a tie, the selected variable is the one with the highest max
    value.
    In case of a tie, the first one is selected, first being defined by the
    order in the vector of IntVars used to create the selector.
    """
    CHOOSE_LOWEST_MIN = _pywrapcp.Solver_CHOOSE_LOWEST_MIN
    r"""
    Among unbound variables, select the variable with the smallest minimal
    value.
    In case of a tie, the first one is selected, "first" defined by the
    order in the vector of IntVars used to create the selector.
    """
    CHOOSE_HIGHEST_MAX = _pywrapcp.Solver_CHOOSE_HIGHEST_MAX
    r"""
    Among unbound variables, select the variable with the highest maximal
    value.
    In case of a tie, the first one is selected, first being defined by the
    order in the vector of IntVars used to create the selector.
    """
    CHOOSE_MIN_SIZE = _pywrapcp.Solver_CHOOSE_MIN_SIZE
    r"""
    Among unbound variables, select the variable with the smallest size.
    In case of a tie, the first one is selected, first being defined by the
    order in the vector of IntVars used to create the selector.
    """
    CHOOSE_MAX_SIZE = _pywrapcp.Solver_CHOOSE_MAX_SIZE
    r"""
    Among unbound variables, select the variable with the highest size.
    In case of a tie, the first one is selected, first being defined by the
    order in the vector of IntVars used to create the selector.
    """
    CHOOSE_MAX_REGRET_ON_MIN = _pywrapcp.Solver_CHOOSE_MAX_REGRET_ON_MIN
    r"""
    Among unbound variables, select the variable with the largest
    gap between the first and the second values of the domain.
    """
    CHOOSE_PATH = _pywrapcp.Solver_CHOOSE_PATH
    r"""
    Selects the next unbound variable on a path, the path being defined by
    the variables: var[i] corresponds to the index of the next of i.
    """
    INT_VALUE_DEFAULT = _pywrapcp.Solver_INT_VALUE_DEFAULT
    r"""The default behavior is ASSIGN_MIN_VALUE."""
    INT_VALUE_SIMPLE = _pywrapcp.Solver_INT_VALUE_SIMPLE
    r"""The simple selection is ASSIGN_MIN_VALUE."""
    ASSIGN_MIN_VALUE = _pywrapcp.Solver_ASSIGN_MIN_VALUE
    r"""Selects the min value of the selected variable."""
    ASSIGN_MAX_VALUE = _pywrapcp.Solver_ASSIGN_MAX_VALUE
    r"""Selects the max value of the selected variable."""
    ASSIGN_RANDOM_VALUE = _pywrapcp.Solver_ASSIGN_RANDOM_VALUE
    r"""Selects randomly one of the possible values of the selected variable."""
    ASSIGN_CENTER_VALUE = _pywrapcp.Solver_ASSIGN_CENTER_VALUE
    r"""
    Selects the first possible value which is the closest to the center
    of the domain of the selected variable.
    The center is defined as (min + max) / 2.
    """
    SPLIT_LOWER_HALF = _pywrapcp.Solver_SPLIT_LOWER_HALF
    r"""
    Split the domain in two around the center, and choose the lower
    part first.
    """
    SPLIT_UPPER_HALF = _pywrapcp.Solver_SPLIT_UPPER_HALF
    r"""
    Split the domain in two around the center, and choose the lower
    part first.
    """
    SEQUENCE_DEFAULT = _pywrapcp.Solver_SEQUENCE_DEFAULT
    SEQUENCE_SIMPLE = _pywrapcp.Solver_SEQUENCE_SIMPLE
    CHOOSE_MIN_SLACK_RANK_FORWARD = _pywrapcp.Solver_CHOOSE_MIN_SLACK_RANK_FORWARD
    CHOOSE_RANDOM_RANK_FORWARD = _pywrapcp.Solver_CHOOSE_RANDOM_RANK_FORWARD
    INTERVAL_DEFAULT = _pywrapcp.Solver_INTERVAL_DEFAULT
    r"""The default is INTERVAL_SET_TIMES_FORWARD."""
    INTERVAL_SIMPLE = _pywrapcp.Solver_INTERVAL_SIMPLE
    r"""The simple is INTERVAL_SET_TIMES_FORWARD."""
    INTERVAL_SET_TIMES_FORWARD = _pywrapcp.Solver_INTERVAL_SET_TIMES_FORWARD
    r"""
    Selects the variable with the lowest starting time of all variables,
    and fixes its starting time to this lowest value.
    """
    INTERVAL_SET_TIMES_BACKWARD = _pywrapcp.Solver_INTERVAL_SET_TIMES_BACKWARD
    r"""
    Selects the variable with the highest ending time of all variables,
    and fixes the ending time to this highest values.
    """
    TWOOPT = _pywrapcp.Solver_TWOOPT
    r"""
    Operator which reverses a sub-chain of a path. It is called TwoOpt
    because it breaks two arcs on the path; resulting paths are called
    two-optimal.
    Possible neighbors for the path 1 -> 2 -> 3 -> 4 -> 5
    (where (1, 5) are first and last nodes of the path and can therefore not
    be moved):
      1 -> [3 -> 2] -> 4  -> 5
      1 -> [4 -> 3  -> 2] -> 5
      1 ->  2 -> [4 -> 3] -> 5
    """
    OROPT = _pywrapcp.Solver_OROPT
    r"""
    Relocate: OROPT and RELOCATE.
    Operator which moves a sub-chain of a path to another position; the
    specified chain length is the fixed length of the chains being moved.
    When this length is 1, the operator simply moves a node to another
    position.
    Possible neighbors for the path 1 -> 2 -> 3 -> 4 -> 5, for a chain
    length of 2 (where (1, 5) are first and last nodes of the path and can
    therefore not be moved):
      1 ->  4 -> [2 -> 3] -> 5
      1 -> [3 -> 4] -> 2  -> 5

    Using Relocate with chain lengths of 1, 2 and 3 together is equivalent
    to the OrOpt operator on a path. The OrOpt operator is a limited
     version of 3Opt (breaks 3 arcs on a path).
    """
    RELOCATE = _pywrapcp.Solver_RELOCATE
    r"""Relocate neighborhood with length of 1 (see OROPT comment)."""
    EXCHANGE = _pywrapcp.Solver_EXCHANGE
    r"""
    Operator which exchanges the positions of two nodes.
    Possible neighbors for the path 1 -> 2 -> 3 -> 4 -> 5
    (where (1, 5) are first and last nodes of the path and can therefore not
    be moved):
      1 -> [3] -> [2] ->  4  -> 5
      1 -> [4] ->  3  -> [2] -> 5
      1 ->  2  -> [4] -> [3] -> 5
    """
    CROSS = _pywrapcp.Solver_CROSS
    r"""
    Operator which cross exchanges the starting chains of 2 paths, including
    exchanging the whole paths.
    First and last nodes are not moved.
    Possible neighbors for the paths 1 -> 2 -> 3 -> 4 -> 5 and 6 -> 7 -> 8
    (where (1, 5) and (6, 8) are first and last nodes of the paths and can
    therefore not be moved):
      1 -> [7] -> 3 -> 4 -> 5  6 -> [2] -> 8
      1 -> [7] -> 4 -> 5       6 -> [2 -> 3] -> 8
      1 -> [7] -> 5            6 -> [2 -> 3 -> 4] -> 8
    """
    MAKEACTIVE = _pywrapcp.Solver_MAKEACTIVE
    r"""
    Operator which inserts an inactive node into a path.
    Possible neighbors for the path 1 -> 2 -> 3 -> 4 with 5 inactive
    (where 1 and 4 are first and last nodes of the path) are:
      1 -> [5] ->  2  ->  3  -> 4
      1 ->  2  -> [5] ->  3  -> 4
      1 ->  2  ->  3  -> [5] -> 4
    """
    MAKEINACTIVE = _pywrapcp.Solver_MAKEINACTIVE
    r"""
    Operator which makes path nodes inactive.
    Possible neighbors for the path 1 -> 2 -> 3 -> 4 (where 1 and 4 are
    first and last nodes of the path) are:
      1 -> 3 -> 4 with 2 inactive
      1 -> 2 -> 4 with 3 inactive
    """
    MAKECHAININACTIVE = _pywrapcp.Solver_MAKECHAININACTIVE
    r"""
    Operator which makes a "chain" of path nodes inactive.
    Possible neighbors for the path 1 -> 2 -> 3 -> 4 (where 1 and 4 are
    first and last nodes of the path) are:
      1 -> 3 -> 4 with 2 inactive
      1 -> 2 -> 4 with 3 inactive
      1 -> 4 with 2 and 3 inactive
    """
    SWAPACTIVE = _pywrapcp.Solver_SWAPACTIVE
    r"""
    Operator which replaces an active node by an inactive one.
    Possible neighbors for the path 1 -> 2 -> 3 -> 4 with 5 inactive
    (where 1 and 4 are first and last nodes of the path) are:
      1 -> [5] ->  3  -> 4 with 2 inactive
      1 ->  2  -> [5] -> 4 with 3 inactive
    """
    EXTENDEDSWAPACTIVE = _pywrapcp.Solver_EXTENDEDSWAPACTIVE
    r"""
    Operator which makes an inactive node active and an active one inactive.
    It is similar to SwapActiveOperator except that it tries to insert the
    inactive node in all possible positions instead of just the position of
    the node made inactive.
    Possible neighbors for the path 1 -> 2 -> 3 -> 4 with 5 inactive
    (where 1 and 4 are first and last nodes of the path) are:
      1 -> [5] ->  3  -> 4 with 2 inactive
      1 ->  3  -> [5] -> 4 with 2 inactive
      1 -> [5] ->  2  -> 4 with 3 inactive
      1 ->  2  -> [5] -> 4 with 3 inactive
    """
    PATHLNS = _pywrapcp.Solver_PATHLNS
    r"""
    Operator which relaxes two sub-chains of three consecutive arcs each.
    Each sub-chain is defined by a start node and the next three arcs. Those
    six arcs are relaxed to build a new neighbor.
    PATHLNS explores all possible pairs of starting nodes and so defines
    n^2 neighbors, n being the number of nodes.
    Note that the two sub-chains can be part of the same path; they even may
    overlap.
    """
    FULLPATHLNS = _pywrapcp.Solver_FULLPATHLNS
    r"""
    Operator which relaxes one entire path and all inactive nodes, thus
    defining num_paths neighbors.
    """
    UNACTIVELNS = _pywrapcp.Solver_UNACTIVELNS
    r"""
    Operator which relaxes all inactive nodes and one sub-chain of six
    consecutive arcs. That way the path can be improved by inserting
    inactive nodes or swapping arcs.
    """
    INCREMENT = _pywrapcp.Solver_INCREMENT
    r"""
    Operator which defines one neighbor per variable. Each neighbor tries to
    increment by one the value of the corresponding variable. When a new
    solution is found the neighborhood is rebuilt from scratch, i.e., tries
    to increment values in the variable order.
    Consider for instance variables x and y. x is incremented one by one to
    its max, and when it is not possible to increment x anymore, y is
    incremented once. If this is a solution, then next neighbor tries to
    increment x.
    """
    DECREMENT = _pywrapcp.Solver_DECREMENT
    r"""
    Operator which defines a neighborhood to decrement values.
    The behavior is the same as INCREMENT, except values are decremented
    instead of incremented.
    """
    SIMPLELNS = _pywrapcp.Solver_SIMPLELNS
    r"""
    Operator which defines one neighbor per variable. Each neighbor relaxes
    one variable.
    When a new solution is found the neighborhood is rebuilt from scratch.
    Consider for instance variables x and y. First x is relaxed and the
    solver is looking for the best possible solution (with only x relaxed).
    Then y is relaxed, and the solver is looking for a new solution.
    If a new solution is found, then the next variable to be relaxed is x.
    """
    GE = _pywrapcp.Solver_GE
    r"""Move is accepted when the current objective value >= objective.Min."""
    LE = _pywrapcp.Solver_LE
    r"""Move is accepted when the current objective value <= objective.Max."""
    EQ = _pywrapcp.Solver_EQ
    r"""
    Move is accepted when the current objective value is in the interval
    objective.Min .. objective.Max.
    """
    DELAYED_PRIORITY = _pywrapcp.Solver_DELAYED_PRIORITY
    r"""
    DELAYED_PRIORITY is the lowest priority: Demons will be processed after
    VAR_PRIORITY and NORMAL_PRIORITY demons.
    """
    VAR_PRIORITY = _pywrapcp.Solver_VAR_PRIORITY
    r"""VAR_PRIORITY is between DELAYED_PRIORITY and NORMAL_PRIORITY."""
    NORMAL_PRIORITY = _pywrapcp.Solver_NORMAL_PRIORITY
    r"""NORMAL_PRIORITY is the highest priority: Demons will be processed first."""

    def __init__(self, *args):
        r"""Solver API"""
        _pywrapcp.Solver_swiginit(self, _pywrapcp.new_Solver(*args))

        self.__python_constraints = []



    __swig_destroy__ = _pywrapcp.delete_Solver

    def Parameters(self):
        r"""Stored Parameters."""
        return _pywrapcp.Solver_Parameters(self)

    @staticmethod
    def DefaultSolverParameters():
        r"""Create a ConstraintSolverParameters proto with all the default values."""
        return _pywrapcp.Solver_DefaultSolverParameters()

    def AddConstraint(self, c):
        r"""
        Adds the constraint 'c' to the model.

        After calling this method, and until there is a backtrack that undoes the
        addition, any assignment of variables to values must satisfy the given
        constraint in order to be considered feasible. There are two fairly
        different use cases:

        - the most common use case is modeling: the given constraint is really
        part of the problem that the user is trying to solve. In this use case,
        AddConstraint is called outside of search (i.e., with state() ==
        OUTSIDE_SEARCH). Most users should only use AddConstraint in this
        way. In this case, the constraint will belong to the model forever: it
        cannot be removed by backtracking.

        - a rarer use case is that 'c' is not a real constraint of the model. It
        may be a constraint generated by a branching decision (a constraint whose
        goal is to restrict the search space), a symmetry breaking constraint (a
        constraint that does restrict the search space, but in a way that cannot
        have an impact on the quality of the solutions in the subtree), or an
        inferred constraint that, while having no semantic value to the model (it
        does not restrict the set of solutions), is worth having because we
        believe it may strengthen the propagation. In these cases, it happens
        that the constraint is added during the search (i.e., with state() ==
        IN_SEARCH or state() == IN_ROOT_NODE). When a constraint is
        added during a search, it applies only to the subtree of the search tree
        rooted at the current node, and will be automatically removed by
        backtracking.

        This method does not take ownership of the constraint. If the constraint
        has been created by any factory method (Solver::MakeXXX), it will
        automatically be deleted. However, power users who implement their own
        constraints should do: solver.AddConstraint(solver.RevAlloc(new
        MyConstraint(...));
        """
        return _pywrapcp.Solver_AddConstraint(self, c)

    def Solve(self, *args):
        r"""
         Solves the problem using the given DecisionBuilder and returns true if a
         solution was found and accepted.

         These methods are the ones most users should use to search for a solution.
         Note that the definition of 'solution' is subtle. A solution here is
         defined as a leaf of the search tree with respect to the given decision
         builder for which there is no failure. What this means is that, contrary
         to intuition, a solution may not have all variables of the model bound.
         It is the responsibility of the decision builder to keep returning
         decisions until all variables are indeed bound. The most extreme
         counterexample is calling Solve with a trivial decision builder whose
         Next() method always returns nullptr. In this case, Solve immediately
         returns 'true', since not assigning any variable to any value is a
         solution, unless the root node propagation discovers that the model is
         infeasible.

         This function must be called either from outside of search,
         or from within the Next() method of a decision builder.

         Solve will terminate whenever any of the following event arise:
        A search monitor asks the solver to terminate the search by calling
           solver()->FinishCurrentSearch().
        A solution is found that is accepted by all search monitors, and none of
           the search monitors decides to search for another one.

         Upon search termination, there will be a series of backtracks all the way
         to the top level. This means that a user cannot expect to inspect the
         solution by querying variables after a call to Solve(): all the
         information will be lost. In order to do something with the solution, the
         user must either:

        Use a search monitor that can process such a leaf. See, in particular,
             the SolutionCollector class.
        Do not use Solve. Instead, use the more fine-grained approach using
             methods NewSearch(...), NextSolution(), and EndSearch().

         :type db: :py:class:`DecisionBuilder`
         :param db: The decision builder that will generate the search tree.
         :type monitors: std::vector< operations_research::SearchMonitor * >
         :param monitors: A vector of search monitors that will be notified of
             various events during the search. In their reaction to these events, such
             monitors may influence the search.
        """
        return _pywrapcp.Solver_Solve(self, *args)

    def NewSearch(self, *args):
        r"""
         Decomposed search.
         The code for a top level search should look like
         solver->NewSearch(db);
         while (solver->NextSolution()) {
        .. use the current solution
         }
         solver()->EndSearch();
        """
        return _pywrapcp.Solver_NewSearch(self, *args)

    def NextSolution(self):
        return _pywrapcp.Solver_NextSolution(self)

    def RestartSearch(self):
        return _pywrapcp.Solver_RestartSearch(self)

    def EndSearch(self):
        return _pywrapcp.Solver_EndSearch(self)

    def SolveAndCommit(self, *args):
        r"""
        SolveAndCommit using a decision builder and up to three
          search monitors, usually one for the objective, one for the limits
          and one to collect solutions.

        The difference between a SolveAndCommit() and a Solve() method
        call is the fact that SolveAndCommit will not backtrack all
        modifications at the end of the search. This method is only
        usable during the Next() method of a decision builder.
        """
        return _pywrapcp.Solver_SolveAndCommit(self, *args)

    def CheckAssignment(self, solution):
        r"""Checks whether the given assignment satisfies all relevant constraints."""
        return _pywrapcp.Solver_CheckAssignment(self, solution)

    def CheckConstraint(self, ct):
        r"""
        Checks whether adding this constraint will lead to an immediate
        failure. It will return false if the model is already inconsistent, or if
        adding the constraint makes it inconsistent.
        """
        return _pywrapcp.Solver_CheckConstraint(self, ct)

    def Fail(self):
        r"""Abandon the current branch in the search tree. A backtrack will follow."""
        return _pywrapcp.Solver_Fail(self)

    @staticmethod
    def MemoryUsage():
        r"""Current memory usage in bytes"""
        return _pywrapcp.Solver_MemoryUsage()

    def WallTime(self):
        r"""
        DEPRECATED: Use Now() instead.
        Time elapsed, in ms since the creation of the solver.
        """
        return _pywrapcp.Solver_WallTime(self)

    def Branches(self):
        r"""The number of branches explored since the creation of the solver."""
        return _pywrapcp.Solver_Branches(self)

    def Solutions(self):
        r"""The number of solutions found since the start of the search."""
        return _pywrapcp.Solver_Solutions(self)

    def Failures(self):
        r"""The number of failures encountered since the creation of the solver."""
        return _pywrapcp.Solver_Failures(self)

    def AcceptedNeighbors(self):
        r"""The number of accepted neighbors."""
        return _pywrapcp.Solver_AcceptedNeighbors(self)

    def Stamp(self):
        r"""
        The stamp indicates how many moves in the search tree we have performed.
        It is useful to detect if we need to update same lazy structures.
        """
        return _pywrapcp.Solver_Stamp(self)

    def FailStamp(self):
        r"""The fail_stamp() is incremented after each backtrack."""
        return _pywrapcp.Solver_FailStamp(self)

    def IntVar(self, *args):
        r"""
        *Overload 1:*
        MakeIntVar will create the best range based int var for the bounds given.

        |

        *Overload 2:*
        MakeIntVar will create a variable with the given sparse domain.

        |

        *Overload 3:*
        MakeIntVar will create a variable with the given sparse domain.

        |

        *Overload 4:*
        MakeIntVar will create the best range based int var for the bounds given.

        |

        *Overload 5:*
        MakeIntVar will create a variable with the given sparse domain.

        |

        *Overload 6:*
        MakeIntVar will create a variable with the given sparse domain.
        """
        return _pywrapcp.Solver_IntVar(self, *args)

    def BoolVar(self, *args):
        r"""
        *Overload 1:*
        MakeBoolVar will create a variable with a {0, 1} domain.

        |

        *Overload 2:*
        MakeBoolVar will create a variable with a {0, 1} domain.
        """
        return _pywrapcp.Solver_BoolVar(self, *args)

    def IntConst(self, *args):
        r"""
        *Overload 1:*
        IntConst will create a constant expression.

        |

        *Overload 2:*
        IntConst will create a constant expression.
        """
        return _pywrapcp.Solver_IntConst(self, *args)

    def Sum(self, vars):
        r"""sum of all vars."""
        return _pywrapcp.Solver_Sum(self, vars)

    def ScalProd(self, *args):
        r"""
        *Overload 1:*
        scalar product

        |

        *Overload 2:*
        scalar product
        """
        return _pywrapcp.Solver_ScalProd(self, *args)

    def MonotonicElement(self, values, increasing, index):
        r"""
        Function based element. The constraint takes ownership of the
        callback.  The callback must be monotonic. It must be able to
        cope with any possible value in the domain of 'index'
        (potentially negative ones too). Furtermore, monotonicity is not
        checked. Thus giving a non-monotonic function, or specifying an
        incorrect increasing parameter will result in undefined behavior.
        """
        return _pywrapcp.Solver_MonotonicElement(self, values, increasing, index)

    def Element(self, *args):
        r"""
        *Overload 1:*
        values[index]

        |

        *Overload 2:*
        values[index]

        |

        *Overload 3:*
        Function-based element. The constraint takes ownership of the
        callback. The callback must be able to cope with any possible
        value in the domain of 'index' (potentially negative ones too).

        |

        *Overload 4:*
        2D version of function-based element expression, values(expr1, expr2).

        |

        *Overload 5:*
        vars[expr]
        """
        return _pywrapcp.Solver_Element(self, *args)

    def IndexExpression(self, vars, value):
        r"""
        Returns the expression expr such that vars[expr] == value.
        It assumes that vars are all different.
        """
        return _pywrapcp.Solver_IndexExpression(self, vars, value)

    def Min(self, *args):
        r"""
        *Overload 1:*
        std::min(vars)

        |

        *Overload 2:*
        std::min (left, right)

        |

        *Overload 3:*
        std::min(expr, value)

        |

        *Overload 4:*
        std::min(expr, value)
        """
        return _pywrapcp.Solver_Min(self, *args)

    def Max(self, *args):
        r"""
        *Overload 1:*
        std::max(vars)

        |

        *Overload 2:*
        std::max(left, right)

        |

        *Overload 3:*
        std::max(expr, value)

        |

        *Overload 4:*
        std::max(expr, value)
        """
        return _pywrapcp.Solver_Max(self, *args)

    def ConvexPiecewiseExpr(self, expr, early_cost, early_date, late_date, late_cost):
        r"""Convex piecewise function."""
        return _pywrapcp.Solver_ConvexPiecewiseExpr(self, expr, early_cost, early_date, late_date, late_cost)

    def SemiContinuousExpr(self, expr, fixed_charge, step):
        r"""
        Semi continuous Expression (x <= 0 -> f(x) = 0; x > 0 -> f(x) = ax + b)
        a >= 0 and b >= 0
        """
        return _pywrapcp.Solver_SemiContinuousExpr(self, expr, fixed_charge, step)

    def ConditionalExpression(self, condition, expr, unperformed_value):
        r"""Conditional Expr condition ? expr : unperformed_value"""
        return _pywrapcp.Solver_ConditionalExpression(self, condition, expr, unperformed_value)

    def TrueConstraint(self):
        r"""This constraint always succeeds."""
        return _pywrapcp.Solver_TrueConstraint(self)

    def FalseConstraint(self, *args):
        r"""This constraint always fails."""
        return _pywrapcp.Solver_FalseConstraint(self, *args)

    def IsEqualCstCt(self, var, value, boolvar):
        r"""boolvar == (var == value)"""
        return _pywrapcp.Solver_IsEqualCstCt(self, var, value, boolvar)

    def IsEqualCstVar(self, var, value):
        r"""status var of (var == value)"""
        return _pywrapcp.Solver_IsEqualCstVar(self, var, value)

    def IsEqualCt(self, v1, v2, b):
        r"""b == (v1 == v2)"""
        return _pywrapcp.Solver_IsEqualCt(self, v1, v2, b)

    def IsEqualVar(self, v1, v2):
        r"""status var of (v1 == v2)"""
        return _pywrapcp.Solver_IsEqualVar(self, v1, v2)

    def IsDifferentCstCt(self, var, value, boolvar):
        r"""boolvar == (var != value)"""
        return _pywrapcp.Solver_IsDifferentCstCt(self, var, value, boolvar)

    def IsDifferentCstVar(self, var, value):
        r"""status var of (var != value)"""
        return _pywrapcp.Solver_IsDifferentCstVar(self, var, value)

    def IsDifferentVar(self, v1, v2):
        r"""status var of (v1 != v2)"""
        return _pywrapcp.Solver_IsDifferentVar(self, v1, v2)

    def IsDifferentCt(self, v1, v2, b):
        r"""b == (v1 != v2)"""
        return _pywrapcp.Solver_IsDifferentCt(self, v1, v2, b)

    def IsLessOrEqualCstCt(self, var, value, boolvar):
        r"""boolvar == (var <= value)"""
        return _pywrapcp.Solver_IsLessOrEqualCstCt(self, var, value, boolvar)

    def IsLessOrEqualCstVar(self, var, value):
        r"""status var of (var <= value)"""
        return _pywrapcp.Solver_IsLessOrEqualCstVar(self, var, value)

    def IsLessOrEqualVar(self, left, right):
        r"""status var of (left <= right)"""
        return _pywrapcp.Solver_IsLessOrEqualVar(self, left, right)

    def IsLessOrEqualCt(self, left, right, b):
        r"""b == (left <= right)"""
        return _pywrapcp.Solver_IsLessOrEqualCt(self, left, right, b)

    def IsGreaterOrEqualCstCt(self, var, value, boolvar):
        r"""boolvar == (var >= value)"""
        return _pywrapcp.Solver_IsGreaterOrEqualCstCt(self, var, value, boolvar)

    def IsGreaterOrEqualCstVar(self, var, value):
        r"""status var of (var >= value)"""
        return _pywrapcp.Solver_IsGreaterOrEqualCstVar(self, var, value)

    def IsGreaterOrEqualVar(self, left, right):
        r"""status var of (left >= right)"""
        return _pywrapcp.Solver_IsGreaterOrEqualVar(self, left, right)

    def IsGreaterOrEqualCt(self, left, right, b):
        r"""b == (left >= right)"""
        return _pywrapcp.Solver_IsGreaterOrEqualCt(self, left, right, b)

    def IsGreaterCstCt(self, v, c, b):
        r"""b == (v > c)"""
        return _pywrapcp.Solver_IsGreaterCstCt(self, v, c, b)

    def IsGreaterCstVar(self, var, value):
        r"""status var of (var > value)"""
        return _pywrapcp.Solver_IsGreaterCstVar(self, var, value)

    def IsGreaterVar(self, left, right):
        r"""status var of (left > right)"""
        return _pywrapcp.Solver_IsGreaterVar(self, left, right)

    def IsGreaterCt(self, left, right, b):
        r"""b == (left > right)"""
        return _pywrapcp.Solver_IsGreaterCt(self, left, right, b)

    def IsLessCstCt(self, v, c, b):
        r"""b == (v < c)"""
        return _pywrapcp.Solver_IsLessCstCt(self, v, c, b)

    def IsLessCstVar(self, var, value):
        r"""status var of (var < value)"""
        return _pywrapcp.Solver_IsLessCstVar(self, var, value)

    def IsLessVar(self, left, right):
        r"""status var of (left < right)"""
        return _pywrapcp.Solver_IsLessVar(self, left, right)

    def IsLessCt(self, left, right, b):
        r"""b == (left < right)"""
        return _pywrapcp.Solver_IsLessCt(self, left, right, b)

    def SumLessOrEqual(self, vars, cst):
        r"""Variation on arrays."""
        return _pywrapcp.Solver_SumLessOrEqual(self, vars, cst)

    def SumGreaterOrEqual(self, vars, cst):
        return _pywrapcp.Solver_SumGreaterOrEqual(self, vars, cst)

    def SumEquality(self, *args):
        return _pywrapcp.Solver_SumEquality(self, *args)

    def ScalProdEquality(self, *args):
        return _pywrapcp.Solver_ScalProdEquality(self, *args)

    def ScalProdGreaterOrEqual(self, *args):
        return _pywrapcp.Solver_ScalProdGreaterOrEqual(self, *args)

    def ScalProdLessOrEqual(self, *args):
        return _pywrapcp.Solver_ScalProdLessOrEqual(self, *args)

    def MinEquality(self, vars, min_var):
        return _pywrapcp.Solver_MinEquality(self, vars, min_var)

    def MaxEquality(self, vars, max_var):
        return _pywrapcp.Solver_MaxEquality(self, vars, max_var)

    def ElementEquality(self, *args):
        return _pywrapcp.Solver_ElementEquality(self, *args)

    def AbsEquality(self, var, abs_var):
        r"""Creates the constraint abs(var) == abs_var."""
        return _pywrapcp.Solver_AbsEquality(self, var, abs_var)

    def IndexOfConstraint(self, vars, index, target):
        r"""
        This constraint is a special case of the element constraint with
        an array of integer variables, where the variables are all
        different and the index variable is constrained such that
        vars[index] == target.
        """
        return _pywrapcp.Solver_IndexOfConstraint(self, vars, index, target)

    def ConstraintInitialPropagateCallback(self, ct):
        r"""
        This method is a specialized case of the MakeConstraintDemon
        method to call the InitiatePropagate of the constraint 'ct'.
        """
        return _pywrapcp.Solver_ConstraintInitialPropagateCallback(self, ct)

    def DelayedConstraintInitialPropagateCallback(self, ct):
        r"""
        This method is a specialized case of the MakeConstraintDemon
        method to call the InitiatePropagate of the constraint 'ct' with
        low priority.
        """
        return _pywrapcp.Solver_DelayedConstraintInitialPropagateCallback(self, ct)

    def ClosureDemon(self, closure):
        r"""Creates a demon from a closure."""
        return _pywrapcp.Solver_ClosureDemon(self, closure)

    def BetweenCt(self, expr, l, u):
        r"""(l <= expr <= u)"""
        return _pywrapcp.Solver_BetweenCt(self, expr, l, u)

    def IsBetweenCt(self, expr, l, u, b):
        r"""b == (l <= expr <= u)"""
        return _pywrapcp.Solver_IsBetweenCt(self, expr, l, u, b)

    def IsBetweenVar(self, v, l, u):
        return _pywrapcp.Solver_IsBetweenVar(self, v, l, u)

    def MemberCt(self, *args):
        r"""
        expr in set. Propagation is lazy, i.e. this constraint does not
        creates holes in the domain of the variable.
        """
        return _pywrapcp.Solver_MemberCt(self, *args)

    def NotMemberCt(self, *args):
        r"""
        *Overload 1:*
        expr not in set.

        |

        *Overload 2:*
        expr should not be in the list of forbidden intervals [start[i]..end[i]].

        |

        *Overload 3:*
        expr should not be in the list of forbidden intervals [start[i]..end[i]].
        """
        return _pywrapcp.Solver_NotMemberCt(self, *args)

    def IsMemberCt(self, *args):
        r"""boolvar == (expr in set)"""
        return _pywrapcp.Solver_IsMemberCt(self, *args)

    def IsMemberVar(self, *args):
        return _pywrapcp.Solver_IsMemberVar(self, *args)

    def Count(self, *args):
        r"""
        *Overload 1:*
        |{i | vars[i] == value}| == max_count

        |

        *Overload 2:*
        |{i | vars[i] == value}| == max_count
        """
        return _pywrapcp.Solver_Count(self, *args)

    def Distribute(self, *args):
        r"""
        *Overload 1:*
        Aggregated version of count:  |{i | v[i] == values[j]}| == cards[j]

        |

        *Overload 2:*
        Aggregated version of count:  |{i | v[i] == values[j]}| == cards[j]

        |

        *Overload 3:*
        Aggregated version of count:  |{i | v[i] == j}| == cards[j]

        |

        *Overload 4:*
        Aggregated version of count with bounded cardinalities:
        forall j in 0 .. card_size - 1: card_min <= |{i | v[i] == j}| <= card_max

        |

        *Overload 5:*
        Aggregated version of count with bounded cardinalities:
        forall j in 0 .. card_size - 1:
           card_min[j] <= |{i | v[i] == j}| <= card_max[j]

        |

        *Overload 6:*
        Aggregated version of count with bounded cardinalities:
        forall j in 0 .. card_size - 1:
           card_min[j] <= |{i | v[i] == j}| <= card_max[j]

        |

        *Overload 7:*
        Aggregated version of count with bounded cardinalities:
        forall j in 0 .. card_size - 1:
           card_min[j] <= |{i | v[i] == values[j]}| <= card_max[j]

        |

        *Overload 8:*
        Aggregated version of count with bounded cardinalities:
        forall j in 0 .. card_size - 1:
           card_min[j] <= |{i | v[i] == values[j]}| <= card_max[j]
        """
        return _pywrapcp.Solver_Distribute(self, *args)

    def Deviation(self, vars, deviation_var, total_sum):
        r"""
        Deviation constraint:
        sum_i |n * vars[i] - total_sum| <= deviation_var and
        sum_i vars[i] == total_sum
        n = #vars
        """
        return _pywrapcp.Solver_Deviation(self, vars, deviation_var, total_sum)

    def AllDifferent(self, *args):
        r"""
        *Overload 1:*
        All variables are pairwise different. This corresponds to the
        stronger version of the propagation algorithm.

        |

        *Overload 2:*
        All variables are pairwise different.  If 'stronger_propagation'
        is true, stronger, and potentially slower propagation will
        occur. This API will be deprecated in the future.
        """
        return _pywrapcp.Solver_AllDifferent(self, *args)

    def AllDifferentExcept(self, vars, escape_value):
        r"""
        All variables are pairwise different, unless they are assigned to
        the escape value.
        """
        return _pywrapcp.Solver_AllDifferentExcept(self, vars, escape_value)

    def SortingConstraint(self, vars, sorted):
        r"""
        Creates a constraint binding the arrays of variables "vars" and
        "sorted_vars": sorted_vars[0] must be equal to the minimum of all
        variables in vars, and so on: the value of sorted_vars[i] must be
        equal to the i-th value of variables invars.

        This constraint propagates in both directions: from "vars" to
        "sorted_vars" and vice-versa.

        Behind the scenes, this constraint maintains that:
          - sorted is always increasing.
          - whatever the values of vars, there exists a permutation that
            injects its values into the sorted variables.

        For more info, please have a look at:
          https://mpi-inf.mpg.de/~mehlhorn/ftp/Mehlhorn-Thiel.pdf
        """
        return _pywrapcp.Solver_SortingConstraint(self, vars, sorted)

    def LexicalLess(self, left, right):
        r"""
        Creates a constraint that enforces that left is lexicographically less
        than right.
        """
        return _pywrapcp.Solver_LexicalLess(self, left, right)

    def LexicalLessOrEqual(self, left, right):
        r"""
        Creates a constraint that enforces that left is lexicographically less
        than or equal to right.
        """
        return _pywrapcp.Solver_LexicalLessOrEqual(self, left, right)

    def InversePermutationConstraint(self, left, right):
        r"""
        Creates a constraint that enforces that 'left' and 'right' both
        represent permutations of [0..left.size()-1], and that 'right' is
        the inverse permutation of 'left', i.e. for all i in
        [0..left.size()-1], right[left[i]] = i.
        """
        return _pywrapcp.Solver_InversePermutationConstraint(self, left, right)

    def NullIntersect(self, first_vars, second_vars):
        r"""
        Creates a constraint that states that all variables in the first
        vector are different from all variables in the second
        group. Thus the set of values in the first vector does not
        intersect with the set of values in the second vector.
        """
        return _pywrapcp.Solver_NullIntersect(self, first_vars, second_vars)

    def NullIntersectExcept(self, first_vars, second_vars, escape_value):
        r"""
        Creates a constraint that states that all variables in the first
        vector are different from all variables from the second group,
        unless they are assigned to the escape value. Thus the set of
        values in the first vector minus the escape value does not
        intersect with the set of values in the second vector.
        """
        return _pywrapcp.Solver_NullIntersectExcept(self, first_vars, second_vars, escape_value)

    def Circuit(self, nexts):
        r"""Force the "nexts" variable to create a complete Hamiltonian path."""
        return _pywrapcp.Solver_Circuit(self, nexts)

    def SubCircuit(self, nexts):
        r"""
        Force the "nexts" variable to create a complete Hamiltonian path
        for those that do not loop upon themselves.
        """
        return _pywrapcp.Solver_SubCircuit(self, nexts)

    def DelayedPathCumul(self, nexts, active, cumuls, transits):
        r"""
        Delayed version of the same constraint: propagation on the nexts variables
        is delayed until all constraints have propagated.
        """
        return _pywrapcp.Solver_DelayedPathCumul(self, nexts, active, cumuls, transits)

    def PathCumul(self, *args):
        r"""
        *Overload 1:*
        Creates a constraint which accumulates values along a path such that:
        cumuls[next[i]] = cumuls[i] + transits[i].
        Active variables indicate if the corresponding next variable is active;
        this could be useful to model unperformed nodes in a routing problem.

        |

        *Overload 2:*
        Creates a constraint which accumulates values along a path such that:
        cumuls[next[i]] = cumuls[i] + transit_evaluator(i, next[i]).
        Active variables indicate if the corresponding next variable is active;
        this could be useful to model unperformed nodes in a routing problem.
        Ownership of transit_evaluator is taken and it must be a repeatable
        callback.

        |

        *Overload 3:*
        Creates a constraint which accumulates values along a path such that:
        cumuls[next[i]] = cumuls[i] + transit_evaluator(i, next[i]) + slacks[i].
        Active variables indicate if the corresponding next variable is active;
        this could be useful to model unperformed nodes in a routing problem.
        Ownership of transit_evaluator is taken and it must be a repeatable
        callback.
        """
        return _pywrapcp.Solver_PathCumul(self, *args)

    def AllowedAssignments(self, *args):
        r"""
        *Overload 1:*
        This method creates a constraint where the graph of the relation
        between the variables is given in extension. There are 'arity'
        variables involved in the relation and the graph is given by a
        integer tuple set.

        |

        *Overload 2:*
        Compatibility layer for Python API.
        """
        return _pywrapcp.Solver_AllowedAssignments(self, *args)

    def TransitionConstraint(self, *args):
        r"""
        *Overload 1:*
        This constraint create a finite automaton that will check the
        sequence of variables vars. It uses a transition table called
        'transition_table'. Each transition is a triple
           (current_state, variable_value, new_state).
        The initial state is given, and the set of accepted states is decribed
        by 'final_states'. These states are hidden inside the constraint.
        Only the transitions (i.e. the variables) are visible.

        |

        *Overload 2:*
        This constraint create a finite automaton that will check the
        sequence of variables vars. It uses a transition table called
        'transition_table'. Each transition is a triple
           (current_state, variable_value, new_state).
        The initial state is given, and the set of accepted states is decribed
        by 'final_states'. These states are hidden inside the constraint.
        Only the transitions (i.e. the variables) are visible.
        """
        return _pywrapcp.Solver_TransitionConstraint(self, *args)

    def NonOverlappingBoxesConstraint(self, *args):
        r"""
        This constraint states that all the boxes must not overlap.
        The coordinates of box i are:
          (x_vars[i], y_vars[i]),
          (x_vars[i], y_vars[i] + y_size[i]),
          (x_vars[i] + x_size[i], y_vars[i]),
          (x_vars[i] + x_size[i], y_vars[i] + y_size[i]).
        The sizes must be non-negative. Boxes with a zero dimension can be
        pushed like any box.
        """
        return _pywrapcp.Solver_NonOverlappingBoxesConstraint(self, *args)

    def Pack(self, vars, number_of_bins):
        r"""
        This constraint packs all variables onto 'number_of_bins'
        variables.  For any given variable, a value of 'number_of_bins'
        indicates that the variable is not assigned to any bin.
        Dimensions, i.e., cumulative constraints on this packing, can be
        added directly from the pack class.
        """
        return _pywrapcp.Solver_Pack(self, vars, number_of_bins)

    def FixedDurationIntervalVar(self, *args):
        r"""
        *Overload 1:*
        Creates an interval var with a fixed duration. The duration must
        be greater than 0. If optional is true, then the interval can be
        performed or unperformed. If optional is false, then the interval
        is always performed.

        |

        *Overload 2:*
        Creates a performed interval var with a fixed duration. The duration must
        be greater than 0.

        |

        *Overload 3:*
        Creates an interval var with a fixed duration, and performed_variable.
        The duration must be greater than 0.
        """
        return _pywrapcp.Solver_FixedDurationIntervalVar(self, *args)

    def FixedInterval(self, start, duration, name):
        r"""Creates a fixed and performed interval."""
        return _pywrapcp.Solver_FixedInterval(self, start, duration, name)

    def IntervalVar(self, start_min, start_max, duration_min, duration_max, end_min, end_max, optional, name):
        r"""
        Creates an interval var by specifying the bounds on start,
        duration, and end.
        """
        return _pywrapcp.Solver_IntervalVar(self, start_min, start_max, duration_min, duration_max, end_min, end_max, optional, name)

    def MirrorInterval(self, interval_var):
        r"""
        Creates an interval var that is the mirror image of the given one, that
        is, the interval var obtained by reversing the axis.
        """
        return _pywrapcp.Solver_MirrorInterval(self, interval_var)

    def FixedDurationStartSyncedOnStartIntervalVar(self, interval_var, duration, offset):
        r"""
        Creates an interval var with a fixed duration whose start is
        synchronized with the start of another interval, with a given
        offset. The performed status is also in sync with the performed
        status of the given interval variable.
        """
        return _pywrapcp.Solver_FixedDurationStartSyncedOnStartIntervalVar(self, interval_var, duration, offset)

    def FixedDurationStartSyncedOnEndIntervalVar(self, interval_var, duration, offset):
        r"""
        Creates an interval var with a fixed duration whose start is
        synchronized with the end of another interval, with a given
        offset. The performed status is also in sync with the performed
        status of the given interval variable.
        """
        return _pywrapcp.Solver_FixedDurationStartSyncedOnEndIntervalVar(self, interval_var, duration, offset)

    def FixedDurationEndSyncedOnStartIntervalVar(self, interval_var, duration, offset):
        r"""
        Creates an interval var with a fixed duration whose end is
        synchronized with the start of another interval, with a given
        offset. The performed status is also in sync with the performed
        status of the given interval variable.
        """
        return _pywrapcp.Solver_FixedDurationEndSyncedOnStartIntervalVar(self, interval_var, duration, offset)

    def FixedDurationEndSyncedOnEndIntervalVar(self, interval_var, duration, offset):
        r"""
        Creates an interval var with a fixed duration whose end is
        synchronized with the end of another interval, with a given
        offset. The performed status is also in sync with the performed
        status of the given interval variable.
        """
        return _pywrapcp.Solver_FixedDurationEndSyncedOnEndIntervalVar(self, interval_var, duration, offset)

    def IntervalRelaxedMin(self, interval_var):
        r"""
         Creates and returns an interval variable that wraps around the given one,
         relaxing the min start and end. Relaxing means making unbounded when
         optional. If the variable is non-optional, this method returns
         interval_var.

         More precisely, such an interval variable behaves as follows:
        When the underlying must be performed, the returned interval variable
             behaves exactly as the underlying;
        When the underlying may or may not be performed, the returned interval
             variable behaves like the underlying, except that it is unbounded on
             the min side;
        When the underlying cannot be performed, the returned interval variable
             is of duration 0 and must be performed in an interval unbounded on
             both sides.

         This is very useful to implement propagators that may only modify
         the start max or end max.
        """
        return _pywrapcp.Solver_IntervalRelaxedMin(self, interval_var)

    def IntervalRelaxedMax(self, interval_var):
        r"""
         Creates and returns an interval variable that wraps around the given one,
         relaxing the max start and end. Relaxing means making unbounded when
         optional. If the variable is non optional, this method returns
         interval_var.

         More precisely, such an interval variable behaves as follows:
        When the underlying must be performed, the returned interval variable
             behaves exactly as the underlying;
        When the underlying may or may not be performed, the returned interval
             variable behaves like the underlying, except that it is unbounded on
             the max side;
        When the underlying cannot be performed, the returned interval variable
             is of duration 0 and must be performed in an interval unbounded on
             both sides.

         This is very useful for implementing propagators that may only modify
         the start min or end min.
        """
        return _pywrapcp.Solver_IntervalRelaxedMax(self, interval_var)

    def TemporalDisjunction(self, *args):
        r"""
        *Overload 1:*
        This constraint implements a temporal disjunction between two
        interval vars t1 and t2. 'alt' indicates which alternative was
        chosen (alt == 0 is equivalent to t1 before t2).

        |

        *Overload 2:*
        This constraint implements a temporal disjunction between two
        interval vars.
        """
        return _pywrapcp.Solver_TemporalDisjunction(self, *args)

    def DisjunctiveConstraint(self, intervals, name):
        r"""
        This constraint forces all interval vars into an non-overlapping
        sequence. Intervals with zero duration can be scheduled anywhere.
        """
        return _pywrapcp.Solver_DisjunctiveConstraint(self, intervals, name)

    def Cumulative(self, *args):
        r"""
        *Overload 1:*
        This constraint forces that, for any integer t, the sum of the demands
        corresponding to an interval containing t does not exceed the given
        capacity.

        Intervals and demands should be vectors of equal size.

        Demands should only contain non-negative values. Zero values are
        supported, and the corresponding intervals are filtered out, as they
        neither impact nor are impacted by this constraint.

        |

        *Overload 2:*
        This constraint forces that, for any integer t, the sum of the demands
        corresponding to an interval containing t does not exceed the given
        capacity.

        Intervals and demands should be vectors of equal size.

        Demands should only contain non-negative values. Zero values are
        supported, and the corresponding intervals are filtered out, as they
        neither impact nor are impacted by this constraint.

        |

        *Overload 3:*
        This constraint forces that, for any integer t, the sum of the demands
        corresponding to an interval containing t does not exceed the given
        capacity.

        Intervals and demands should be vectors of equal size.

        Demands should only contain non-negative values. Zero values are
        supported, and the corresponding intervals are filtered out, as they
        neither impact nor are impacted by this constraint.

        |

        *Overload 4:*
        This constraint enforces that, for any integer t, the sum of the demands
        corresponding to an interval containing t does not exceed the given
        capacity.

        Intervals and demands should be vectors of equal size.

        Demands should only contain non-negative values. Zero values are
        supported, and the corresponding intervals are filtered out, as they
        neither impact nor are impacted by this constraint.

        |

        *Overload 5:*
        This constraint enforces that, for any integer t, the sum of demands
        corresponding to an interval containing t does not exceed the given
        capacity.

        Intervals and demands should be vectors of equal size.

        Demands should be positive.

        |

        *Overload 6:*
        This constraint enforces that, for any integer t, the sum of demands
        corresponding to an interval containing t does not exceed the given
        capacity.

        Intervals and demands should be vectors of equal size.

        Demands should be positive.
        """
        return _pywrapcp.Solver_Cumulative(self, *args)

    def Cover(self, vars, target_var):
        r"""
        This constraint states that the target_var is the convex hull of
        the intervals. If none of the interval variables is performed,
        then the target var is unperformed too. Also, if the target
        variable is unperformed, then all the intervals variables are
        unperformed too.
        """
        return _pywrapcp.Solver_Cover(self, vars, target_var)

    def Assignment(self, *args):
        r"""
        *Overload 1:*
        This method creates an empty assignment.

        |

        *Overload 2:*
        This method creates an assignment which is a copy of 'a'.
        """
        return _pywrapcp.Solver_Assignment(self, *args)

    def FirstSolutionCollector(self, *args):
        r"""
        *Overload 1:*
        Collect the first solution of the search.

        |

        *Overload 2:*
        Collect the first solution of the search. The variables will need to
        be added later.
        """
        return _pywrapcp.Solver_FirstSolutionCollector(self, *args)

    def LastSolutionCollector(self, *args):
        r"""
        *Overload 1:*
        Collect the last solution of the search.

        |

        *Overload 2:*
        Collect the last solution of the search. The variables will need to
        be added later.
        """
        return _pywrapcp.Solver_LastSolutionCollector(self, *args)

    def BestValueSolutionCollector(self, *args):
        r"""
        *Overload 1:*
        Collect the solution corresponding to the optimal value of the objective
        of 'assignment'; if 'assignment' does not have an objective no solution is
        collected. This collector only collects one solution corresponding to the
        best objective value (the first one found).

        |

        *Overload 2:*
        Collect the solution corresponding to the optimal value of the
        objective of the internal assignment; if this assignment does not have an
        objective no solution is collected. This collector only collects one
        solution corresponding to the best objective value (the first one found).
        The variables and objective(s) will need to be added later.
        """
        return _pywrapcp.Solver_BestValueSolutionCollector(self, *args)

    def AllSolutionCollector(self, *args):
        r"""
        *Overload 1:*
        Collect all solutions of the search.

        |

        *Overload 2:*
        Collect all solutions of the search. The variables will need to
        be added later.
        """
        return _pywrapcp.Solver_AllSolutionCollector(self, *args)

    def Minimize(self, v, step):
        r"""Creates a minimization objective."""
        return _pywrapcp.Solver_Minimize(self, v, step)

    def Maximize(self, v, step):
        r"""Creates a maximization objective."""
        return _pywrapcp.Solver_Maximize(self, v, step)

    def Optimize(self, maximize, v, step):
        r"""Creates a objective with a given sense (true = maximization)."""
        return _pywrapcp.Solver_Optimize(self, maximize, v, step)

    def WeightedMinimize(self, *args):
        r"""
        *Overload 1:*
        Creates a minimization weighted objective. The actual objective is
        scalar_prod(sub_objectives, weights).

        |

        *Overload 2:*
        Creates a minimization weighted objective. The actual objective is
        scalar_prod(sub_objectives, weights).
        """
        return _pywrapcp.Solver_WeightedMinimize(self, *args)

    def WeightedMaximize(self, *args):
        r"""
        *Overload 1:*
        Creates a maximization weigthed objective.

        |

        *Overload 2:*
        Creates a maximization weigthed objective.
        """
        return _pywrapcp.Solver_WeightedMaximize(self, *args)

    def WeightedOptimize(self, *args):
        r"""
        *Overload 1:*
        Creates a weighted objective with a given sense (true = maximization).

        |

        *Overload 2:*
        Creates a weighted objective with a given sense (true = maximization).
        """
        return _pywrapcp.Solver_WeightedOptimize(self, *args)

    def TabuSearch(self, maximize, objective, step, vars, keep_tenure, forbid_tenure, tabu_factor):
        r"""
        MetaHeuristics which try to get the search out of local optima.
        Creates a Tabu Search monitor.
        In the context of local search the behavior is similar to MakeOptimize(),
        creating an objective in a given sense. The behavior differs once a local
        optimum is reached: thereafter solutions which degrade the value of the
        objective are allowed if they are not "tabu". A solution is "tabu" if it
        doesn't respect the following rules:
        - improving the best solution found so far
        - variables in the "keep" list must keep their value, variables in the
        "forbid" list must not take the value they have in the list.
        Variables with new values enter the tabu lists after each new solution
        found and leave the lists after a given number of iterations (called
        tenure). Only the variables passed to the method can enter the lists.
        The tabu criterion is softened by the tabu factor which gives the number
        of "tabu" violations which is tolerated; a factor of 1 means no violations
        allowed; a factor of 0 means all violations are allowed.
        """
        return _pywrapcp.Solver_TabuSearch(self, maximize, objective, step, vars, keep_tenure, forbid_tenure, tabu_factor)

    def SimulatedAnnealing(self, maximize, v, step, initial_temperature):
        r"""Creates a Simulated Annealing monitor."""
        return _pywrapcp.Solver_SimulatedAnnealing(self, maximize, v, step, initial_temperature)

    def LubyRestart(self, scale_factor):
        r"""
        This search monitor will restart the search periodically.
        At the iteration n, it will restart after scale_factor * Luby(n) failures
        where Luby is the Luby Strategy (i.e. 1 1 2 1 1 2 4 1 1 2 1 1 2 4 8...).
        """
        return _pywrapcp.Solver_LubyRestart(self, scale_factor)

    def ConstantRestart(self, frequency):
        r"""
        This search monitor will restart the search periodically after 'frequency'
        failures.
        """
        return _pywrapcp.Solver_ConstantRestart(self, frequency)

    def TimeLimit(self, *args):
        r"""Creates a search limit that constrains the running time."""
        return _pywrapcp.Solver_TimeLimit(self, *args)

    def BranchesLimit(self, branches):
        r"""
        Creates a search limit that constrains the number of branches
        explored in the search tree.
        """
        return _pywrapcp.Solver_BranchesLimit(self, branches)

    def FailuresLimit(self, failures):
        r"""
        Creates a search limit that constrains the number of failures
        that can happen when exploring the search tree.
        """
        return _pywrapcp.Solver_FailuresLimit(self, failures)

    def SolutionsLimit(self, solutions):
        r"""
        Creates a search limit that constrains the number of solutions found
        during the search.
        """
        return _pywrapcp.Solver_SolutionsLimit(self, solutions)

    def Limit(self, *args):
        r"""
        *Overload 1:*
        Limits the search with the 'time', 'branches', 'failures' and
        'solutions' limits. 'smart_time_check' reduces the calls to the wall

        |

        *Overload 2:*
        Creates a search limit from its protobuf description

        |

        *Overload 3:*
        Creates a search limit that is reached when either of the underlying limit
        is reached. That is, the returned limit is more stringent than both
        argument limits.
        """
        return _pywrapcp.Solver_Limit(self, *args)

    def CustomLimit(self, limiter):
        r"""
        Callback-based search limit. Search stops when limiter returns true; if
        this happens at a leaf the corresponding solution will be rejected.
        """
        return _pywrapcp.Solver_CustomLimit(self, limiter)

    def SearchLog(self, *args):
        r"""
        *Overload 1:*
         The SearchMonitors below will display a periodic search log
         on LOG(INFO) every branch_period branches explored.

        |

        *Overload 2:*
         At each solution, this monitor also display the var value.

        |

        *Overload 3:*
         At each solution, this monitor will also display result of
        ``display_callback``.

        |

        *Overload 4:*
         At each solution, this monitor will display the 'var' value and the
         result of ``display_callback``.

        |

        *Overload 5:*
         At each solution, this monitor will display the 'vars' values and the
         result of ``display_callback``.

        |

        *Overload 6:*
         OptimizeVar Search Logs
         At each solution, this monitor will also display the 'opt_var' value.

        |

        *Overload 7:*
         Creates a search monitor that will also print the result of the
         display callback.
        """
        return _pywrapcp.Solver_SearchLog(self, *args)

    def SearchTrace(self, prefix):
        r"""
        Creates a search monitor that will trace precisely the behavior of the
        search. Use this only for low level debugging.
        """
        return _pywrapcp.Solver_SearchTrace(self, prefix)

    def PrintModelVisitor(self):
        r"""Prints the model."""
        return _pywrapcp.Solver_PrintModelVisitor(self)

    def StatisticsModelVisitor(self):
        r"""Displays some nice statistics on the model."""
        return _pywrapcp.Solver_StatisticsModelVisitor(self)

    def AssignVariableValue(self, var, val):
        r"""Decisions."""
        return _pywrapcp.Solver_AssignVariableValue(self, var, val)

    def VariableLessOrEqualValue(self, var, value):
        return _pywrapcp.Solver_VariableLessOrEqualValue(self, var, value)

    def VariableGreaterOrEqualValue(self, var, value):
        return _pywrapcp.Solver_VariableGreaterOrEqualValue(self, var, value)

    def SplitVariableDomain(self, var, val, start_with_lower_half):
        return _pywrapcp.Solver_SplitVariableDomain(self, var, val, start_with_lower_half)

    def AssignVariableValueOrFail(self, var, value):
        return _pywrapcp.Solver_AssignVariableValueOrFail(self, var, value)

    def AssignVariablesValues(self, vars, values):
        return _pywrapcp.Solver_AssignVariablesValues(self, vars, values)

    def FailDecision(self):
        return _pywrapcp.Solver_FailDecision(self)

    def Decision(self, apply, refute):
        return _pywrapcp.Solver_Decision(self, apply, refute)

    def Compose(self, dbs):
        return _pywrapcp.Solver_Compose(self, dbs)

    def Try(self, dbs):
        return _pywrapcp.Solver_Try(self, dbs)

    def DefaultPhase(self, *args):
        return _pywrapcp.Solver_DefaultPhase(self, *args)

    def ScheduleOrPostpone(self, var, est, marker):
        r"""
        Returns a decision that tries to schedule a task at a given time.
        On the Apply branch, it will set that interval var as performed and set
        its start to 'est'. On the Refute branch, it will just update the
        'marker' to 'est' + 1. This decision is used in the
        INTERVAL_SET_TIMES_FORWARD strategy.
        """
        return _pywrapcp.Solver_ScheduleOrPostpone(self, var, est, marker)

    def ScheduleOrExpedite(self, var, est, marker):
        r"""
        Returns a decision that tries to schedule a task at a given time.
        On the Apply branch, it will set that interval var as performed and set
        its end to 'est'. On the Refute branch, it will just update the
        'marker' to 'est' - 1. This decision is used in the
        INTERVAL_SET_TIMES_BACKWARD strategy.
        """
        return _pywrapcp.Solver_ScheduleOrExpedite(self, var, est, marker)

    def RankFirstInterval(self, sequence, index):
        r"""
        Returns a decision that tries to rank first the ith interval var
        in the sequence variable.
        """
        return _pywrapcp.Solver_RankFirstInterval(self, sequence, index)

    def RankLastInterval(self, sequence, index):
        r"""
        Returns a decision that tries to rank last the ith interval var
        in the sequence variable.
        """
        return _pywrapcp.Solver_RankLastInterval(self, sequence, index)

    def Phase(self, *args):
        r"""
        *Overload 1:*
        Phases on IntVar arrays.
        for all other functions that have several homonyms in this .h).

        |

        *Overload 2:*
        Scheduling phases.
        """
        return _pywrapcp.Solver_Phase(self, *args)

    def DecisionBuilderFromAssignment(self, assignment, db, vars):
        r"""
        Returns a decision builder for which the left-most leaf corresponds
        to assignment, the rest of the tree being explored using 'db'.
        """
        return _pywrapcp.Solver_DecisionBuilderFromAssignment(self, assignment, db, vars)

    def ConstraintAdder(self, ct):
        r"""
        Returns a decision builder that will add the given constraint to
        the model.
        """
        return _pywrapcp.Solver_ConstraintAdder(self, ct)

    def SolveOnce(self, db, monitors):
        return _pywrapcp.Solver_SolveOnce(self, db, monitors)

    def NestedOptimize(self, *args):
        r"""
        NestedOptimize will collapse a search tree described by a
        decision builder 'db' and a set of monitors and wrap it into a
        single point. If there are no solutions to this nested tree, then
        NestedOptimize will fail. If there are solutions, it will find
        the best as described by the mandatory objective in the solution
        as well as the optimization direction, instantiate all variables
        to this solution, and return nullptr.
        """
        return _pywrapcp.Solver_NestedOptimize(self, *args)

    def RestoreAssignment(self, assignment):
        r"""
        Returns a DecisionBuilder which restores an Assignment
        (calls void Assignment::Restore())
        """
        return _pywrapcp.Solver_RestoreAssignment(self, assignment)

    def StoreAssignment(self, assignment):
        r"""
        Returns a DecisionBuilder which stores an Assignment
        (calls void Assignment::Store())
        """
        return _pywrapcp.Solver_StoreAssignment(self, assignment)

    def Operator(self, *args):
        r"""Local Search Operators."""
        return _pywrapcp.Solver_Operator(self, *args)

    def RandomLnsOperator(self, *args):
        r"""
        Creates a large neighborhood search operator which creates fragments (set
        of relaxed variables) with up to number_of_variables random variables
        (sampling with replacement is performed meaning that at most
        number_of_variables variables are selected). Warning: this operator will
        always return neighbors; using it without a search limit will result in a
        non-ending search.
        Optionally a random seed can be specified.
        """
        return _pywrapcp.Solver_RandomLnsOperator(self, *args)

    def MoveTowardTargetOperator(self, *args):
        r"""
        *Overload 1:*
        Creates a local search operator that tries to move the assignment of some
        variables toward a target. The target is given as an Assignment. This
        operator generates neighbors in which the only difference compared to the
        current state is that one variable that belongs to the target assignment
        is set to its target value.

        |

        *Overload 2:*
        Creates a local search operator that tries to move the assignment of some
        variables toward a target. The target is given either as two vectors: a
        vector of variables and a vector of associated target values. The two
        vectors should be of the same length. This operator generates neighbors in
        which the only difference compared to the current state is that one
        variable that belongs to the given vector is set to its target value.
        """
        return _pywrapcp.Solver_MoveTowardTargetOperator(self, *args)

    def ConcatenateOperators(self, *args):
        r"""
        Creates a local search operator which concatenates a vector of operators.
        Each operator from the vector is called sequentially. By default, when a
        neighbor is found the neighborhood exploration restarts from the last
        active operator (the one which produced the neighbor).
        This can be overridden by setting restart to true to force the exploration
        to start from the first operator in the vector.

        The default behavior can also be overridden using an evaluation callback
        to set the order in which the operators are explored (the callback is
        called in LocalSearchOperator::Start()). The first argument of the
        callback is the index of the operator which produced the last move, the
        second argument is the index of the operator to be evaluated. Ownership of
        the callback is taken by ConcatenateOperators.

        Example:

         const int kPriorities = {10, 100, 10, 0};
         int64_t Evaluate(int active_operator, int current_operator) {
           return kPriorities[current_operator];
         }

         LocalSearchOperator* concat =
           solver.ConcatenateOperators(operators,
                                       NewPermanentCallback(&Evaluate));

        The elements of the vector operators will be sorted by increasing priority
        and explored in that order (tie-breaks are handled by keeping the relative
        operator order in the vector). This would result in the following order:
        operators[3], operators[0], operators[2], operators[1].
        """
        return _pywrapcp.Solver_ConcatenateOperators(self, *args)

    def RandomConcatenateOperators(self, *args):
        r"""
        *Overload 1:*
        Randomized version of local search concatenator; calls a random operator
        at each call to MakeNextNeighbor().

        |

        *Overload 2:*
        Randomized version of local search concatenator; calls a random operator
        at each call to MakeNextNeighbor(). The provided seed is used to
        initialize the random number generator.
        """
        return _pywrapcp.Solver_RandomConcatenateOperators(self, *args)

    def NeighborhoodLimit(self, op, limit):
        r"""
        Creates a local search operator that wraps another local search
        operator and limits the number of neighbors explored (i.e., calls
        to MakeNextNeighbor from the current solution (between two calls
        to Start()). When this limit is reached, MakeNextNeighbor()
        returns false. The counter is cleared when Start() is called.
        """
        return _pywrapcp.Solver_NeighborhoodLimit(self, op, limit)

    def LocalSearchPhase(self, *args):
        r"""
        *Overload 1:*
        Local Search decision builders factories.
        Local search is used to improve a given solution. This initial solution
        can be specified either by an Assignment or by a DecisionBulder, and the
        corresponding variables, the initial solution being the first solution
        found by the DecisionBuilder.
        The LocalSearchPhaseParameters parameter holds the actual definition of
        the local search phase:
        - a local search operator used to explore the neighborhood of the current
          solution,
        - a decision builder to instantiate unbound variables once a neighbor has
          been defined; in the case of LNS-based operators instantiates fragment
          variables; search monitors can be added to this sub-search by wrapping
          the decision builder with MakeSolveOnce.
        - a search limit specifying how long local search looks for neighbors
          before accepting one; the last neighbor is always taken and in the case
          of a greedy search, this corresponds to the best local neighbor;
          first-accept (which is the default behavior) can be modeled using a
          solution found limit of 1,
        - a vector of local search filters used to speed up the search by pruning
          unfeasible neighbors.
        Metaheuristics can be added by defining specialized search monitors;
        currently down/up-hill climbing is available through OptimizeVar, as well
        as Guided Local Search, Tabu Search and Simulated Annealing.

        |

        *Overload 2:*
        Variant with a sub_decison_builder specific to the first solution.
        """
        return _pywrapcp.Solver_LocalSearchPhase(self, *args)

    def LocalSearchPhaseParameters(self, *args):
        r"""Local Search Phase Parameters"""
        return _pywrapcp.Solver_LocalSearchPhaseParameters(self, *args)

    def TopProgressPercent(self):
        r"""
        Returns a percentage representing the propress of the search before
        reaching the limits of the top-level search (can be called from a nested
        solve).
        """
        return _pywrapcp.Solver_TopProgressPercent(self)

    def SearchDepth(self):
        r"""
        Gets the search depth of the current active search. Returns -1 if
        there is no active search opened.
        """
        return _pywrapcp.Solver_SearchDepth(self)

    def SearchLeftDepth(self):
        r"""
        Gets the search left depth of the current active search. Returns -1 if
        there is no active search opened.
        """
        return _pywrapcp.Solver_SearchLeftDepth(self)

    def SolveDepth(self):
        r"""
        Gets the number of nested searches. It returns 0 outside search,
        1 during the top level search, 2 or more in case of nested searches.
        """
        return _pywrapcp.Solver_SolveDepth(self)

    def Rand64(self, size):
        r"""Returns a random value between 0 and 'size' - 1;"""
        return _pywrapcp.Solver_Rand64(self, size)

    def Rand32(self, size):
        r"""Returns a random value between 0 and 'size' - 1;"""
        return _pywrapcp.Solver_Rand32(self, size)

    def ReSeed(self, seed):
        r"""Reseed the solver random generator."""
        return _pywrapcp.Solver_ReSeed(self, seed)

    def LocalSearchProfile(self):
        r"""Returns local search profiling information in a human readable format."""
        return _pywrapcp.Solver_LocalSearchProfile(self)

    def Constraints(self):
        r"""
        Counts the number of constraints that have been added
        to the solver before the search.
        """
        return _pywrapcp.Solver_Constraints(self)

    def Accept(self, visitor):
        r"""Accepts the given model visitor."""
        return _pywrapcp.Solver_Accept(self, visitor)

    def FinishCurrentSearch(self):
        r"""Tells the solver to kill or restart the current search."""
        return _pywrapcp.Solver_FinishCurrentSearch(self)

    def RestartCurrentSearch(self):
        return _pywrapcp.Solver_RestartCurrentSearch(self)

    def ShouldFail(self):
        r"""
        These methods are only useful for the SWIG wrappers, which need a way
        to externally cause the Solver to fail.
        """
        return _pywrapcp.Solver_ShouldFail(self)

    def __str__(self):
        return _pywrapcp.Solver___str__(self)

    def Add(self, ct):
      if isinstance(ct, PyConstraint):
        self.__python_constraints.append(ct)
      self.AddConstraint(ct)


    def TreeNoCycle(self, nexts, active, callback=0):
        return _pywrapcp.Solver_TreeNoCycle(self, nexts, active, callback)

    def SearchLogWithCallback(self, period, callback):
        return _pywrapcp.Solver_SearchLogWithCallback(self, period, callback)

    def ElementFunction(self, values, index):
        return _pywrapcp.Solver_ElementFunction(self, values, index)

    def VarEvalValStrPhase(self, vars, var_evaluator, val_str):
        return _pywrapcp.Solver_VarEvalValStrPhase(self, vars, var_evaluator, val_str)

    def VarStrValEvalPhase(self, vars, var_str, val_eval):
        return _pywrapcp.Solver_VarStrValEvalPhase(self, vars, var_str, val_eval)

    def VarEvalValEvalPhase(self, vars, var_eval, val_eval):
        return _pywrapcp.Solver_VarEvalValEvalPhase(self, vars, var_eval, val_eval)

    def VarStrValEvalTieBreakPhase(self, vars, var_str, val_eval, tie_breaker):
        return _pywrapcp.Solver_VarStrValEvalTieBreakPhase(self, vars, var_str, val_eval, tie_breaker)

    def VarEvalValEvalTieBreakPhase(self, vars, var_eval, val_eval, tie_breaker):
        return _pywrapcp.Solver_VarEvalValEvalTieBreakPhase(self, vars, var_eval, val_eval, tie_breaker)

    def EvalEvalStrPhase(self, vars, evaluator, str):
        return _pywrapcp.Solver_EvalEvalStrPhase(self, vars, evaluator, str)

    def EvalEvalStrTieBreakPhase(self, vars, evaluator, tie_breaker, str):
        return _pywrapcp.Solver_EvalEvalStrTieBreakPhase(self, vars, evaluator, tie_breaker, str)

    def GuidedLocalSearch(self, maximize, objective, objective_function, step, vars, penalty_factor):
        return _pywrapcp.Solver_GuidedLocalSearch(self, maximize, objective, objective_function, step, vars, penalty_factor)

    def SumObjectiveFilter(self, vars, values, filter_enum):
        return _pywrapcp.Solver_SumObjectiveFilter(self, vars, values, filter_enum)

# Register Solver in _pywrapcp:
_pywrapcp.Solver_swigregister(Solver)
class BaseObject(object):
    r"""
    A BaseObject is the root of all reversibly allocated objects.
    A DebugString method and the associated << operator are implemented
    as a convenience.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self):
        if self.__class__ == BaseObject:
            _self = None
        else:
            _self = self
        _pywrapcp.BaseObject_swiginit(self, _pywrapcp.new_BaseObject(_self, ))
    __swig_destroy__ = _pywrapcp.delete_BaseObject

    def DebugString(self):
        return _pywrapcp.BaseObject_DebugString(self)

    def __str__(self):
        return _pywrapcp.BaseObject___str__(self)

    def __repr__(self):
        return _pywrapcp.BaseObject___repr__(self)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_BaseObject(self)
        return weakref.proxy(self)

# Register BaseObject in _pywrapcp:
_pywrapcp.BaseObject_swigregister(BaseObject)
class PropagationBaseObject(BaseObject):
    r"""
    NOLINT
    The PropagationBaseObject is a subclass of BaseObject that is also
    friend to the Solver class. It allows accessing methods useful when
    writing new constraints or new expressions.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, s):
        if self.__class__ == PropagationBaseObject:
            _self = None
        else:
            _self = self
        _pywrapcp.PropagationBaseObject_swiginit(self, _pywrapcp.new_PropagationBaseObject(_self, s))
    __swig_destroy__ = _pywrapcp.delete_PropagationBaseObject

    def DebugString(self):
        return _pywrapcp.PropagationBaseObject_DebugString(self)

    def solver(self):
        return _pywrapcp.PropagationBaseObject_solver(self)

    def Name(self):
        r"""Object naming."""
        return _pywrapcp.PropagationBaseObject_Name(self)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_PropagationBaseObject(self)
        return weakref.proxy(self)

# Register PropagationBaseObject in _pywrapcp:
_pywrapcp.PropagationBaseObject_swigregister(PropagationBaseObject)
class Decision(BaseObject):
    r"""
    A Decision represents a choice point in the search tree. The two main
    methods are Apply() to go left, or Refute() to go right.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self):
        if self.__class__ == Decision:
            _self = None
        else:
            _self = self
        _pywrapcp.Decision_swiginit(self, _pywrapcp.new_Decision(_self, ))
    __swig_destroy__ = _pywrapcp.delete_Decision

    def ApplyWrapper(self, s):
        r"""Apply will be called first when the decision is executed."""
        return _pywrapcp.Decision_ApplyWrapper(self, s)

    def RefuteWrapper(self, s):
        r"""Refute will be called after a backtrack."""
        return _pywrapcp.Decision_RefuteWrapper(self, s)

    def DebugString(self):
        return _pywrapcp.Decision_DebugString(self)

    def __repr__(self):
        return _pywrapcp.Decision___repr__(self)

    def __str__(self):
        return _pywrapcp.Decision___str__(self)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_Decision(self)
        return weakref.proxy(self)

# Register Decision in _pywrapcp:
_pywrapcp.Decision_swigregister(Decision)
class DecisionBuilder(BaseObject):
    r"""
    A DecisionBuilder is responsible for creating the search tree. The
    important method is Next(), which returns the next decision to execute.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self):
        if self.__class__ == DecisionBuilder:
            _self = None
        else:
            _self = self
        _pywrapcp.DecisionBuilder_swiginit(self, _pywrapcp.new_DecisionBuilder(_self, ))
    __swig_destroy__ = _pywrapcp.delete_DecisionBuilder

    def NextWrapper(self, s):
        r"""
        This is the main method of the decision builder class. It must
        return a decision (an instance of the class Decision). If it
        returns nullptr, this means that the decision builder has finished
        its work.
        """
        return _pywrapcp.DecisionBuilder_NextWrapper(self, s)

    def DebugString(self):
        return _pywrapcp.DecisionBuilder_DebugString(self)

    def __repr__(self):
        return _pywrapcp.DecisionBuilder___repr__(self)

    def __str__(self):
        return _pywrapcp.DecisionBuilder___str__(self)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_DecisionBuilder(self)
        return weakref.proxy(self)

# Register DecisionBuilder in _pywrapcp:
_pywrapcp.DecisionBuilder_swigregister(DecisionBuilder)
class Demon(BaseObject):
    r"""
    A Demon is the base element of a propagation queue. It is the main
      object responsible for implementing the actual propagation
      of the constraint and pruning the inconsistent values in the domains
      of the variables. The main concept is that demons are listeners that are
      attached to the variables and listen to their modifications.
    There are two methods:
     - Run() is the actual method called when the demon is processed.
     - priority() returns its priority. Standard priorities are slow, normal
       or fast. "immediate" is reserved for variables and is treated separately.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self):
        r"""
        This indicates the priority of a demon. Immediate demons are treated
        separately and corresponds to variables.
        """
        if self.__class__ == Demon:
            _self = None
        else:
            _self = self
        _pywrapcp.Demon_swiginit(self, _pywrapcp.new_Demon(_self, ))
    __swig_destroy__ = _pywrapcp.delete_Demon

    def RunWrapper(self, s):
        r"""This is the main callback of the demon."""
        return _pywrapcp.Demon_RunWrapper(self, s)

    def Priority(self):
        r"""
        This method returns the priority of the demon. Usually a demon is
        fast, slow or normal. Immediate demons are reserved for internal
        use to maintain variables.
        """
        return _pywrapcp.Demon_Priority(self)

    def DebugString(self):
        return _pywrapcp.Demon_DebugString(self)

    def Inhibit(self, s):
        r"""
        This method inhibits the demon in the search tree below the
        current position.
        """
        return _pywrapcp.Demon_Inhibit(self, s)

    def Desinhibit(self, s):
        r"""This method un-inhibits the demon that was previously inhibited."""
        return _pywrapcp.Demon_Desinhibit(self, s)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_Demon(self)
        return weakref.proxy(self)

# Register Demon in _pywrapcp:
_pywrapcp.Demon_swigregister(Demon)
class Constraint(PropagationBaseObject):
    r"""
    A constraint is the main modeling object. It provides two methods:
      - Post() is responsible for creating the demons and attaching them to
        immediate demons().
      - InitialPropagate() is called once just after Post and performs
        the initial propagation. The subsequent propagations will be performed
        by the demons Posted during the post() method.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, solver):
        if self.__class__ == Constraint:
            _self = None
        else:
            _self = self
        _pywrapcp.Constraint_swiginit(self, _pywrapcp.new_Constraint(_self, solver))
    __swig_destroy__ = _pywrapcp.delete_Constraint

    def Post(self):
        r"""
        This method is called when the constraint is processed by the
        solver. Its main usage is to attach demons to variables.
        """
        return _pywrapcp.Constraint_Post(self)

    def InitialPropagateWrapper(self):
        r"""
        This method performs the initial propagation of the
        constraint. It is called just after the post.
        """
        return _pywrapcp.Constraint_InitialPropagateWrapper(self)

    def DebugString(self):
        return _pywrapcp.Constraint_DebugString(self)

    def Var(self):
        r"""
        Creates a Boolean variable representing the status of the constraint
        (false = constraint is violated, true = constraint is satisfied). It
        returns nullptr if the constraint does not support this API.
        """
        return _pywrapcp.Constraint_Var(self)

    def __repr__(self):
        return _pywrapcp.Constraint___repr__(self)

    def __str__(self):
        return _pywrapcp.Constraint___str__(self)

    def __add__(self, *args):
        return _pywrapcp.Constraint___add__(self, *args)

    def __radd__(self, v):
        return _pywrapcp.Constraint___radd__(self, v)

    def __sub__(self, *args):
        return _pywrapcp.Constraint___sub__(self, *args)

    def __rsub__(self, v):
        return _pywrapcp.Constraint___rsub__(self, v)

    def __mul__(self, *args):
        return _pywrapcp.Constraint___mul__(self, *args)

    def __rmul__(self, v):
        return _pywrapcp.Constraint___rmul__(self, v)

    def __floordiv__(self, v):
        return _pywrapcp.Constraint___floordiv__(self, v)

    def __neg__(self):
        return _pywrapcp.Constraint___neg__(self)

    def __abs__(self):
        return _pywrapcp.Constraint___abs__(self)

    def Square(self):
        return _pywrapcp.Constraint_Square(self)

    def __eq__(self, *args):
        return _pywrapcp.Constraint___eq__(self, *args)

    def __ne__(self, *args):
        return _pywrapcp.Constraint___ne__(self, *args)

    def __ge__(self, *args):
        return _pywrapcp.Constraint___ge__(self, *args)

    def __gt__(self, *args):
        return _pywrapcp.Constraint___gt__(self, *args)

    def __le__(self, *args):
        return _pywrapcp.Constraint___le__(self, *args)

    def __lt__(self, *args):
        return _pywrapcp.Constraint___lt__(self, *args)

    def MapTo(self, vars):
        return _pywrapcp.Constraint_MapTo(self, vars)

    def IndexOf(self, *args):
        return _pywrapcp.Constraint_IndexOf(self, *args)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_Constraint(self)
        return weakref.proxy(self)

# Register Constraint in _pywrapcp:
_pywrapcp.Constraint_swigregister(Constraint)
class SearchMonitor(BaseObject):
    r"""A search monitor is a simple set of callbacks to monitor all search events"""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    kNoProgress = _pywrapcp.SearchMonitor_kNoProgress

    def __init__(self, s):
        if self.__class__ == SearchMonitor:
            _self = None
        else:
            _self = self
        _pywrapcp.SearchMonitor_swiginit(self, _pywrapcp.new_SearchMonitor(_self, s))
    __swig_destroy__ = _pywrapcp.delete_SearchMonitor

    def EnterSearch(self):
        r"""Beginning of the search."""
        return _pywrapcp.SearchMonitor_EnterSearch(self)

    def RestartSearch(self):
        r"""Restart the search."""
        return _pywrapcp.SearchMonitor_RestartSearch(self)

    def ExitSearch(self):
        r"""End of the search."""
        return _pywrapcp.SearchMonitor_ExitSearch(self)

    def BeginNextDecision(self, b):
        r"""Before calling DecisionBuilder::Next."""
        return _pywrapcp.SearchMonitor_BeginNextDecision(self, b)

    def EndNextDecision(self, b, d):
        r"""After calling DecisionBuilder::Next, along with the returned decision."""
        return _pywrapcp.SearchMonitor_EndNextDecision(self, b, d)

    def ApplyDecision(self, d):
        r"""Before applying the decision."""
        return _pywrapcp.SearchMonitor_ApplyDecision(self, d)

    def RefuteDecision(self, d):
        r"""Before refuting the decision."""
        return _pywrapcp.SearchMonitor_RefuteDecision(self, d)

    def AfterDecision(self, d, apply):
        r"""
        Just after refuting or applying the decision, apply is true after Apply.
        This is called only if the Apply() or Refute() methods have not failed.
        """
        return _pywrapcp.SearchMonitor_AfterDecision(self, d, apply)

    def BeginFail(self):
        r"""Just when the failure occurs."""
        return _pywrapcp.SearchMonitor_BeginFail(self)

    def EndFail(self):
        r"""After completing the backtrack."""
        return _pywrapcp.SearchMonitor_EndFail(self)

    def BeginInitialPropagation(self):
        r"""Before the initial propagation."""
        return _pywrapcp.SearchMonitor_BeginInitialPropagation(self)

    def EndInitialPropagation(self):
        r"""After the initial propagation."""
        return _pywrapcp.SearchMonitor_EndInitialPropagation(self)

    def AcceptSolution(self):
        r"""
        This method is called when a solution is found. It asserts whether the
        solution is valid. A value of false indicates that the solution
        should be discarded.
        """
        return _pywrapcp.SearchMonitor_AcceptSolution(self)

    def AtSolution(self):
        r"""
        This method is called when a valid solution is found. If the
        return value is true, then search will resume after. If the result
        is false, then search will stop there.
        """
        return _pywrapcp.SearchMonitor_AtSolution(self)

    def NoMoreSolutions(self):
        r"""When the search tree is finished."""
        return _pywrapcp.SearchMonitor_NoMoreSolutions(self)

    def LocalOptimum(self):
        r"""
        When a local optimum is reached. If 'true' is returned, the last solution
        is discarded and the search proceeds with the next one.
        """
        return _pywrapcp.SearchMonitor_LocalOptimum(self)

    def AcceptDelta(self, delta, deltadelta):
        
        return _pywrapcp.SearchMonitor_AcceptDelta(self, delta, deltadelta)

    def AcceptNeighbor(self):
        r"""After accepting a neighbor during local search."""
        return _pywrapcp.SearchMonitor_AcceptNeighbor(self)

    def ProgressPercent(self):
        r"""
        Returns a percentage representing the propress of the search before
        reaching limits.
        """
        return _pywrapcp.SearchMonitor_ProgressPercent(self)

    def solver(self):
        return _pywrapcp.SearchMonitor_solver(self)

    def __repr__(self):
        return _pywrapcp.SearchMonitor___repr__(self)

    def __str__(self):
        return _pywrapcp.SearchMonitor___str__(self)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_SearchMonitor(self)
        return weakref.proxy(self)

# Register SearchMonitor in _pywrapcp:
_pywrapcp.SearchMonitor_swigregister(SearchMonitor)
class IntExpr(PropagationBaseObject):
    r"""
    The class IntExpr is the base of all integer expressions in
    constraint programming.
    It contains the basic protocol for an expression:
      - setting and modifying its bound
      - querying if it is bound
      - listening to events modifying its bounds
      - casting it into a variable (instance of IntVar)
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")

    def Min(self):
        return _pywrapcp.IntExpr_Min(self)

    def SetMin(self, m):
        return _pywrapcp.IntExpr_SetMin(self, m)

    def Max(self):
        return _pywrapcp.IntExpr_Max(self)

    def SetMax(self, m):
        return _pywrapcp.IntExpr_SetMax(self, m)

    def SetRange(self, l, u):
        r"""This method sets both the min and the max of the expression."""
        return _pywrapcp.IntExpr_SetRange(self, l, u)

    def SetValue(self, v):
        r"""This method sets the value of the expression."""
        return _pywrapcp.IntExpr_SetValue(self, v)

    def Bound(self):
        r"""Returns true if the min and the max of the expression are equal."""
        return _pywrapcp.IntExpr_Bound(self)

    def IsVar(self):
        r"""Returns true if the expression is indeed a variable."""
        return _pywrapcp.IntExpr_IsVar(self)

    def Var(self):
        r"""Creates a variable from the expression."""
        return _pywrapcp.IntExpr_Var(self)

    def VarWithName(self, name):
        r"""
        Creates a variable from the expression and set the name of the
        resulting var. If the expression is already a variable, then it
        will set the name of the expression, possibly overwriting it.
        This is just a shortcut to Var() followed by set_name().
        """
        return _pywrapcp.IntExpr_VarWithName(self, name)

    def WhenRange(self, *args):
        r"""
        *Overload 1:*
        Attach a demon that will watch the min or the max of the expression.

        |

        *Overload 2:*
        Attach a demon that will watch the min or the max of the expression.
        """
        return _pywrapcp.IntExpr_WhenRange(self, *args)

    def __repr__(self):
        return _pywrapcp.IntExpr___repr__(self)

    def __str__(self):
        return _pywrapcp.IntExpr___str__(self)

    def __add__(self, *args):
        return _pywrapcp.IntExpr___add__(self, *args)

    def __radd__(self, v):
        return _pywrapcp.IntExpr___radd__(self, v)

    def __sub__(self, *args):
        return _pywrapcp.IntExpr___sub__(self, *args)

    def __rsub__(self, v):
        return _pywrapcp.IntExpr___rsub__(self, v)

    def __mul__(self, *args):
        return _pywrapcp.IntExpr___mul__(self, *args)

    def __rmul__(self, v):
        return _pywrapcp.IntExpr___rmul__(self, v)

    def __floordiv__(self, *args):
        return _pywrapcp.IntExpr___floordiv__(self, *args)

    def __mod__(self, *args):
        return _pywrapcp.IntExpr___mod__(self, *args)

    def __neg__(self):
        return _pywrapcp.IntExpr___neg__(self)

    def __abs__(self):
        return _pywrapcp.IntExpr___abs__(self)

    def Square(self):
        return _pywrapcp.IntExpr_Square(self)

    def __eq__(self, *args):
        return _pywrapcp.IntExpr___eq__(self, *args)

    def __ne__(self, *args):
        return _pywrapcp.IntExpr___ne__(self, *args)

    def __ge__(self, *args):
        return _pywrapcp.IntExpr___ge__(self, *args)

    def __gt__(self, *args):
        return _pywrapcp.IntExpr___gt__(self, *args)

    def __le__(self, *args):
        return _pywrapcp.IntExpr___le__(self, *args)

    def __lt__(self, *args):
        return _pywrapcp.IntExpr___lt__(self, *args)

    def MapTo(self, vars):
        return _pywrapcp.IntExpr_MapTo(self, vars)

    def IndexOf(self, *args):
        return _pywrapcp.IntExpr_IndexOf(self, *args)

    def IsMember(self, values):
        return _pywrapcp.IntExpr_IsMember(self, values)

    def Member(self, values):
        return _pywrapcp.IntExpr_Member(self, values)

    def NotMember(self, starts, ends):
        return _pywrapcp.IntExpr_NotMember(self, starts, ends)

# Register IntExpr in _pywrapcp:
_pywrapcp.IntExpr_swigregister(IntExpr)
class IntVarIterator(BaseObject):
    r"""
     The class Iterator has two direct subclasses. HoleIterators
     iterates over all holes, that is value removed between the
     current min and max of the variable since the last time the
     variable was processed in the queue. DomainIterators iterates
     over all elements of the variable domain. Both iterators are not
     robust to domain changes. Hole iterators can also report values outside
     the current min and max of the variable.
     HoleIterators should only be called from a demon attached to the
     variable that has created this iterator.
     IntVar* current_var;
     std::unique_ptr<IntVarIterator> it(current_var->MakeHoleIterator(false));
     for (const int64_t hole : InitAndGetValues(it)) {
    use the hole
     }
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr

    def Init(self):
        r"""This method must be called before each loop."""
        return _pywrapcp.IntVarIterator_Init(self)

    def Ok(self):
        r"""This method indicates if we can call Value() or not."""
        return _pywrapcp.IntVarIterator_Ok(self)

    def Value(self):
        r"""This method returns the current value of the iterator."""
        return _pywrapcp.IntVarIterator_Value(self)

    def Next(self):
        r"""This method moves the iterator to the next value."""
        return _pywrapcp.IntVarIterator_Next(self)

    def DebugString(self):
        r"""Pretty Print."""
        return _pywrapcp.IntVarIterator_DebugString(self)

    def __iter__(self):
      self.Init()
      return self

    def next(self):
      if self.Ok():
        result = self.Value()
        self.Next()
        return result
      else:
        raise StopIteration()

    def __next__(self):
      return self.next()


# Register IntVarIterator in _pywrapcp:
_pywrapcp.IntVarIterator_swigregister(IntVarIterator)
class IntVar(IntExpr):
    r"""
    The class IntVar is a subset of IntExpr. In addition to the
    IntExpr protocol, it offers persistence, removing values from the domains,
    and a finer model for events.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")

    def IsVar(self):
        return _pywrapcp.IntVar_IsVar(self)

    def Var(self):
        return _pywrapcp.IntVar_Var(self)

    def Value(self):
        r"""
        This method returns the value of the variable. This method checks
        before that the variable is bound.
        """
        return _pywrapcp.IntVar_Value(self)

    def RemoveValue(self, v):
        r"""This method removes the value 'v' from the domain of the variable."""
        return _pywrapcp.IntVar_RemoveValue(self, v)

    def RemoveInterval(self, l, u):
        r"""
        This method removes the interval 'l' .. 'u' from the domain of
        the variable. It assumes that 'l' <= 'u'.
        """
        return _pywrapcp.IntVar_RemoveInterval(self, l, u)

    def RemoveValues(self, values):
        r"""This method remove the values from the domain of the variable."""
        return _pywrapcp.IntVar_RemoveValues(self, values)

    def SetValues(self, values):
        r"""This method intersects the current domain with the values in the array."""
        return _pywrapcp.IntVar_SetValues(self, values)

    def WhenBound(self, *args):
        r"""
        *Overload 1:*
        This method attaches a demon that will be awakened when the
        variable is bound.

        |

        *Overload 2:*
        This method attaches a closure that will be awakened when the
        variable is bound.
        """
        return _pywrapcp.IntVar_WhenBound(self, *args)

    def WhenDomain(self, *args):
        r"""
        *Overload 1:*
        This method attaches a demon that will watch any domain
        modification of the domain of the variable.

        |

        *Overload 2:*
        This method attaches a closure that will watch any domain
        modification of the domain of the variable.
        """
        return _pywrapcp.IntVar_WhenDomain(self, *args)

    def Size(self):
        r"""This method returns the number of values in the domain of the variable."""
        return _pywrapcp.IntVar_Size(self)

    def Contains(self, v):
        r"""
        This method returns whether the value 'v' is in the domain of the
        variable.
        """
        return _pywrapcp.IntVar_Contains(self, v)

    def HoleIteratorAux(self, reversible):
        r"""
        Creates a hole iterator. When 'reversible' is false, the returned
        object is created on the normal C++ heap and the solver does NOT
        take ownership of the object.
        """
        return _pywrapcp.IntVar_HoleIteratorAux(self, reversible)

    def DomainIteratorAux(self, reversible):
        r"""
        Creates a domain iterator. When 'reversible' is false, the
        returned object is created on the normal C++ heap and the solver
        does NOT take ownership of the object.
        """
        return _pywrapcp.IntVar_DomainIteratorAux(self, reversible)

    def OldMin(self):
        r"""Returns the previous min."""
        return _pywrapcp.IntVar_OldMin(self)

    def OldMax(self):
        r"""Returns the previous max."""
        return _pywrapcp.IntVar_OldMax(self)

    def __repr__(self):
        return _pywrapcp.IntVar___repr__(self)

    def __str__(self):
        return _pywrapcp.IntVar___str__(self)

    def DomainIterator(self):
      return iter(self.DomainIteratorAux(False))

    def HoleIterator(self):
      return iter(self.HoleIteratorAux(False))


# Register IntVar in _pywrapcp:
_pywrapcp.IntVar_swigregister(IntVar)
class SolutionCollector(SearchMonitor):
    r"""
    This class is the root class of all solution collectors.
    It implements a basic query API to be used independently
    of the collector used.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def DebugString(self):
        return _pywrapcp.SolutionCollector_DebugString(self)

    def Add(self, *args):
        r"""Add API."""
        return _pywrapcp.SolutionCollector_Add(self, *args)

    def AddObjective(self, objective):
        return _pywrapcp.SolutionCollector_AddObjective(self, objective)

    def EnterSearch(self):
        r"""Beginning of the search."""
        return _pywrapcp.SolutionCollector_EnterSearch(self)

    def SolutionCount(self):
        r"""Returns how many solutions were stored during the search."""
        return _pywrapcp.SolutionCollector_SolutionCount(self)

    def Solution(self, n):
        r"""Returns the nth solution."""
        return _pywrapcp.SolutionCollector_Solution(self, n)

    def WallTime(self, n):
        r"""Returns the wall time in ms for the nth solution."""
        return _pywrapcp.SolutionCollector_WallTime(self, n)

    def Branches(self, n):
        r"""Returns the number of branches when the nth solution was found."""
        return _pywrapcp.SolutionCollector_Branches(self, n)

    def Failures(self, n):
        r"""
        Returns the number of failures encountered at the time of the nth
        solution.
        """
        return _pywrapcp.SolutionCollector_Failures(self, n)

    def ObjectiveValue(self, n):
        r"""Returns the objective value of the nth solution."""
        return _pywrapcp.SolutionCollector_ObjectiveValue(self, n)

    def Value(self, n, var):
        r"""This is a shortcut to get the Value of 'var' in the nth solution."""
        return _pywrapcp.SolutionCollector_Value(self, n, var)

    def StartValue(self, n, var):
        r"""This is a shortcut to get the StartValue of 'var' in the nth solution."""
        return _pywrapcp.SolutionCollector_StartValue(self, n, var)

    def EndValue(self, n, var):
        r"""This is a shortcut to get the EndValue of 'var' in the nth solution."""
        return _pywrapcp.SolutionCollector_EndValue(self, n, var)

    def DurationValue(self, n, var):
        r"""This is a shortcut to get the DurationValue of 'var' in the nth solution."""
        return _pywrapcp.SolutionCollector_DurationValue(self, n, var)

    def PerformedValue(self, n, var):
        r"""This is a shortcut to get the PerformedValue of 'var' in the nth solution."""
        return _pywrapcp.SolutionCollector_PerformedValue(self, n, var)

    def ForwardSequence(self, n, var):
        r"""
        This is a shortcut to get the ForwardSequence of 'var' in the
        nth solution. The forward sequence is the list of ranked interval
        variables starting from the start of the sequence.
        """
        return _pywrapcp.SolutionCollector_ForwardSequence(self, n, var)

    def BackwardSequence(self, n, var):
        r"""
        This is a shortcut to get the BackwardSequence of 'var' in the
        nth solution. The backward sequence is the list of ranked interval
        variables starting from the end of the sequence.
        """
        return _pywrapcp.SolutionCollector_BackwardSequence(self, n, var)

    def Unperformed(self, n, var):
        r"""
        This is a shortcut to get the list of unperformed of 'var' in the
        nth solution.
        """
        return _pywrapcp.SolutionCollector_Unperformed(self, n, var)

# Register SolutionCollector in _pywrapcp:
_pywrapcp.SolutionCollector_swigregister(SolutionCollector)
class OptimizeVar(object):
    r"""
    This class encapsulates an objective. It requires the direction
    (minimize or maximize), the variable to optimize, and the
    improvement step.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Best(self):
        r"""Returns the best value found during search."""
        return _pywrapcp.OptimizeVar_Best(self)

    def BeginNextDecision(self, db):
        r"""Internal methods."""
        return _pywrapcp.OptimizeVar_BeginNextDecision(self, db)

    def RefuteDecision(self, d):
        return _pywrapcp.OptimizeVar_RefuteDecision(self, d)

    def AtSolution(self):
        return _pywrapcp.OptimizeVar_AtSolution(self)

    def AcceptSolution(self):
        return _pywrapcp.OptimizeVar_AcceptSolution(self)

    def DebugString(self):
        return _pywrapcp.OptimizeVar_DebugString(self)
    __swig_destroy__ = _pywrapcp.delete_OptimizeVar

# Register OptimizeVar in _pywrapcp:
_pywrapcp.OptimizeVar_swigregister(OptimizeVar)
class SearchLimit(SearchMonitor):
    r"""Base class of all search limits."""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr
    __swig_destroy__ = _pywrapcp.delete_SearchLimit

    def Crossed(self):
        r"""Returns true if the limit has been crossed."""
        return _pywrapcp.SearchLimit_Crossed(self)

    def Check(self):
        r"""
        This method is called to check the status of the limit. A return
        value of true indicates that we have indeed crossed the limit. In
        that case, this method will not be called again and the remaining
        search will be discarded.
        """
        return _pywrapcp.SearchLimit_Check(self)

    def Init(self):
        r"""This method is called when the search limit is initialized."""
        return _pywrapcp.SearchLimit_Init(self)

    def EnterSearch(self):
        r"""Internal methods."""
        return _pywrapcp.SearchLimit_EnterSearch(self)

    def BeginNextDecision(self, b):
        return _pywrapcp.SearchLimit_BeginNextDecision(self, b)

    def RefuteDecision(self, d):
        return _pywrapcp.SearchLimit_RefuteDecision(self, d)

    def DebugString(self):
        return _pywrapcp.SearchLimit_DebugString(self)

# Register SearchLimit in _pywrapcp:
_pywrapcp.SearchLimit_swigregister(SearchLimit)
class IntervalVar(PropagationBaseObject):
    r"""
    Interval variables are often used in scheduling. The main characteristics
    of an IntervalVar are the start position, duration, and end
    date. All these characteristics can be queried and set, and demons can
    be posted on their modifications.

    An important aspect is optionality: an IntervalVar can be performed or not.
    If unperformed, then it simply does not exist, and its characteristics
    cannot be accessed any more. An interval var is automatically marked
    as unperformed when it is not consistent anymore (start greater
    than end, duration < 0...)
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")

    def StartMin(self):
        r"""
        These methods query, set, and watch the start position of the
        interval var.
        """
        return _pywrapcp.IntervalVar_StartMin(self)

    def StartMax(self):
        return _pywrapcp.IntervalVar_StartMax(self)

    def SetStartMin(self, m):
        return _pywrapcp.IntervalVar_SetStartMin(self, m)

    def SetStartMax(self, m):
        return _pywrapcp.IntervalVar_SetStartMax(self, m)

    def SetStartRange(self, mi, ma):
        return _pywrapcp.IntervalVar_SetStartRange(self, mi, ma)

    def OldStartMin(self):
        return _pywrapcp.IntervalVar_OldStartMin(self)

    def OldStartMax(self):
        return _pywrapcp.IntervalVar_OldStartMax(self)

    def WhenStartRange(self, *args):
        return _pywrapcp.IntervalVar_WhenStartRange(self, *args)

    def WhenStartBound(self, *args):
        return _pywrapcp.IntervalVar_WhenStartBound(self, *args)

    def DurationMin(self):
        r"""These methods query, set, and watch the duration of the interval var."""
        return _pywrapcp.IntervalVar_DurationMin(self)

    def DurationMax(self):
        return _pywrapcp.IntervalVar_DurationMax(self)

    def SetDurationMin(self, m):
        return _pywrapcp.IntervalVar_SetDurationMin(self, m)

    def SetDurationMax(self, m):
        return _pywrapcp.IntervalVar_SetDurationMax(self, m)

    def SetDurationRange(self, mi, ma):
        return _pywrapcp.IntervalVar_SetDurationRange(self, mi, ma)

    def OldDurationMin(self):
        return _pywrapcp.IntervalVar_OldDurationMin(self)

    def OldDurationMax(self):
        return _pywrapcp.IntervalVar_OldDurationMax(self)

    def WhenDurationRange(self, *args):
        return _pywrapcp.IntervalVar_WhenDurationRange(self, *args)

    def WhenDurationBound(self, *args):
        return _pywrapcp.IntervalVar_WhenDurationBound(self, *args)

    def EndMin(self):
        r"""These methods query, set, and watch the end position of the interval var."""
        return _pywrapcp.IntervalVar_EndMin(self)

    def EndMax(self):
        return _pywrapcp.IntervalVar_EndMax(self)

    def SetEndMin(self, m):
        return _pywrapcp.IntervalVar_SetEndMin(self, m)

    def SetEndMax(self, m):
        return _pywrapcp.IntervalVar_SetEndMax(self, m)

    def SetEndRange(self, mi, ma):
        return _pywrapcp.IntervalVar_SetEndRange(self, mi, ma)

    def OldEndMin(self):
        return _pywrapcp.IntervalVar_OldEndMin(self)

    def OldEndMax(self):
        return _pywrapcp.IntervalVar_OldEndMax(self)

    def WhenEndRange(self, *args):
        return _pywrapcp.IntervalVar_WhenEndRange(self, *args)

    def WhenEndBound(self, *args):
        return _pywrapcp.IntervalVar_WhenEndBound(self, *args)

    def MustBePerformed(self):
        r"""
        These methods query, set, and watch the performed status of the
        interval var.
        """
        return _pywrapcp.IntervalVar_MustBePerformed(self)

    def MayBePerformed(self):
        return _pywrapcp.IntervalVar_MayBePerformed(self)

    def CannotBePerformed(self):
        return _pywrapcp.IntervalVar_CannotBePerformed(self)

    def IsPerformedBound(self):
        return _pywrapcp.IntervalVar_IsPerformedBound(self)

    def SetPerformed(self, val):
        return _pywrapcp.IntervalVar_SetPerformed(self, val)

    def WasPerformedBound(self):
        return _pywrapcp.IntervalVar_WasPerformedBound(self)

    def WhenPerformedBound(self, *args):
        return _pywrapcp.IntervalVar_WhenPerformedBound(self, *args)

    def WhenAnything(self, *args):
        r"""
        *Overload 1:*
        Attaches a demon awakened when anything about this interval changes.

        |

        *Overload 2:*
        Attaches a closure awakened when anything about this interval changes.
        """
        return _pywrapcp.IntervalVar_WhenAnything(self, *args)

    def StartExpr(self):
        r"""
        These methods create expressions encapsulating the start, end
        and duration of the interval var. Please note that these must not
        be used if the interval var is unperformed.
        """
        return _pywrapcp.IntervalVar_StartExpr(self)

    def DurationExpr(self):
        return _pywrapcp.IntervalVar_DurationExpr(self)

    def EndExpr(self):
        return _pywrapcp.IntervalVar_EndExpr(self)

    def PerformedExpr(self):
        return _pywrapcp.IntervalVar_PerformedExpr(self)

    def SafeStartExpr(self, unperformed_value):
        r"""
        These methods create expressions encapsulating the start, end
        and duration of the interval var. If the interval var is
        unperformed, they will return the unperformed_value.
        """
        return _pywrapcp.IntervalVar_SafeStartExpr(self, unperformed_value)

    def SafeDurationExpr(self, unperformed_value):
        return _pywrapcp.IntervalVar_SafeDurationExpr(self, unperformed_value)

    def SafeEndExpr(self, unperformed_value):
        return _pywrapcp.IntervalVar_SafeEndExpr(self, unperformed_value)

    def EndsAfterEnd(self, other):
        return _pywrapcp.IntervalVar_EndsAfterEnd(self, other)

    def EndsAfterEndWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_EndsAfterEndWithDelay(self, other, delay)

    def EndsAfterStart(self, other):
        return _pywrapcp.IntervalVar_EndsAfterStart(self, other)

    def EndsAfterStartWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_EndsAfterStartWithDelay(self, other, delay)

    def EndsAtEnd(self, other):
        return _pywrapcp.IntervalVar_EndsAtEnd(self, other)

    def EndsAtEndWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_EndsAtEndWithDelay(self, other, delay)

    def EndsAtStart(self, other):
        return _pywrapcp.IntervalVar_EndsAtStart(self, other)

    def EndsAtStartWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_EndsAtStartWithDelay(self, other, delay)

    def StartsAfterEnd(self, other):
        return _pywrapcp.IntervalVar_StartsAfterEnd(self, other)

    def StartsAfterEndWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_StartsAfterEndWithDelay(self, other, delay)

    def StartsAfterStart(self, other):
        return _pywrapcp.IntervalVar_StartsAfterStart(self, other)

    def StartsAfterStartWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_StartsAfterStartWithDelay(self, other, delay)

    def StartsAtEnd(self, other):
        return _pywrapcp.IntervalVar_StartsAtEnd(self, other)

    def StartsAtEndWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_StartsAtEndWithDelay(self, other, delay)

    def StartsAtStart(self, other):
        return _pywrapcp.IntervalVar_StartsAtStart(self, other)

    def StartsAtStartWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_StartsAtStartWithDelay(self, other, delay)

    def StaysInSync(self, other):
        return _pywrapcp.IntervalVar_StaysInSync(self, other)

    def StaysInSyncWithDelay(self, other, delay):
        return _pywrapcp.IntervalVar_StaysInSyncWithDelay(self, other, delay)

    def EndsAfter(self, date):
        return _pywrapcp.IntervalVar_EndsAfter(self, date)

    def EndsAt(self, date):
        return _pywrapcp.IntervalVar_EndsAt(self, date)

    def EndsBefore(self, date):
        return _pywrapcp.IntervalVar_EndsBefore(self, date)

    def StartsAfter(self, date):
        return _pywrapcp.IntervalVar_StartsAfter(self, date)

    def StartsAt(self, date):
        return _pywrapcp.IntervalVar_StartsAt(self, date)

    def StartsBefore(self, date):
        return _pywrapcp.IntervalVar_StartsBefore(self, date)

    def CrossesDate(self, date):
        return _pywrapcp.IntervalVar_CrossesDate(self, date)

    def AvoidsDate(self, date):
        return _pywrapcp.IntervalVar_AvoidsDate(self, date)

    def __repr__(self):
        return _pywrapcp.IntervalVar___repr__(self)

    def __str__(self):
        return _pywrapcp.IntervalVar___str__(self)

# Register IntervalVar in _pywrapcp:
_pywrapcp.IntervalVar_swigregister(IntervalVar)
class SequenceVar(PropagationBaseObject):
    r"""
    A sequence variable is a variable whose domain is a set of possible
    orderings of the interval variables. It allows ordering of tasks. It
    has two sets of methods: ComputePossibleFirstsAndLasts(), which
    returns the list of interval variables that can be ranked first or
    last; and RankFirst/RankNotFirst/RankLast/RankNotLast, which can be
    used to create the search decision.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")

    def DebugString(self):
        return _pywrapcp.SequenceVar_DebugString(self)

    def RankFirst(self, index):
        r"""
        Ranks the index_th interval var first of all unranked interval
        vars. After that, it will no longer be considered ranked.
        """
        return _pywrapcp.SequenceVar_RankFirst(self, index)

    def RankNotFirst(self, index):
        r"""
        Indicates that the index_th interval var will not be ranked first
        of all currently unranked interval vars.
        """
        return _pywrapcp.SequenceVar_RankNotFirst(self, index)

    def RankLast(self, index):
        r"""
        Ranks the index_th interval var first of all unranked interval
        vars. After that, it will no longer be considered ranked.
        """
        return _pywrapcp.SequenceVar_RankLast(self, index)

    def RankNotLast(self, index):
        r"""
        Indicates that the index_th interval var will not be ranked first
        of all currently unranked interval vars.
        """
        return _pywrapcp.SequenceVar_RankNotLast(self, index)

    def Interval(self, index):
        r"""Returns the index_th interval of the sequence."""
        return _pywrapcp.SequenceVar_Interval(self, index)

    def Next(self, index):
        r"""Returns the next of the index_th interval of the sequence."""
        return _pywrapcp.SequenceVar_Next(self, index)

    def Size(self):
        r"""Returns the number of interval vars in the sequence."""
        return _pywrapcp.SequenceVar_Size(self)

    def __repr__(self):
        return _pywrapcp.SequenceVar___repr__(self)

    def __str__(self):
        return _pywrapcp.SequenceVar___str__(self)

# Register SequenceVar in _pywrapcp:
_pywrapcp.SequenceVar_swigregister(SequenceVar)
class AssignmentElement(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Activate(self):
        return _pywrapcp.AssignmentElement_Activate(self)

    def Deactivate(self):
        return _pywrapcp.AssignmentElement_Deactivate(self)

    def Activated(self):
        return _pywrapcp.AssignmentElement_Activated(self)
    __swig_destroy__ = _pywrapcp.delete_AssignmentElement

# Register AssignmentElement in _pywrapcp:
_pywrapcp.AssignmentElement_swigregister(AssignmentElement)
class IntVarElement(AssignmentElement):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Var(self):
        return _pywrapcp.IntVarElement_Var(self)

    def Min(self):
        return _pywrapcp.IntVarElement_Min(self)

    def SetMin(self, m):
        return _pywrapcp.IntVarElement_SetMin(self, m)

    def Max(self):
        return _pywrapcp.IntVarElement_Max(self)

    def SetMax(self, m):
        return _pywrapcp.IntVarElement_SetMax(self, m)

    def Value(self):
        return _pywrapcp.IntVarElement_Value(self)

    def Bound(self):
        return _pywrapcp.IntVarElement_Bound(self)

    def SetRange(self, l, u):
        return _pywrapcp.IntVarElement_SetRange(self, l, u)

    def SetValue(self, v):
        return _pywrapcp.IntVarElement_SetValue(self, v)

    def __eq__(self, element):
        return _pywrapcp.IntVarElement___eq__(self, element)

    def __ne__(self, element):
        return _pywrapcp.IntVarElement___ne__(self, element)
    __swig_destroy__ = _pywrapcp.delete_IntVarElement

# Register IntVarElement in _pywrapcp:
_pywrapcp.IntVarElement_swigregister(IntVarElement)
class IntervalVarElement(AssignmentElement):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Var(self):
        return _pywrapcp.IntervalVarElement_Var(self)

    def StartMin(self):
        return _pywrapcp.IntervalVarElement_StartMin(self)

    def StartMax(self):
        return _pywrapcp.IntervalVarElement_StartMax(self)

    def StartValue(self):
        return _pywrapcp.IntervalVarElement_StartValue(self)

    def DurationMin(self):
        return _pywrapcp.IntervalVarElement_DurationMin(self)

    def DurationMax(self):
        return _pywrapcp.IntervalVarElement_DurationMax(self)

    def DurationValue(self):
        return _pywrapcp.IntervalVarElement_DurationValue(self)

    def EndMin(self):
        return _pywrapcp.IntervalVarElement_EndMin(self)

    def EndMax(self):
        return _pywrapcp.IntervalVarElement_EndMax(self)

    def EndValue(self):
        return _pywrapcp.IntervalVarElement_EndValue(self)

    def PerformedMin(self):
        return _pywrapcp.IntervalVarElement_PerformedMin(self)

    def PerformedMax(self):
        return _pywrapcp.IntervalVarElement_PerformedMax(self)

    def PerformedValue(self):
        return _pywrapcp.IntervalVarElement_PerformedValue(self)

    def SetStartMin(self, m):
        return _pywrapcp.IntervalVarElement_SetStartMin(self, m)

    def SetStartMax(self, m):
        return _pywrapcp.IntervalVarElement_SetStartMax(self, m)

    def SetStartRange(self, mi, ma):
        return _pywrapcp.IntervalVarElement_SetStartRange(self, mi, ma)

    def SetStartValue(self, v):
        return _pywrapcp.IntervalVarElement_SetStartValue(self, v)

    def SetDurationMin(self, m):
        return _pywrapcp.IntervalVarElement_SetDurationMin(self, m)

    def SetDurationMax(self, m):
        return _pywrapcp.IntervalVarElement_SetDurationMax(self, m)

    def SetDurationRange(self, mi, ma):
        return _pywrapcp.IntervalVarElement_SetDurationRange(self, mi, ma)

    def SetDurationValue(self, v):
        return _pywrapcp.IntervalVarElement_SetDurationValue(self, v)

    def SetEndMin(self, m):
        return _pywrapcp.IntervalVarElement_SetEndMin(self, m)

    def SetEndMax(self, m):
        return _pywrapcp.IntervalVarElement_SetEndMax(self, m)

    def SetEndRange(self, mi, ma):
        return _pywrapcp.IntervalVarElement_SetEndRange(self, mi, ma)

    def SetEndValue(self, v):
        return _pywrapcp.IntervalVarElement_SetEndValue(self, v)

    def SetPerformedMin(self, m):
        return _pywrapcp.IntervalVarElement_SetPerformedMin(self, m)

    def SetPerformedMax(self, m):
        return _pywrapcp.IntervalVarElement_SetPerformedMax(self, m)

    def SetPerformedRange(self, mi, ma):
        return _pywrapcp.IntervalVarElement_SetPerformedRange(self, mi, ma)

    def SetPerformedValue(self, v):
        return _pywrapcp.IntervalVarElement_SetPerformedValue(self, v)

    def __eq__(self, element):
        return _pywrapcp.IntervalVarElement___eq__(self, element)

    def __ne__(self, element):
        return _pywrapcp.IntervalVarElement___ne__(self, element)
    __swig_destroy__ = _pywrapcp.delete_IntervalVarElement

# Register IntervalVarElement in _pywrapcp:
_pywrapcp.IntervalVarElement_swigregister(IntervalVarElement)
class SequenceVarElement(AssignmentElement):
    r"""
    The SequenceVarElement stores a partial representation of ranked
    interval variables in the underlying sequence variable.
    This representation consists of three vectors:
      - the forward sequence. That is the list of interval variables
        ranked first in the sequence.  The first element of the backward
        sequence is the first interval in the sequence variable.
      - the backward sequence. That is the list of interval variables
        ranked last in the sequence. The first element of the backward
        sequence is the last interval in the sequence variable.
      - The list of unperformed interval variables.
     Furthermore, if all performed variables are ranked, then by
     convention, the forward_sequence will contain all such variables
     and the backward_sequence will be empty.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Var(self):
        return _pywrapcp.SequenceVarElement_Var(self)

    def ForwardSequence(self):
        return _pywrapcp.SequenceVarElement_ForwardSequence(self)

    def BackwardSequence(self):
        return _pywrapcp.SequenceVarElement_BackwardSequence(self)

    def Unperformed(self):
        return _pywrapcp.SequenceVarElement_Unperformed(self)

    def SetSequence(self, forward_sequence, backward_sequence, unperformed):
        return _pywrapcp.SequenceVarElement_SetSequence(self, forward_sequence, backward_sequence, unperformed)

    def SetForwardSequence(self, forward_sequence):
        return _pywrapcp.SequenceVarElement_SetForwardSequence(self, forward_sequence)

    def SetBackwardSequence(self, backward_sequence):
        return _pywrapcp.SequenceVarElement_SetBackwardSequence(self, backward_sequence)

    def SetUnperformed(self, unperformed):
        return _pywrapcp.SequenceVarElement_SetUnperformed(self, unperformed)

    def __eq__(self, element):
        return _pywrapcp.SequenceVarElement___eq__(self, element)

    def __ne__(self, element):
        return _pywrapcp.SequenceVarElement___ne__(self, element)
    __swig_destroy__ = _pywrapcp.delete_SequenceVarElement

# Register SequenceVarElement in _pywrapcp:
_pywrapcp.SequenceVarElement_swigregister(SequenceVarElement)
class Assignment(PropagationBaseObject):
    r"""
    An Assignment is a variable -> domains mapping, used
    to report solutions to the user.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Clear(self):
        return _pywrapcp.Assignment_Clear(self)

    def Empty(self):
        return _pywrapcp.Assignment_Empty(self)

    def Size(self):
        return _pywrapcp.Assignment_Size(self)

    def NumIntVars(self):
        return _pywrapcp.Assignment_NumIntVars(self)

    def NumIntervalVars(self):
        return _pywrapcp.Assignment_NumIntervalVars(self)

    def NumSequenceVars(self):
        return _pywrapcp.Assignment_NumSequenceVars(self)

    def Store(self):
        return _pywrapcp.Assignment_Store(self)

    def Restore(self):
        return _pywrapcp.Assignment_Restore(self)

    def Load(self, *args):
        r"""
        Loads an assignment from a file; does not add variables to the
        assignment (only the variables contained in the assignment are modified).
        """
        return _pywrapcp.Assignment_Load(self, *args)

    def Save(self, *args):
        r"""Saves the assignment to a file."""
        return _pywrapcp.Assignment_Save(self, *args)

    def AddObjective(self, v):
        return _pywrapcp.Assignment_AddObjective(self, v)

    def Objective(self):
        return _pywrapcp.Assignment_Objective(self)

    def HasObjective(self):
        return _pywrapcp.Assignment_HasObjective(self)

    def ObjectiveMin(self):
        return _pywrapcp.Assignment_ObjectiveMin(self)

    def ObjectiveMax(self):
        return _pywrapcp.Assignment_ObjectiveMax(self)

    def ObjectiveValue(self):
        return _pywrapcp.Assignment_ObjectiveValue(self)

    def ObjectiveBound(self):
        return _pywrapcp.Assignment_ObjectiveBound(self)

    def SetObjectiveMin(self, m):
        return _pywrapcp.Assignment_SetObjectiveMin(self, m)

    def SetObjectiveMax(self, m):
        return _pywrapcp.Assignment_SetObjectiveMax(self, m)

    def SetObjectiveValue(self, value):
        return _pywrapcp.Assignment_SetObjectiveValue(self, value)

    def SetObjectiveRange(self, l, u):
        return _pywrapcp.Assignment_SetObjectiveRange(self, l, u)

    def Min(self, var):
        return _pywrapcp.Assignment_Min(self, var)

    def Max(self, var):
        return _pywrapcp.Assignment_Max(self, var)

    def Value(self, var):
        return _pywrapcp.Assignment_Value(self, var)

    def Bound(self, var):
        return _pywrapcp.Assignment_Bound(self, var)

    def SetMin(self, var, m):
        return _pywrapcp.Assignment_SetMin(self, var, m)

    def SetMax(self, var, m):
        return _pywrapcp.Assignment_SetMax(self, var, m)

    def SetRange(self, var, l, u):
        return _pywrapcp.Assignment_SetRange(self, var, l, u)

    def SetValue(self, var, value):
        return _pywrapcp.Assignment_SetValue(self, var, value)

    def StartMin(self, var):
        return _pywrapcp.Assignment_StartMin(self, var)

    def StartMax(self, var):
        return _pywrapcp.Assignment_StartMax(self, var)

    def StartValue(self, var):
        return _pywrapcp.Assignment_StartValue(self, var)

    def DurationMin(self, var):
        return _pywrapcp.Assignment_DurationMin(self, var)

    def DurationMax(self, var):
        return _pywrapcp.Assignment_DurationMax(self, var)

    def DurationValue(self, var):
        return _pywrapcp.Assignment_DurationValue(self, var)

    def EndMin(self, var):
        return _pywrapcp.Assignment_EndMin(self, var)

    def EndMax(self, var):
        return _pywrapcp.Assignment_EndMax(self, var)

    def EndValue(self, var):
        return _pywrapcp.Assignment_EndValue(self, var)

    def PerformedMin(self, var):
        return _pywrapcp.Assignment_PerformedMin(self, var)

    def PerformedMax(self, var):
        return _pywrapcp.Assignment_PerformedMax(self, var)

    def PerformedValue(self, var):
        return _pywrapcp.Assignment_PerformedValue(self, var)

    def SetStartMin(self, var, m):
        return _pywrapcp.Assignment_SetStartMin(self, var, m)

    def SetStartMax(self, var, m):
        return _pywrapcp.Assignment_SetStartMax(self, var, m)

    def SetStartRange(self, var, mi, ma):
        return _pywrapcp.Assignment_SetStartRange(self, var, mi, ma)

    def SetStartValue(self, var, value):
        return _pywrapcp.Assignment_SetStartValue(self, var, value)

    def SetDurationMin(self, var, m):
        return _pywrapcp.Assignment_SetDurationMin(self, var, m)

    def SetDurationMax(self, var, m):
        return _pywrapcp.Assignment_SetDurationMax(self, var, m)

    def SetDurationRange(self, var, mi, ma):
        return _pywrapcp.Assignment_SetDurationRange(self, var, mi, ma)

    def SetDurationValue(self, var, value):
        return _pywrapcp.Assignment_SetDurationValue(self, var, value)

    def SetEndMin(self, var, m):
        return _pywrapcp.Assignment_SetEndMin(self, var, m)

    def SetEndMax(self, var, m):
        return _pywrapcp.Assignment_SetEndMax(self, var, m)

    def SetEndRange(self, var, mi, ma):
        return _pywrapcp.Assignment_SetEndRange(self, var, mi, ma)

    def SetEndValue(self, var, value):
        return _pywrapcp.Assignment_SetEndValue(self, var, value)

    def SetPerformedMin(self, var, m):
        return _pywrapcp.Assignment_SetPerformedMin(self, var, m)

    def SetPerformedMax(self, var, m):
        return _pywrapcp.Assignment_SetPerformedMax(self, var, m)

    def SetPerformedRange(self, var, mi, ma):
        return _pywrapcp.Assignment_SetPerformedRange(self, var, mi, ma)

    def SetPerformedValue(self, var, value):
        return _pywrapcp.Assignment_SetPerformedValue(self, var, value)

    def Add(self, *args):
        return _pywrapcp.Assignment_Add(self, *args)

    def ForwardSequence(self, var):
        return _pywrapcp.Assignment_ForwardSequence(self, var)

    def BackwardSequence(self, var):
        return _pywrapcp.Assignment_BackwardSequence(self, var)

    def Unperformed(self, var):
        return _pywrapcp.Assignment_Unperformed(self, var)

    def SetSequence(self, var, forward_sequence, backward_sequence, unperformed):
        return _pywrapcp.Assignment_SetSequence(self, var, forward_sequence, backward_sequence, unperformed)

    def SetForwardSequence(self, var, forward_sequence):
        return _pywrapcp.Assignment_SetForwardSequence(self, var, forward_sequence)

    def SetBackwardSequence(self, var, backward_sequence):
        return _pywrapcp.Assignment_SetBackwardSequence(self, var, backward_sequence)

    def SetUnperformed(self, var, unperformed):
        return _pywrapcp.Assignment_SetUnperformed(self, var, unperformed)

    def Activate(self, *args):
        return _pywrapcp.Assignment_Activate(self, *args)

    def Deactivate(self, *args):
        return _pywrapcp.Assignment_Deactivate(self, *args)

    def Activated(self, *args):
        return _pywrapcp.Assignment_Activated(self, *args)

    def DebugString(self):
        return _pywrapcp.Assignment_DebugString(self)

    def IntVarContainer(self):
        return _pywrapcp.Assignment_IntVarContainer(self)

    def MutableIntVarContainer(self):
        return _pywrapcp.Assignment_MutableIntVarContainer(self)

    def IntervalVarContainer(self):
        return _pywrapcp.Assignment_IntervalVarContainer(self)

    def MutableIntervalVarContainer(self):
        return _pywrapcp.Assignment_MutableIntervalVarContainer(self)

    def SequenceVarContainer(self):
        return _pywrapcp.Assignment_SequenceVarContainer(self)

    def MutableSequenceVarContainer(self):
        return _pywrapcp.Assignment_MutableSequenceVarContainer(self)

    def __eq__(self, assignment):
        return _pywrapcp.Assignment___eq__(self, assignment)

    def __ne__(self, assignment):
        return _pywrapcp.Assignment___ne__(self, assignment)

# Register Assignment in _pywrapcp:
_pywrapcp.Assignment_swigregister(Assignment)

def __lshift__(*args):
    return _pywrapcp.__lshift__(*args)
class Pack(Constraint):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def AddWeightedSumLessOrEqualConstantDimension(self, *args):
        r"""
        *Overload 1:*
        Dimensions are additional constraints than can restrict what is
        possible with the pack constraint. It can be used to set capacity
        limits, to count objects per bin, to compute unassigned
        penalties...
        This dimension imposes that for all bins b, the weighted sum
        (weights[i]) of all objects i assigned to 'b' is less or equal
        'bounds[b]'.

        |

        *Overload 2:*
        This dimension imposes that for all bins b, the weighted sum
        (weights->Run(i)) of all objects i assigned to 'b' is less or
        equal to 'bounds[b]'. Ownership of the callback is transferred to
        the pack constraint.

        |

        *Overload 3:*
        This dimension imposes that for all bins b, the weighted sum
        (weights->Run(i, b) of all objects i assigned to 'b' is less or
        equal to 'bounds[b]'. Ownership of the callback is transferred to
        the pack constraint.
        """
        return _pywrapcp.Pack_AddWeightedSumLessOrEqualConstantDimension(self, *args)

    def AddWeightedSumEqualVarDimension(self, *args):
        r"""
        *Overload 1:*
        This dimension imposes that for all bins b, the weighted sum
        (weights[i]) of all objects i assigned to 'b' is equal to loads[b].

        |

        *Overload 2:*
        This dimension imposes that for all bins b, the weighted sum
        (weights->Run(i, b)) of all objects i assigned to 'b' is equal to
        loads[b].
        """
        return _pywrapcp.Pack_AddWeightedSumEqualVarDimension(self, *args)

    def AddSumVariableWeightsLessOrEqualConstantDimension(self, usage, capacity):
        r"""
        This dimension imposes:
        forall b in bins,
           sum (i in items: usage[i] * is_assigned(i, b)) <= capacity[b]
        where is_assigned(i, b) is true if and only if item i is assigned
        to the bin b.

        This can be used to model shapes of items by linking variables of
        the same item on parallel dimensions with an allowed assignment
        constraint.
        """
        return _pywrapcp.Pack_AddSumVariableWeightsLessOrEqualConstantDimension(self, usage, capacity)

    def AddWeightedSumOfAssignedDimension(self, weights, cost_var):
        r"""
        This dimension enforces that cost_var == sum of weights[i] for
        all objects 'i' assigned to a bin.
        """
        return _pywrapcp.Pack_AddWeightedSumOfAssignedDimension(self, weights, cost_var)

    def AddCountUsedBinDimension(self, count_var):
        r"""
        This dimension links 'count_var' to the actual number of bins used in the
        pack.
        """
        return _pywrapcp.Pack_AddCountUsedBinDimension(self, count_var)

    def AddCountAssignedItemsDimension(self, count_var):
        r"""
        This dimension links 'count_var' to the actual number of items
        assigned to a bin in the pack.
        """
        return _pywrapcp.Pack_AddCountAssignedItemsDimension(self, count_var)

    def Post(self):
        return _pywrapcp.Pack_Post(self)

    def InitialPropagateWrapper(self):
        return _pywrapcp.Pack_InitialPropagateWrapper(self)

    def DebugString(self):
        return _pywrapcp.Pack_DebugString(self)

# Register Pack in _pywrapcp:
_pywrapcp.Pack_swigregister(Pack)
class DisjunctiveConstraint(Constraint):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr

    def SequenceVar(self):
        r"""Creates a sequence variable from the constraint."""
        return _pywrapcp.DisjunctiveConstraint_SequenceVar(self)

    def SetTransitionTime(self, transition_time):
        r"""
        Add a transition time between intervals.  It forces the distance between
        the end of interval a and start of interval b that follows it to be at
        least transition_time(a, b). This function must always return
        a positive or null value.
        """
        return _pywrapcp.DisjunctiveConstraint_SetTransitionTime(self, transition_time)

    def TransitionTime(self, before_index, after_index):
        return _pywrapcp.DisjunctiveConstraint_TransitionTime(self, before_index, after_index)

# Register DisjunctiveConstraint in _pywrapcp:
_pywrapcp.DisjunctiveConstraint_swigregister(DisjunctiveConstraint)
class RevInteger(object):
    r"""
    This class adds reversibility to a POD type.
    It contains the stamp optimization. i.e. the SaveValue call is done
    only once per node of the search tree.  Please note that actual
    stamps always starts at 1, thus an initial value of 0 will always
    trigger the first SaveValue.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, val):
        _pywrapcp.RevInteger_swiginit(self, _pywrapcp.new_RevInteger(val))

    def Value(self):
        return _pywrapcp.RevInteger_Value(self)

    def SetValue(self, s, val):
        return _pywrapcp.RevInteger_SetValue(self, s, val)
    __swig_destroy__ = _pywrapcp.delete_RevInteger

# Register RevInteger in _pywrapcp:
_pywrapcp.RevInteger_swigregister(RevInteger)
class NumericalRevInteger(RevInteger):
    r"""Subclass of Rev<T> which adds numerical operations."""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, val):
        _pywrapcp.NumericalRevInteger_swiginit(self, _pywrapcp.new_NumericalRevInteger(val))

    def Add(self, s, to_add):
        return _pywrapcp.NumericalRevInteger_Add(self, s, to_add)

    def Incr(self, s):
        return _pywrapcp.NumericalRevInteger_Incr(self, s)

    def Decr(self, s):
        return _pywrapcp.NumericalRevInteger_Decr(self, s)
    __swig_destroy__ = _pywrapcp.delete_NumericalRevInteger

# Register NumericalRevInteger in _pywrapcp:
_pywrapcp.NumericalRevInteger_swigregister(NumericalRevInteger)
class RevBool(object):
    r"""
    This class adds reversibility to a POD type.
    It contains the stamp optimization. i.e. the SaveValue call is done
    only once per node of the search tree.  Please note that actual
    stamps always starts at 1, thus an initial value of 0 will always
    trigger the first SaveValue.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, val):
        _pywrapcp.RevBool_swiginit(self, _pywrapcp.new_RevBool(val))

    def Value(self):
        return _pywrapcp.RevBool_Value(self)

    def SetValue(self, s, val):
        return _pywrapcp.RevBool_SetValue(self, s, val)
    __swig_destroy__ = _pywrapcp.delete_RevBool

# Register RevBool in _pywrapcp:
_pywrapcp.RevBool_swigregister(RevBool)
class IntVarContainer(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Contains(self, var):
        return _pywrapcp.IntVarContainer_Contains(self, var)

    def Element(self, index):
        return _pywrapcp.IntVarContainer_Element(self, index)

    def Size(self):
        return _pywrapcp.IntVarContainer_Size(self)

    def Store(self):
        return _pywrapcp.IntVarContainer_Store(self)

    def Restore(self):
        return _pywrapcp.IntVarContainer_Restore(self)

    def __eq__(self, container):
        r"""
        Returns true if this and 'container' both represent the same V* -> E map.
        Runs in linear time; requires that the == operator on the type E is well
        defined.
        """
        return _pywrapcp.IntVarContainer___eq__(self, container)

    def __ne__(self, container):
        return _pywrapcp.IntVarContainer___ne__(self, container)
    __swig_destroy__ = _pywrapcp.delete_IntVarContainer

# Register IntVarContainer in _pywrapcp:
_pywrapcp.IntVarContainer_swigregister(IntVarContainer)
class IntervalVarContainer(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Contains(self, var):
        return _pywrapcp.IntervalVarContainer_Contains(self, var)

    def Element(self, index):
        return _pywrapcp.IntervalVarContainer_Element(self, index)

    def Size(self):
        return _pywrapcp.IntervalVarContainer_Size(self)

    def Store(self):
        return _pywrapcp.IntervalVarContainer_Store(self)

    def Restore(self):
        return _pywrapcp.IntervalVarContainer_Restore(self)

    def __eq__(self, container):
        r"""
        Returns true if this and 'container' both represent the same V* -> E map.
        Runs in linear time; requires that the == operator on the type E is well
        defined.
        """
        return _pywrapcp.IntervalVarContainer___eq__(self, container)

    def __ne__(self, container):
        return _pywrapcp.IntervalVarContainer___ne__(self, container)
    __swig_destroy__ = _pywrapcp.delete_IntervalVarContainer

# Register IntervalVarContainer in _pywrapcp:
_pywrapcp.IntervalVarContainer_swigregister(IntervalVarContainer)
class SequenceVarContainer(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr

    def Contains(self, var):
        return _pywrapcp.SequenceVarContainer_Contains(self, var)

    def Element(self, index):
        return _pywrapcp.SequenceVarContainer_Element(self, index)

    def Size(self):
        return _pywrapcp.SequenceVarContainer_Size(self)

    def Store(self):
        return _pywrapcp.SequenceVarContainer_Store(self)

    def Restore(self):
        return _pywrapcp.SequenceVarContainer_Restore(self)

    def __eq__(self, container):
        r"""
        Returns true if this and 'container' both represent the same V* -> E map.
        Runs in linear time; requires that the == operator on the type E is well
        defined.
        """
        return _pywrapcp.SequenceVarContainer___eq__(self, container)

    def __ne__(self, container):
        return _pywrapcp.SequenceVarContainer___ne__(self, container)
    __swig_destroy__ = _pywrapcp.delete_SequenceVarContainer

# Register SequenceVarContainer in _pywrapcp:
_pywrapcp.SequenceVarContainer_swigregister(SequenceVarContainer)
class LocalSearchOperator(BaseObject):
    r"""
    The base class for all local search operators.

    A local search operator is an object that defines the neighborhood of a
    solution. In other words, a neighborhood is the set of solutions which can
    be reached from a given solution using an operator.

    The behavior of the LocalSearchOperator class is similar to iterators.
    The operator is synchronized with an assignment (gives the
    current values of the variables); this is done in the Start() method.

    Then one can iterate over the neighbors using the MakeNextNeighbor method.
    This method returns an assignment which represents the incremental changes
    to the current solution. It also returns a second assignment representing
    the changes to the last solution defined by the neighborhood operator; this
    assignment is empty if the neighborhood operator cannot track this
    information.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr

    def NextNeighbor(self, delta, deltadelta):
        return _pywrapcp.LocalSearchOperator_NextNeighbor(self, delta, deltadelta)

    def Start(self, assignment):
        return _pywrapcp.LocalSearchOperator_Start(self, assignment)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_LocalSearchOperator(self)
        return weakref.proxy(self)

# Register LocalSearchOperator in _pywrapcp:
_pywrapcp.LocalSearchOperator_swigregister(LocalSearchOperator)
class IntVarLocalSearchOperator(LocalSearchOperator):
    r"""
    Specialization of LocalSearchOperator built from an array of IntVars
    which specifies the scope of the operator.
    This class also takes care of storing current variable values in Start(),
    keeps track of changes done by the operator and builds the delta.
    The Deactivate() method can be used to perform Large Neighborhood Search.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, vars, keep_inverse_values=False):
        if self.__class__ == IntVarLocalSearchOperator:
            _self = None
        else:
            _self = self
        _pywrapcp.IntVarLocalSearchOperator_swiginit(self, _pywrapcp.new_IntVarLocalSearchOperator(_self, vars, keep_inverse_values))
    __swig_destroy__ = _pywrapcp.delete_IntVarLocalSearchOperator

    def Start(self, assignment):
        r"""
        This method should not be overridden. Override OnStart() instead which is
        called before exiting this method.
        """
        return _pywrapcp.IntVarLocalSearchOperator_Start(self, assignment)

    def IsIncremental(self):
        return _pywrapcp.IntVarLocalSearchOperator_IsIncremental(self)

    def Size(self):
        return _pywrapcp.IntVarLocalSearchOperator_Size(self)

    def Value(self, index):
        r"""
        Returns the value in the current assignment of the variable of given
        index.
        """
        return _pywrapcp.IntVarLocalSearchOperator_Value(self, index)

    def Var(self, index):
        r"""Returns the variable of given index."""
        return _pywrapcp.IntVarLocalSearchOperator_Var(self, index)

    def OldValue(self, index):
        return _pywrapcp.IntVarLocalSearchOperator_OldValue(self, index)

    def PrevValue(self, index):
        return _pywrapcp.IntVarLocalSearchOperator_PrevValue(self, index)

    def SetValue(self, index, value):
        return _pywrapcp.IntVarLocalSearchOperator_SetValue(self, index, value)

    def Activated(self, index):
        return _pywrapcp.IntVarLocalSearchOperator_Activated(self, index)

    def Activate(self, index):
        return _pywrapcp.IntVarLocalSearchOperator_Activate(self, index)

    def Deactivate(self, index):
        return _pywrapcp.IntVarLocalSearchOperator_Deactivate(self, index)

    def AddVars(self, vars):
        return _pywrapcp.IntVarLocalSearchOperator_AddVars(self, vars)

    def OnStart(self):
        r"""
        Called by Start() after synchronizing the operator with the current
        assignment. Should be overridden instead of Start() to avoid calling
        IntVarLocalSearchOperator::Start explicitly.
        """
        return _pywrapcp.IntVarLocalSearchOperator_OnStart(self)

    def NextNeighbor(self, delta, deltadelta):
        r"""
        OnStart() should really be protected, but then SWIG doesn't see it. So we
        make it public, but only subclasses should access to it (to override it).
        Redefines MakeNextNeighbor to export a simpler interface. The calls to
        ApplyChanges() and RevertChanges() are factored in this method, hiding
        both delta and deltadelta from subclasses which only need to override
        MakeOneNeighbor().
        Therefore this method should not be overridden. Override MakeOneNeighbor()
        instead.
        """
        return _pywrapcp.IntVarLocalSearchOperator_NextNeighbor(self, delta, deltadelta)

    def OneNeighbor(self):
        r"""
        Creates a new neighbor. It returns false when the neighborhood is
        completely explored.
        MakeNextNeighbor() in a subclass of IntVarLocalSearchOperator.
        """
        return _pywrapcp.IntVarLocalSearchOperator_OneNeighbor(self)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_IntVarLocalSearchOperator(self)
        return weakref.proxy(self)

# Register IntVarLocalSearchOperator in _pywrapcp:
_pywrapcp.IntVarLocalSearchOperator_swigregister(IntVarLocalSearchOperator)
class BaseLns(IntVarLocalSearchOperator):
    r"""
    This is the base class for building an Lns operator. An Lns fragment is a
    collection of variables which will be relaxed. Fragments are built with
    NextFragment(), which returns false if there are no more fragments to build.
    Optionally one can override InitFragments, which is called from
    LocalSearchOperator::Start to initialize fragment data.

    Here's a sample relaxing one variable at a time:

    class OneVarLns : public BaseLns {
     public:
      OneVarLns(const std::vector<IntVar*>& vars) : BaseLns(vars), index_(0) {}
      virtual ~OneVarLns() {}
      virtual void InitFragments() { index_ = 0; }
      virtual bool NextFragment() {
        const int size = Size();
        if (index_ < size) {
          AppendToFragment(index_);
          ++index_;
          return true;
        } else {
          return false;
        }
      }

     private:
      int index_;
    };
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, vars):
        if self.__class__ == BaseLns:
            _self = None
        else:
            _self = self
        _pywrapcp.BaseLns_swiginit(self, _pywrapcp.new_BaseLns(_self, vars))
    __swig_destroy__ = _pywrapcp.delete_BaseLns

    def InitFragments(self):
        return _pywrapcp.BaseLns_InitFragments(self)

    def NextFragment(self):
        return _pywrapcp.BaseLns_NextFragment(self)

    def AppendToFragment(self, index):
        return _pywrapcp.BaseLns_AppendToFragment(self, index)

    def FragmentSize(self):
        return _pywrapcp.BaseLns_FragmentSize(self)

    def __getitem__(self, index):
        return _pywrapcp.BaseLns___getitem__(self, index)

    def __len__(self):
        return _pywrapcp.BaseLns___len__(self)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_BaseLns(self)
        return weakref.proxy(self)

# Register BaseLns in _pywrapcp:
_pywrapcp.BaseLns_swigregister(BaseLns)
class ChangeValue(IntVarLocalSearchOperator):
    r"""
    Defines operators which change the value of variables;
    each neighbor corresponds to *one* modified variable.
    Sub-classes have to define ModifyValue which determines what the new
    variable value is going to be (given the current value and the variable).
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, vars):
        if self.__class__ == ChangeValue:
            _self = None
        else:
            _self = self
        _pywrapcp.ChangeValue_swiginit(self, _pywrapcp.new_ChangeValue(_self, vars))
    __swig_destroy__ = _pywrapcp.delete_ChangeValue

    def ModifyValue(self, index, value):
        return _pywrapcp.ChangeValue_ModifyValue(self, index, value)

    def OneNeighbor(self):
        r"""This method should not be overridden. Override ModifyValue() instead."""
        return _pywrapcp.ChangeValue_OneNeighbor(self)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_ChangeValue(self)
        return weakref.proxy(self)

# Register ChangeValue in _pywrapcp:
_pywrapcp.ChangeValue_swigregister(ChangeValue)
class LocalSearchFilter(BaseObject):
    r"""
    Local Search Filters are used for fast neighbor pruning.
    Filtering a move is done in several phases:
    - in the Relax phase, filters determine which parts of their internals
      will be changed by the candidate, and modify intermediary State
    - in the Accept phase, filters check that the candidate is feasible,
    - if the Accept phase succeeds, the solver may decide to trigger a
      Synchronize phase that makes filters change their internal representation
      to the last candidate,
    - otherwise (Accept fails or the solver does not want to synchronize),
      a Revert phase makes filters erase any intermediary State generated by the
      Relax and Accept phases.
    A given filter has phases called with the following pattern:
    (Relax.Accept.Synchronize | Relax.Accept.Revert | Relax.Revert)*.
    Filters's Revert() is always called in the reverse order their Accept() was
    called, to allow late filters to use state done/undone by early filters'
    Accept()/Revert().
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr

    def Accept(self, delta, deltadelta, objective_min, objective_max):
        r"""
        Accepts a "delta" given the assignment with which the filter has been
        synchronized; the delta holds the variables which have been modified and
        their new value.
        If the filter represents a part of the global objective, its contribution
        must be between objective_min and objective_max.
        Sample: supposing one wants to maintain a[0,1] + b[0,1] <= 1,
        for the assignment (a,1), (b,0), the delta (b,1) will be rejected
        but the delta (a,0) will be accepted.
        TODO(user): Remove arguments when there are no more need for those.
        """
        return _pywrapcp.LocalSearchFilter_Accept(self, delta, deltadelta, objective_min, objective_max)

    def IsIncremental(self):
        return _pywrapcp.LocalSearchFilter_IsIncremental(self)

    def Synchronize(self, assignment, delta):
        r"""
        Synchronizes the filter with the current solution, delta being the
        difference with the solution passed to the previous call to Synchronize()
        or IncrementalSynchronize(). 'delta' can be used to incrementally
        synchronizing the filter with the new solution by only considering the
        changes in delta.
        """
        return _pywrapcp.LocalSearchFilter_Synchronize(self, assignment, delta)
    __swig_destroy__ = _pywrapcp.delete_LocalSearchFilter

# Register LocalSearchFilter in _pywrapcp:
_pywrapcp.LocalSearchFilter_swigregister(LocalSearchFilter)
class LocalSearchFilterManager(BaseObject):
    r"""
    Filter manager: when a move is made, filters are executed to decide whether
    the solution is feasible and compute parts of the new cost. This class
    schedules filter execution and composes costs as a sum.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def DebugString(self):
        return _pywrapcp.LocalSearchFilterManager_DebugString(self)

    def __init__(self, *args):
        _pywrapcp.LocalSearchFilterManager_swiginit(self, _pywrapcp.new_LocalSearchFilterManager(*args))

    def Accept(self, monitor, delta, deltadelta, objective_min, objective_max):
        r"""
        Returns true iff all filters return true, and the sum of their accepted
        objectives is between objective_min and objective_max.
        The monitor has its Begin/EndFiltering events triggered.
        """
        return _pywrapcp.LocalSearchFilterManager_Accept(self, monitor, delta, deltadelta, objective_min, objective_max)

    def Synchronize(self, assignment, delta):
        r"""Synchronizes all filters to assignment."""
        return _pywrapcp.LocalSearchFilterManager_Synchronize(self, assignment, delta)
    __swig_destroy__ = _pywrapcp.delete_LocalSearchFilterManager

# Register LocalSearchFilterManager in _pywrapcp:
_pywrapcp.LocalSearchFilterManager_swigregister(LocalSearchFilterManager)
class IntVarLocalSearchFilter(LocalSearchFilter):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, vars):
        if self.__class__ == IntVarLocalSearchFilter:
            _self = None
        else:
            _self = self
        _pywrapcp.IntVarLocalSearchFilter_swiginit(self, _pywrapcp.new_IntVarLocalSearchFilter(_self, vars))
    __swig_destroy__ = _pywrapcp.delete_IntVarLocalSearchFilter

    def Synchronize(self, assignment, delta):
        r"""
        This method should not be overridden. Override OnSynchronize() instead
        which is called before exiting this method.
        """
        return _pywrapcp.IntVarLocalSearchFilter_Synchronize(self, assignment, delta)

    def Size(self):
        return _pywrapcp.IntVarLocalSearchFilter_Size(self)

    def Value(self, index):
        return _pywrapcp.IntVarLocalSearchFilter_Value(self, index)

    def IndexFromVar(self, var):
        return _pywrapcp.IntVarLocalSearchFilter_IndexFromVar(self, var)
    def __disown__(self):
        self.this.disown()
        _pywrapcp.disown_IntVarLocalSearchFilter(self)
        return weakref.proxy(self)

# Register IntVarLocalSearchFilter in _pywrapcp:
_pywrapcp.IntVarLocalSearchFilter_swigregister(IntVarLocalSearchFilter)
class BooleanVar(IntVar):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr

    def Min(self):
        return _pywrapcp.BooleanVar_Min(self)

    def SetMin(self, m):
        return _pywrapcp.BooleanVar_SetMin(self, m)

    def Max(self):
        return _pywrapcp.BooleanVar_Max(self)

    def SetMax(self, m):
        return _pywrapcp.BooleanVar_SetMax(self, m)

    def SetRange(self, mi, ma):
        return _pywrapcp.BooleanVar_SetRange(self, mi, ma)

    def Bound(self):
        return _pywrapcp.BooleanVar_Bound(self)

    def Value(self):
        return _pywrapcp.BooleanVar_Value(self)

    def RemoveValue(self, v):
        return _pywrapcp.BooleanVar_RemoveValue(self, v)

    def RemoveInterval(self, l, u):
        return _pywrapcp.BooleanVar_RemoveInterval(self, l, u)

    def WhenBound(self, d):
        return _pywrapcp.BooleanVar_WhenBound(self, d)

    def WhenRange(self, d):
        return _pywrapcp.BooleanVar_WhenRange(self, d)

    def WhenDomain(self, d):
        return _pywrapcp.BooleanVar_WhenDomain(self, d)

    def Size(self):
        return _pywrapcp.BooleanVar_Size(self)

    def Contains(self, v):
        return _pywrapcp.BooleanVar_Contains(self, v)

    def HoleIteratorAux(self, reversible):
        return _pywrapcp.BooleanVar_HoleIteratorAux(self, reversible)

    def DomainIteratorAux(self, reversible):
        return _pywrapcp.BooleanVar_DomainIteratorAux(self, reversible)

    def DebugString(self):
        return _pywrapcp.BooleanVar_DebugString(self)

# Register BooleanVar in _pywrapcp:
_pywrapcp.BooleanVar_swigregister(BooleanVar)

class PyDecision(Decision):
  def ApplyWrapper(self, solver):
    try:
       self.Apply(solver)
    except Exception as e:
      if 'CP Solver fail' in str(e):
        solver.ShouldFail()
      else:
        raise

  def RefuteWrapper(self, solver):
    try:
       self.Refute(solver)
    except Exception as e:
      if 'CP Solver fail' in str(e):
        solver.ShouldFail()
      else:
        raise

  def DebugString(self):
    return "PyDecision"


class PyDecisionBuilder(DecisionBuilder):
  def NextWrapper(self, solver):
    try:
      return self.Next(solver)
    except Exception as e:
      if 'CP Solver fail' in str(e):
        return solver.FailDecision()
      else:
        raise

  def DebugString(self):
    return "PyDecisionBuilder"


class PyDemon(Demon):
  def RunWrapper(self, solver):
    try:
      self.Run(solver)
    except Exception as e:
      if 'CP Solver fail' in str(e):
        solver.ShouldFail()
      else:
        raise

  def DebugString(self):
    return "PyDemon"


class PyConstraintDemon(PyDemon):
  def __init__(self, ct, method, delayed, *args):
    super().__init__()
    self.__constraint = ct
    self.__method = method
    self.__delayed = delayed
    self.__args = args

  def Run(self, solver):
    self.__method(self.__constraint, *self.__args)

  def Priority(self):
    return Solver.DELAYED_PRIORITY if self.__delayed else Solver.NORMAL_PRIORITY

  def DebugString(self):
    return 'PyConstraintDemon'


class PyConstraint(Constraint):
  def __init__(self, solver):
    super().__init__(solver)
    self.__demons = []

  def Demon(self, method, *args):
    demon = PyConstraintDemon(self, method, False, *args)
    self.__demons.append(demon)
    return demon

  def DelayedDemon(self, method, *args):
    demon = PyConstraintDemon(self, method, True, *args)
    self.__demons.append(demon)
    return demon

  def InitialPropagateDemon(self):
    return self.solver().ConstraintInitialPropagateCallback(self)

  def DelayedInitialPropagateDemon(self):
    return self.solver().DelayedConstraintInitialPropagateCallback(self)

  def InitialPropagateWrapper(self):
    try:
      self.InitialPropagate()
    except Exception as e:
      if 'CP Solver fail' in str(e):
        self.solver().ShouldFail()
      else:
        raise

  def DebugString(self):
    return "PyConstraint"

class RoutingIndexManager(object):
    r"""
    Manager for any NodeIndex <-> variable index conversion. The routing solver
    uses variable indices internally and through its API. These variable indices
    are tricky to manage directly because one Node can correspond to a multitude
    of variables, depending on the number of times they appear in the model, and
    if they're used as start and/or end points. This class aims to simplify
    variable index usage, allowing users to use NodeIndex instead.

    Usage:

      .. code-block:: c++

          auto starts_ends = ...;  /// These are NodeIndex.
          RoutingIndexManager manager(10, 4, starts_ends);  // 10 nodes, 4 vehicles.
          RoutingModel model(manager);

    Then, use 'manager.NodeToIndex(node)' whenever model requires a variable
    index.

    Note: the mapping between node indices and variables indices is subject to
    change so no assumption should be made on it. The only guarantee is that
    indices range between 0 and n-1, where n = number of vehicles * 2 (for start
    and end nodes) + number of non-start or end nodes.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, *args):
        r"""
        Creates a NodeIndex to variable index mapping for a problem containing
        'num_nodes', 'num_vehicles' and the given starts and ends for each
        vehicle. If used, any start/end arrays have to have exactly 'num_vehicles'
        elements.
        """
        _pywrapcp.RoutingIndexManager_swiginit(self, _pywrapcp.new_RoutingIndexManager(*args))

    def GetNumberOfNodes(self):
        return _pywrapcp.RoutingIndexManager_GetNumberOfNodes(self)

    def GetNumberOfVehicles(self):
        return _pywrapcp.RoutingIndexManager_GetNumberOfVehicles(self)

    def GetNumberOfIndices(self):
        return _pywrapcp.RoutingIndexManager_GetNumberOfIndices(self)

    def GetStartIndex(self, vehicle):
        return _pywrapcp.RoutingIndexManager_GetStartIndex(self, vehicle)

    def GetEndIndex(self, vehicle):
        return _pywrapcp.RoutingIndexManager_GetEndIndex(self, vehicle)

    def NodeToIndex(self, node):
        return _pywrapcp.RoutingIndexManager_NodeToIndex(self, node)

    def IndexToNode(self, index):
        return _pywrapcp.RoutingIndexManager_IndexToNode(self, index)
    __swig_destroy__ = _pywrapcp.delete_RoutingIndexManager

# Register RoutingIndexManager in _pywrapcp:
_pywrapcp.RoutingIndexManager_swigregister(RoutingIndexManager)

def DefaultRoutingModelParameters():
    return _pywrapcp.DefaultRoutingModelParameters()

def DefaultRoutingSearchParameters():
    return _pywrapcp.DefaultRoutingSearchParameters()

def FindErrorInRoutingSearchParameters(search_parameters):
    r"""
    Returns an empty std::string if the routing search parameters are valid, and
    a non-empty, human readable error description if they're not.
    """
    return _pywrapcp.FindErrorInRoutingSearchParameters(search_parameters)
BOOL_UNSPECIFIED = _pywrapcp.BOOL_UNSPECIFIED
BOOL_FALSE = _pywrapcp.BOOL_FALSE
BOOL_TRUE = _pywrapcp.BOOL_TRUE
class FirstSolutionStrategy(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self):
        _pywrapcp.FirstSolutionStrategy_swiginit(self, _pywrapcp.new_FirstSolutionStrategy())
    __swig_destroy__ = _pywrapcp.delete_FirstSolutionStrategy

# Register FirstSolutionStrategy in _pywrapcp:
_pywrapcp.FirstSolutionStrategy_swigregister(FirstSolutionStrategy)
class LocalSearchMetaheuristic(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self):
        _pywrapcp.LocalSearchMetaheuristic_swiginit(self, _pywrapcp.new_LocalSearchMetaheuristic())
    __swig_destroy__ = _pywrapcp.delete_LocalSearchMetaheuristic

# Register LocalSearchMetaheuristic in _pywrapcp:
_pywrapcp.LocalSearchMetaheuristic_swigregister(LocalSearchMetaheuristic)
class RoutingSearchStatus(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self):
        _pywrapcp.RoutingSearchStatus_swiginit(self, _pywrapcp.new_RoutingSearchStatus())
    __swig_destroy__ = _pywrapcp.delete_RoutingSearchStatus

# Register RoutingSearchStatus in _pywrapcp:
_pywrapcp.RoutingSearchStatus_swigregister(RoutingSearchStatus)
class PathsMetadata(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, manager):
        _pywrapcp.PathsMetadata_swiginit(self, _pywrapcp.new_PathsMetadata(manager))

    def IsStart(self, node):
        return _pywrapcp.PathsMetadata_IsStart(self, node)

    def IsEnd(self, node):
        return _pywrapcp.PathsMetadata_IsEnd(self, node)

    def GetPath(self, start_or_end_node):
        return _pywrapcp.PathsMetadata_GetPath(self, start_or_end_node)

    def NumPaths(self):
        return _pywrapcp.PathsMetadata_NumPaths(self)

    def Paths(self):
        return _pywrapcp.PathsMetadata_Paths(self)

    def Starts(self):
        return _pywrapcp.PathsMetadata_Starts(self)

    def Start(self, path):
        return _pywrapcp.PathsMetadata_Start(self, path)

    def End(self, path):
        return _pywrapcp.PathsMetadata_End(self, path)

    def Ends(self):
        return _pywrapcp.PathsMetadata_Ends(self)
    __swig_destroy__ = _pywrapcp.delete_PathsMetadata

# Register PathsMetadata in _pywrapcp:
_pywrapcp.PathsMetadata_swigregister(PathsMetadata)
class RoutingModel(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    PICKUP_AND_DELIVERY_NO_ORDER = _pywrapcp.RoutingModel_PICKUP_AND_DELIVERY_NO_ORDER
    r"""Any precedence is accepted."""
    PICKUP_AND_DELIVERY_LIFO = _pywrapcp.RoutingModel_PICKUP_AND_DELIVERY_LIFO
    r"""Deliveries must be performed in reverse order of pickups."""
    PICKUP_AND_DELIVERY_FIFO = _pywrapcp.RoutingModel_PICKUP_AND_DELIVERY_FIFO
    r"""Deliveries must be performed in the same order as pickups."""

    def __init__(self, *args):
        r"""
        Constructor taking an index manager. The version which does not take
        RoutingModelParameters is equivalent to passing
        DefaultRoutingModelParameters().
        """
        _pywrapcp.RoutingModel_swiginit(self, _pywrapcp.new_RoutingModel(*args))
    __swig_destroy__ = _pywrapcp.delete_RoutingModel
    kTransitEvaluatorSignUnknown = _pywrapcp.RoutingModel_kTransitEvaluatorSignUnknown
    kTransitEvaluatorSignPositiveOrZero = _pywrapcp.RoutingModel_kTransitEvaluatorSignPositiveOrZero
    kTransitEvaluatorSignNegativeOrZero = _pywrapcp.RoutingModel_kTransitEvaluatorSignNegativeOrZero

    def RegisterUnaryTransitVector(self, values):
        r"""
        Registers 'callback' and returns its index.
        The sign parameter allows to notify the solver that the callback only
        return values of the given sign. This can help the solver, but passing
        an incorrect sign may crash in non-opt compilation mode, and yield
        incorrect results in opt.
        """
        return _pywrapcp.RoutingModel_RegisterUnaryTransitVector(self, values)

    def RegisterUnaryTransitCallback(self, *args):
        return _pywrapcp.RoutingModel_RegisterUnaryTransitCallback(self, *args)

    def RegisterTransitMatrix(self, values):
        return _pywrapcp.RoutingModel_RegisterTransitMatrix(self, values)

    def RegisterTransitCallback(self, *args):
        return _pywrapcp.RoutingModel_RegisterTransitCallback(self, *args)

    def RegisterCumulDependentTransitCallback(self, callback):
        return _pywrapcp.RoutingModel_RegisterCumulDependentTransitCallback(self, callback)

    def TransitCallback(self, callback_index):
        return _pywrapcp.RoutingModel_TransitCallback(self, callback_index)

    def UnaryTransitCallbackOrNull(self, callback_index):
        return _pywrapcp.RoutingModel_UnaryTransitCallbackOrNull(self, callback_index)

    def CumulDependentTransitCallback(self, callback_index):
        return _pywrapcp.RoutingModel_CumulDependentTransitCallback(self, callback_index)

    def AddDimension(self, evaluator_index, slack_max, capacity, fix_start_cumul_to_zero, name):
        r"""
        Model creation
        Methods to add dimensions to routes; dimensions represent quantities
        accumulated at nodes along the routes. They represent quantities such as
        weights or volumes carried along the route, or distance or times.
        Quantities at a node are represented by "cumul" variables and the increase
        or decrease of quantities between nodes are represented by "transit"
        variables. These variables are linked as follows:
        if j == next(i), cumul(j) = cumul(i) + transit(i, j) + slack(i)
        where slack is a positive slack variable (can represent waiting times for
        a time dimension).
        Setting the value of fix_start_cumul_to_zero to true will force the
        "cumul" variable of the start node of all vehicles to be equal to 0.
        Creates a dimension where the transit variable is constrained to be
        equal to evaluator(i, next(i)); 'slack_max' is the upper bound of the
        slack variable and 'capacity' is the upper bound of the cumul variables.
        'name' is the name used to reference the dimension; this name is used to
        get cumul and transit variables from the routing model.
        Returns false if a dimension with the same name has already been created
        (and doesn't create the new dimension).
        Takes ownership of the callback 'evaluator'.
        """
        return _pywrapcp.RoutingModel_AddDimension(self, evaluator_index, slack_max, capacity, fix_start_cumul_to_zero, name)

    def AddDimensionWithVehicleTransits(self, evaluator_indices, slack_max, capacity, fix_start_cumul_to_zero, name):
        return _pywrapcp.RoutingModel_AddDimensionWithVehicleTransits(self, evaluator_indices, slack_max, capacity, fix_start_cumul_to_zero, name)

    def AddDimensionWithVehicleCapacity(self, evaluator_index, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name):
        return _pywrapcp.RoutingModel_AddDimensionWithVehicleCapacity(self, evaluator_index, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name)

    def AddDimensionWithVehicleTransitAndCapacity(self, evaluator_indices, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name):
        return _pywrapcp.RoutingModel_AddDimensionWithVehicleTransitAndCapacity(self, evaluator_indices, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name)

    def AddDimensionWithCumulDependentVehicleTransitAndCapacity(self, fixed_evaluator_indices, cumul_dependent_evaluator_indices, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name):
        r"""
        Creates a dimension where the transit variable on arc i->j is the sum of:
        - A "fixed" transit value, obtained from the fixed_evaluator_index for
          this vehicle, referencing evaluators in transit_evaluators_, and
        - A FloatSlopePiecewiseLinearFunction of the cumul of node i, obtained
          from the cumul_dependent_evaluator_index of this vehicle, pointing to
          an evaluator in cumul_dependent_transit_evaluators_.
        """
        return _pywrapcp.RoutingModel_AddDimensionWithCumulDependentVehicleTransitAndCapacity(self, fixed_evaluator_indices, cumul_dependent_evaluator_indices, slack_max, vehicle_capacities, fix_start_cumul_to_zero, name)

    def AddConstantDimensionWithSlack(self, value, capacity, slack_max, fix_start_cumul_to_zero, name):
        r"""
        Creates a dimension where the transit variable is constrained to be
        equal to 'value'; 'capacity' is the upper bound of the cumul variables.
        'name' is the name used to reference the dimension; this name is used to
        get cumul and transit variables from the routing model.
        Returns a pair consisting of an index to the registered unary transit
        callback and a bool denoting whether the dimension has been created.
        It is false if a dimension with the same name has already been created
        (and doesn't create the new dimension but still register a new callback).
        """
        return _pywrapcp.RoutingModel_AddConstantDimensionWithSlack(self, value, capacity, slack_max, fix_start_cumul_to_zero, name)

    def AddConstantDimension(self, value, capacity, fix_start_cumul_to_zero, name):
        return _pywrapcp.RoutingModel_AddConstantDimension(self, value, capacity, fix_start_cumul_to_zero, name)

    def AddVectorDimension(self, values, capacity, fix_start_cumul_to_zero, name):
        r"""
        Creates a dimension where the transit variable is constrained to be
        equal to 'values[i]' for node i; 'capacity' is the upper bound of
        the cumul variables. 'name' is the name used to reference the dimension;
        this name is used to get cumul and transit variables from the routing
        model.
        Returns a pair consisting of an index to the registered unary transit
        callback and a bool denoting whether the dimension has been created.
        It is false if a dimension with the same name has already been created
        (and doesn't create the new dimension but still register a new callback).
        """
        return _pywrapcp.RoutingModel_AddVectorDimension(self, values, capacity, fix_start_cumul_to_zero, name)

    def AddMatrixDimension(self, values, capacity, fix_start_cumul_to_zero, name):
        r"""
        Creates a dimension where the transit variable is constrained to be
        equal to 'values[i][next(i)]' for node i; 'capacity' is the upper bound of
        the cumul variables. 'name' is the name used to reference the dimension;
        this name is used to get cumul and transit variables from the routing
        model.
        Returns a pair consisting of an index to the registered transit callback
        and a bool denoting whether the dimension has been created.
        It is false if a dimension with the same name has already been created
        (and doesn't create the new dimension but still register a new callback).
        """
        return _pywrapcp.RoutingModel_AddMatrixDimension(self, values, capacity, fix_start_cumul_to_zero, name)

    def GetAllDimensionNames(self):
        r"""Outputs the names of all dimensions added to the routing engine."""
        return _pywrapcp.RoutingModel_GetAllDimensionNames(self)

    def GetDimensions(self):
        r"""Returns all dimensions of the model."""
        return _pywrapcp.RoutingModel_GetDimensions(self)

    def GetDimensionsWithSoftOrSpanCosts(self):
        r"""Returns dimensions with soft or vehicle span costs."""
        return _pywrapcp.RoutingModel_GetDimensionsWithSoftOrSpanCosts(self)

    def GetUnaryDimensions(self):
        r"""Returns dimensions for which all transit evaluators are unary."""
        return _pywrapcp.RoutingModel_GetUnaryDimensions(self)

    def GetDimensionsWithGlobalCumulOptimizers(self):
        r"""Returns the dimensions which have [global|local]_dimension_optimizers_."""
        return _pywrapcp.RoutingModel_GetDimensionsWithGlobalCumulOptimizers(self)

    def GetDimensionsWithLocalCumulOptimizers(self):
        return _pywrapcp.RoutingModel_GetDimensionsWithLocalCumulOptimizers(self)

    def HasGlobalCumulOptimizer(self, dimension):
        r"""Returns whether the given dimension has global/local cumul optimizers."""
        return _pywrapcp.RoutingModel_HasGlobalCumulOptimizer(self, dimension)

    def HasLocalCumulOptimizer(self, dimension):
        return _pywrapcp.RoutingModel_HasLocalCumulOptimizer(self, dimension)

    def GetMutableGlobalCumulLPOptimizer(self, dimension):
        r"""
        Returns the global/local dimension cumul optimizer for a given dimension,
        or nullptr if there is none.
        """
        return _pywrapcp.RoutingModel_GetMutableGlobalCumulLPOptimizer(self, dimension)

    def GetMutableGlobalCumulMPOptimizer(self, dimension):
        return _pywrapcp.RoutingModel_GetMutableGlobalCumulMPOptimizer(self, dimension)

    def GetMutableLocalCumulLPOptimizer(self, dimension):
        return _pywrapcp.RoutingModel_GetMutableLocalCumulLPOptimizer(self, dimension)

    def GetMutableLocalCumulMPOptimizer(self, dimension):
        return _pywrapcp.RoutingModel_GetMutableLocalCumulMPOptimizer(self, dimension)

    def HasDimension(self, dimension_name):
        r"""Returns true if a dimension exists for a given dimension name."""
        return _pywrapcp.RoutingModel_HasDimension(self, dimension_name)

    def GetDimensionOrDie(self, dimension_name):
        r"""Returns a dimension from its name. Dies if the dimension does not exist."""
        return _pywrapcp.RoutingModel_GetDimensionOrDie(self, dimension_name)

    def GetMutableDimension(self, dimension_name):
        r"""
        Returns a dimension from its name. Returns nullptr if the dimension does
        not exist.
        """
        return _pywrapcp.RoutingModel_GetMutableDimension(self, dimension_name)

    def SetPrimaryConstrainedDimension(self, dimension_name):
        r"""
        Set the given dimension as "primary constrained". As of August 2013, this
        is only used by ArcIsMoreConstrainedThanArc().
        "dimension" must be the name of an existing dimension, or be empty, in
        which case there will not be a primary dimension after this call.
        """
        return _pywrapcp.RoutingModel_SetPrimaryConstrainedDimension(self, dimension_name)

    def GetPrimaryConstrainedDimension(self):
        r"""Get the primary constrained dimension, or an empty string if it is unset."""
        return _pywrapcp.RoutingModel_GetPrimaryConstrainedDimension(self)

    def GetResourceGroup(self, rg_index):
        return _pywrapcp.RoutingModel_GetResourceGroup(self, rg_index)

    def GetDimensionResourceGroupIndices(self, dimension):
        r"""
        Returns the indices of resource groups for this dimension. This method can
        only be called after the model has been closed.
        """
        return _pywrapcp.RoutingModel_GetDimensionResourceGroupIndices(self, dimension)

    def GetDimensionResourceGroupIndex(self, dimension):
        r"""
        Returns the index of the resource group attached to the dimension.
        DCHECKS that there's exactly one resource group for this dimension.
        """
        return _pywrapcp.RoutingModel_GetDimensionResourceGroupIndex(self, dimension)
    PENALIZE_ONCE = _pywrapcp.RoutingModel_PENALIZE_ONCE
    PENALIZE_PER_INACTIVE = _pywrapcp.RoutingModel_PENALIZE_PER_INACTIVE

    def AddDisjunction(self, *args):
        r"""
        Adds a disjunction constraint on the indices: exactly 'max_cardinality' of
        the indices are active. Start and end indices of any vehicle cannot be
        part of a disjunction.

        If a penalty is given, at most 'max_cardinality' of the indices can be
        active, and if less are active, 'penalty' is payed per inactive index if
        the penalty cost is set to `PENALIZE_PER_INACTIVE`.
        This is equivalent to adding the constraint:
            p + Sum(i)active[i] == max_cardinality
        where p is an integer variable.
        If the penalty cost is set to `PENALIZE_ONCE`, then 'penalty' is payed
        once if there are less than `max_cardinality` of the indices active.
        This is equivalent to adding the constraint:
            p == (Sum(i)active[i] != max_cardinality)
        where p is a boolean variable.
        The following cost is added to the cost function: p * penalty.
        'penalty' must be positive to make the disjunction optional; a negative
        penalty will force 'max_cardinality' indices of the disjunction to be
        performed, and therefore p == 0.
        Note: passing a vector with a single index will model an optional index
        with a penalty cost if it is not visited.
        """
        return _pywrapcp.RoutingModel_AddDisjunction(self, *args)

    def GetDisjunctionIndices(self, index):
        r"""Returns the indices of the disjunctions to which an index belongs."""
        return _pywrapcp.RoutingModel_GetDisjunctionIndices(self, index)

    def GetDisjunctionPenalty(self, index):
        r"""Returns the penalty of the node disjunction of index 'index'."""
        return _pywrapcp.RoutingModel_GetDisjunctionPenalty(self, index)

    def GetDisjunctionMaxCardinality(self, index):
        r"""
        Returns the maximum number of possible active nodes of the node
        disjunction of index 'index'.
        """
        return _pywrapcp.RoutingModel_GetDisjunctionMaxCardinality(self, index)

    def GetDisjunctionPenaltyCostBehavior(self, index):
        r"""
        Returns the 'PenaltyCostBehavior' used by the disjunction of index
        'index'.
        """
        return _pywrapcp.RoutingModel_GetDisjunctionPenaltyCostBehavior(self, index)

    def GetNumberOfDisjunctions(self):
        r"""Returns the number of node disjunctions in the model."""
        return _pywrapcp.RoutingModel_GetNumberOfDisjunctions(self)

    def HasMandatoryDisjunctions(self):
        r"""
        Returns true if the model contains mandatory disjunctions (ones with
        kNoPenalty as penalty).
        """
        return _pywrapcp.RoutingModel_HasMandatoryDisjunctions(self)

    def HasMaxCardinalityConstrainedDisjunctions(self):
        r"""
        Returns true if the model contains at least one disjunction which is
        constrained by its max_cardinality.
        """
        return _pywrapcp.RoutingModel_HasMaxCardinalityConstrainedDisjunctions(self)

    def GetPerfectBinaryDisjunctions(self):
        r"""
        Returns the list of all perfect binary disjunctions, as pairs of variable
        indices: a disjunction is "perfect" when its variables do not appear in
        any other disjunction. Each pair is sorted (lowest variable index first),
        and the output vector is also sorted (lowest pairs first).
        """
        return _pywrapcp.RoutingModel_GetPerfectBinaryDisjunctions(self)

    def IgnoreDisjunctionsAlreadyForcedToZero(self):
        r"""
        SPECIAL: Makes the solver ignore all the disjunctions whose active
        variables are all trivially zero (i.e. Max() == 0), by setting their
        max_cardinality to 0.
        This can be useful when using the BaseBinaryDisjunctionNeighborhood
        operators, in the context of arc-based routing.
        """
        return _pywrapcp.RoutingModel_IgnoreDisjunctionsAlreadyForcedToZero(self)

    def AddSoftSameVehicleConstraint(self, indices, cost):
        r"""
        Adds a soft constraint to force a set of variable indices to be on the
        same vehicle. If all nodes are not on the same vehicle, each extra vehicle
        used adds 'cost' to the cost function.
        """
        return _pywrapcp.RoutingModel_AddSoftSameVehicleConstraint(self, indices, cost)

    def SetAllowedVehiclesForIndex(self, vehicles, index):
        r"""
        Sets the vehicles which can visit a given node. If the node is in a
        disjunction, this will not prevent it from being unperformed.
        Specifying an empty vector of vehicles has no effect (all vehicles
        will be allowed to visit the node).
        """
        return _pywrapcp.RoutingModel_SetAllowedVehiclesForIndex(self, vehicles, index)

    def IsVehicleAllowedForIndex(self, vehicle, index):
        r"""Returns true if a vehicle is allowed to visit a given node."""
        return _pywrapcp.RoutingModel_IsVehicleAllowedForIndex(self, vehicle, index)

    def AddPickupAndDelivery(self, pickup, delivery):
        r"""
        Notifies that index1 and index2 form a pair of nodes which should belong
        to the same route. This methods helps the search find better solutions,
        especially in the local search phase.
        It should be called each time you have an equality constraint linking
        the vehicle variables of two node (including for instance pickup and
        delivery problems):
            Solver* const solver = routing.solver();
            int64_t index1 = manager.NodeToIndex(node1);
            int64_t index2 = manager.NodeToIndex(node2);
            solver->AddConstraint(solver->MakeEquality(
                routing.VehicleVar(index1),
                routing.VehicleVar(index2)));
            routing.AddPickupAndDelivery(index1, index2);
        """
        return _pywrapcp.RoutingModel_AddPickupAndDelivery(self, pickup, delivery)

    def AddPickupAndDeliverySets(self, pickup_disjunction, delivery_disjunction):
        r"""
        Same as AddPickupAndDelivery but notifying that the performed node from
        the disjunction of index 'pickup_disjunction' is on the same route as the
        performed node from the disjunction of index 'delivery_disjunction'.
        """
        return _pywrapcp.RoutingModel_AddPickupAndDeliverySets(self, pickup_disjunction, delivery_disjunction)

    def GetPickupPosition(self, node_index):
        r"""Returns the pickup and delivery positions where the node is a pickup."""
        return _pywrapcp.RoutingModel_GetPickupPosition(self, node_index)

    def GetDeliveryPosition(self, node_index):
        r"""Returns the pickup and delivery positions where the node is a delivery."""
        return _pywrapcp.RoutingModel_GetDeliveryPosition(self, node_index)

    def IsPickup(self, node_index):
        r"""Returns whether the node is a pickup (resp. delivery)."""
        return _pywrapcp.RoutingModel_IsPickup(self, node_index)

    def IsDelivery(self, node_index):
        return _pywrapcp.RoutingModel_IsDelivery(self, node_index)

    def SetPickupAndDeliveryPolicyOfAllVehicles(self, policy):
        r"""
        Sets the Pickup and delivery policy of all vehicles. It is equivalent to
        calling SetPickupAndDeliveryPolicyOfVehicle on all vehicles.
        """
        return _pywrapcp.RoutingModel_SetPickupAndDeliveryPolicyOfAllVehicles(self, policy)

    def SetPickupAndDeliveryPolicyOfVehicle(self, policy, vehicle):
        return _pywrapcp.RoutingModel_SetPickupAndDeliveryPolicyOfVehicle(self, policy, vehicle)

    def GetPickupAndDeliveryPolicyOfVehicle(self, vehicle):
        return _pywrapcp.RoutingModel_GetPickupAndDeliveryPolicyOfVehicle(self, vehicle)

    def GetNumOfSingletonNodes(self):
        r"""
        Returns the number of non-start/end nodes which do not appear in a
        pickup/delivery pair.
        """
        return _pywrapcp.RoutingModel_GetNumOfSingletonNodes(self)

    def GetFirstMatchingPickupDeliverySibling(self, node, is_match):
        return _pywrapcp.RoutingModel_GetFirstMatchingPickupDeliverySibling(self, node, is_match)
    TYPE_ADDED_TO_VEHICLE = _pywrapcp.RoutingModel_TYPE_ADDED_TO_VEHICLE
    r"""When visited, the number of types 'T' on the vehicle increases by one."""
    ADDED_TYPE_REMOVED_FROM_VEHICLE = _pywrapcp.RoutingModel_ADDED_TYPE_REMOVED_FROM_VEHICLE
    r"""
    When visited, one instance of type 'T' previously added to the route
    (TYPE_ADDED_TO_VEHICLE), if any, is removed from the vehicle.
    If the type was not previously added to the route or all added instances
    have already been removed, this visit has no effect on the types.
    """
    TYPE_ON_VEHICLE_UP_TO_VISIT = _pywrapcp.RoutingModel_TYPE_ON_VEHICLE_UP_TO_VISIT
    r"""
    With the following policy, the visit enforces that type 'T' is
    considered on the route from its start until this node is visited.
    """
    TYPE_SIMULTANEOUSLY_ADDED_AND_REMOVED = _pywrapcp.RoutingModel_TYPE_SIMULTANEOUSLY_ADDED_AND_REMOVED
    r"""
    The visit doesn't have an impact on the number of types 'T' on the
    route, as it's (virtually) added and removed directly.
    This policy can be used for visits which are part of an incompatibility
    or requirement set without affecting the type count on the route.
    """

    def SetVisitType(self, index, type, type_policy):
        return _pywrapcp.RoutingModel_SetVisitType(self, index, type, type_policy)

    def GetVisitType(self, index):
        return _pywrapcp.RoutingModel_GetVisitType(self, index)

    def GetSingleNodesOfType(self, type):
        return _pywrapcp.RoutingModel_GetSingleNodesOfType(self, type)

    def GetPairIndicesOfType(self, type):
        return _pywrapcp.RoutingModel_GetPairIndicesOfType(self, type)

    def GetVisitTypePolicy(self, index):
        return _pywrapcp.RoutingModel_GetVisitTypePolicy(self, index)

    def GetNumberOfVisitTypes(self):
        return _pywrapcp.RoutingModel_GetNumberOfVisitTypes(self)

    def AddHardTypeIncompatibility(self, type1, type2):
        r"""
        Incompatibilities:
        Two nodes with "hard" incompatible types cannot share the same route at
        all, while with a "temporal" incompatibility they can't be on the same
        route at the same time.
        NOTE: To avoid unnecessary memory reallocations, it is recommended to only
        add incompatibilities once all the existing types have been set with
        SetVisitType().
        """
        return _pywrapcp.RoutingModel_AddHardTypeIncompatibility(self, type1, type2)

    def AddTemporalTypeIncompatibility(self, type1, type2):
        return _pywrapcp.RoutingModel_AddTemporalTypeIncompatibility(self, type1, type2)

    def GetHardTypeIncompatibilitiesOfType(self, type):
        r"""Returns visit types incompatible with a given type."""
        return _pywrapcp.RoutingModel_GetHardTypeIncompatibilitiesOfType(self, type)

    def GetTemporalTypeIncompatibilitiesOfType(self, type):
        return _pywrapcp.RoutingModel_GetTemporalTypeIncompatibilitiesOfType(self, type)

    def HasHardTypeIncompatibilities(self):
        r"""
        Returns true iff any hard (resp. temporal) type incompatibilities have
        been added to the model.
        """
        return _pywrapcp.RoutingModel_HasHardTypeIncompatibilities(self)

    def HasTemporalTypeIncompatibilities(self):
        return _pywrapcp.RoutingModel_HasTemporalTypeIncompatibilities(self)

    def AddSameVehicleRequiredTypeAlternatives(self, dependent_type, required_type_alternatives):
        r"""
        Requirements:
        NOTE: As of 2019-04, cycles in the requirement graph are not supported,
        and lead to the dependent nodes being skipped if possible (otherwise
        the model is considered infeasible).
        The following functions specify that "dependent_type" requires at least
        one of the types in "required_type_alternatives".

        For same-vehicle requirements, a node of dependent type type_D requires at
        least one node of type type_R among the required alternatives on the same
        route.
        NOTE: To avoid unnecessary memory reallocations, it is recommended to only
        add requirements once all the existing types have been set with
        SetVisitType().
        """
        return _pywrapcp.RoutingModel_AddSameVehicleRequiredTypeAlternatives(self, dependent_type, required_type_alternatives)

    def AddRequiredTypeAlternativesWhenAddingType(self, dependent_type, required_type_alternatives):
        r"""
        If type_D depends on type_R when adding type_D, any node_D of type_D and
        VisitTypePolicy TYPE_ADDED_TO_VEHICLE or
        TYPE_SIMULTANEOUSLY_ADDED_AND_REMOVED requires at least one type_R on its
        vehicle at the time node_D is visited.
        """
        return _pywrapcp.RoutingModel_AddRequiredTypeAlternativesWhenAddingType(self, dependent_type, required_type_alternatives)

    def AddRequiredTypeAlternativesWhenRemovingType(self, dependent_type, required_type_alternatives):
        r"""
        The following requirements apply when visiting dependent nodes that remove
        their type from the route, i.e. type_R must be on the vehicle when type_D
        of VisitTypePolicy ADDED_TYPE_REMOVED_FROM_VEHICLE,
        TYPE_ON_VEHICLE_UP_TO_VISIT or TYPE_SIMULTANEOUSLY_ADDED_AND_REMOVED is
        visited.
        """
        return _pywrapcp.RoutingModel_AddRequiredTypeAlternativesWhenRemovingType(self, dependent_type, required_type_alternatives)

    def GetSameVehicleRequiredTypeAlternativesOfType(self, type):
        r"""
        Returns the set of same-vehicle requirement alternatives for the given
        type.
        """
        return _pywrapcp.RoutingModel_GetSameVehicleRequiredTypeAlternativesOfType(self, type)

    def GetRequiredTypeAlternativesWhenAddingType(self, type):
        r"""Returns the set of requirement alternatives when adding the given type."""
        return _pywrapcp.RoutingModel_GetRequiredTypeAlternativesWhenAddingType(self, type)

    def GetRequiredTypeAlternativesWhenRemovingType(self, type):
        r"""Returns the set of requirement alternatives when removing the given type."""
        return _pywrapcp.RoutingModel_GetRequiredTypeAlternativesWhenRemovingType(self, type)

    def HasSameVehicleTypeRequirements(self):
        r"""
        Returns true iff any same-route (resp. temporal) type requirements have
        been added to the model.
        """
        return _pywrapcp.RoutingModel_HasSameVehicleTypeRequirements(self)

    def HasTemporalTypeRequirements(self):
        return _pywrapcp.RoutingModel_HasTemporalTypeRequirements(self)

    def HasTypeRegulations(self):
        r"""
        Returns true iff the model has any incompatibilities or requirements set
        on node types.
        """
        return _pywrapcp.RoutingModel_HasTypeRegulations(self)

    def UnperformedPenalty(self, var_index):
        r"""
        Get the "unperformed" penalty of a node. This is only well defined if the
        node is only part of a single Disjunction, and that disjunction has a
        penalty. For forced active nodes returns max int64_t. In all other cases,
        this returns 0.
        """
        return _pywrapcp.RoutingModel_UnperformedPenalty(self, var_index)

    def UnperformedPenaltyOrValue(self, default_value, var_index):
        r"""
        Same as above except that it returns default_value instead of 0 when
        penalty is not well defined (default value is passed as first argument to
        simplify the usage of the method in a callback).
        """
        return _pywrapcp.RoutingModel_UnperformedPenaltyOrValue(self, default_value, var_index)

    def GetDepot(self):
        r"""
        Returns the variable index of the first starting or ending node of all
        routes. If all routes start  and end at the same node (single depot), this
        is the node returned.
        """
        return _pywrapcp.RoutingModel_GetDepot(self)

    def SetMaximumNumberOfActiveVehicles(self, max_active_vehicles):
        r"""
        Constrains the maximum number of active vehicles, aka the number of
        vehicles which do not have an empty route. For instance, this can be used
        to limit the number of routes in the case where there are fewer drivers
        than vehicles and that the fleet of vehicle is heterogeneous.
        """
        return _pywrapcp.RoutingModel_SetMaximumNumberOfActiveVehicles(self, max_active_vehicles)

    def GetMaximumNumberOfActiveVehicles(self):
        r"""Returns the maximum number of active vehicles."""
        return _pywrapcp.RoutingModel_GetMaximumNumberOfActiveVehicles(self)

    def SetArcCostEvaluatorOfAllVehicles(self, evaluator_index):
        r"""
        Sets the cost function of the model such that the cost of a segment of a
        route between node 'from' and 'to' is evaluator(from, to), whatever the
        route or vehicle performing the route.
        """
        return _pywrapcp.RoutingModel_SetArcCostEvaluatorOfAllVehicles(self, evaluator_index)

    def SetArcCostEvaluatorOfVehicle(self, evaluator_index, vehicle):
        r"""Sets the cost function for a given vehicle route."""
        return _pywrapcp.RoutingModel_SetArcCostEvaluatorOfVehicle(self, evaluator_index, vehicle)

    def SetFixedCostOfAllVehicles(self, cost):
        r"""
        Sets the fixed cost of all vehicle routes. It is equivalent to calling
        SetFixedCostOfVehicle on all vehicle routes.
        """
        return _pywrapcp.RoutingModel_SetFixedCostOfAllVehicles(self, cost)

    def SetFixedCostOfVehicle(self, cost, vehicle):
        r"""Sets the fixed cost of one vehicle route."""
        return _pywrapcp.RoutingModel_SetFixedCostOfVehicle(self, cost, vehicle)

    def GetFixedCostOfVehicle(self, vehicle):
        r"""
        Returns the route fixed cost taken into account if the route of the
        vehicle is not empty, aka there's at least one node on the route other
        than the first and last nodes.
        """
        return _pywrapcp.RoutingModel_GetFixedCostOfVehicle(self, vehicle)

    def SetPathEnergyCostOfVehicle(self, force, distance, cost_per_unit, vehicle):
        return _pywrapcp.RoutingModel_SetPathEnergyCostOfVehicle(self, force, distance, cost_per_unit, vehicle)

    def SetPathEnergyCostsOfVehicle(self, force, distance, threshold, cost_per_unit_below_threshold, cost_per_unit_above_threshold, vehicle):
        return _pywrapcp.RoutingModel_SetPathEnergyCostsOfVehicle(self, force, distance, threshold, cost_per_unit_below_threshold, cost_per_unit_above_threshold, vehicle)

    def SetAmortizedCostFactorsOfAllVehicles(self, linear_cost_factor, quadratic_cost_factor):
        r"""
        The following methods set the linear and quadratic cost factors of
        vehicles (must be positive values). The default value of these parameters
        is zero for all vehicles.

        When set, the cost_ of the model will contain terms aiming at reducing the
        number of vehicles used in the model, by adding the following to the
        objective for every vehicle v:
        INDICATOR(v used in the model) *
          [linear_cost_factor_of_vehicle_[v]
           - quadratic_cost_factor_of_vehicle_[v]*(square of length of route v)]
        i.e. for every used vehicle, we add the linear factor as fixed cost, and
        subtract the square of the route length multiplied by the quadratic
        factor. This second term aims at making the routes as dense as possible.

        Sets the linear and quadratic cost factor of all vehicles.
        """
        return _pywrapcp.RoutingModel_SetAmortizedCostFactorsOfAllVehicles(self, linear_cost_factor, quadratic_cost_factor)

    def SetAmortizedCostFactorsOfVehicle(self, linear_cost_factor, quadratic_cost_factor, vehicle):
        r"""Sets the linear and quadratic cost factor of the given vehicle."""
        return _pywrapcp.RoutingModel_SetAmortizedCostFactorsOfVehicle(self, linear_cost_factor, quadratic_cost_factor, vehicle)

    def GetAmortizedLinearCostFactorOfVehicles(self):
        return _pywrapcp.RoutingModel_GetAmortizedLinearCostFactorOfVehicles(self)

    def GetAmortizedQuadraticCostFactorOfVehicles(self):
        return _pywrapcp.RoutingModel_GetAmortizedQuadraticCostFactorOfVehicles(self)

    def AddRouteConstraint(self, route_evaluator, costs_are_homogeneous_across_vehicles=False):
        return _pywrapcp.RoutingModel_AddRouteConstraint(self, route_evaluator, costs_are_homogeneous_across_vehicles)

    def GetRouteCost(self, route):
        return _pywrapcp.RoutingModel_GetRouteCost(self, route)

    def SetVehicleUsedWhenEmpty(self, is_used, vehicle):
        return _pywrapcp.RoutingModel_SetVehicleUsedWhenEmpty(self, is_used, vehicle)

    def IsVehicleUsedWhenEmpty(self, vehicle):
        return _pywrapcp.RoutingModel_IsVehicleUsedWhenEmpty(self, vehicle)

    def SetFirstSolutionEvaluator(self, evaluator):
        r"""
        Gets/sets the evaluator used during the search. Only relevant when
        RoutingSearchParameters.first_solution_strategy = EVALUATOR_STRATEGY.
        Takes ownership of evaluator.
        """
        return _pywrapcp.RoutingModel_SetFirstSolutionEvaluator(self, evaluator)

    def SetFirstSolutionHint(self, hint):
        r"""
        Adds a hint to be used by first solution strategies. The hint assignment
        must outlive the search.
        As of 2024-12, only used by LOCAL_CHEAPEST_INSERTION and
        LOCAL_CHEAPEST_COST_INSERTION.
        """
        return _pywrapcp.RoutingModel_SetFirstSolutionHint(self, hint)

    def GetFirstSolutionHint(self):
        r"""Returns the current hint assignment."""
        return _pywrapcp.RoutingModel_GetFirstSolutionHint(self)

    def AddLocalSearchOperator(self, ls_operator):
        r"""
        Adds a local search operator to the set of operators used to solve the
        vehicle routing problem.
        """
        return _pywrapcp.RoutingModel_AddLocalSearchOperator(self, ls_operator)

    def AddSearchMonitor(self, monitor):
        r"""Adds a search monitor to the search used to solve the routing model."""
        return _pywrapcp.RoutingModel_AddSearchMonitor(self, monitor)

    def AddEnterSearchCallback(self, callback):
        return _pywrapcp.RoutingModel_AddEnterSearchCallback(self, callback)

    def AddAtSolutionCallback(self, callback, track_unchecked_neighbors=False):
        r"""
        Adds a callback called each time a solution is found during the search.
        This is a shortcut to creating a monitor to call the callback on
        AtSolution() and adding it with AddSearchMonitor.
        If track_unchecked_neighbors is true, the callback will also be called on
        AcceptUncheckedNeighbor() events, which is useful to grab solutions
        obtained when solver_parameters.check_solution_period > 1 (aka fastLS).
        """
        return _pywrapcp.RoutingModel_AddAtSolutionCallback(self, callback, track_unchecked_neighbors)

    def AddRestoreDimensionValuesResetCallback(self, callback):
        return _pywrapcp.RoutingModel_AddRestoreDimensionValuesResetCallback(self, callback)

    def AddVariableMinimizedByFinalizer(self, var):
        r"""
        Adds a variable to minimize in the solution finalizer. The solution
        finalizer is called each time a solution is found during the search and
        allows to instantiate secondary variables (such as dimension cumul
        variables).
        """
        return _pywrapcp.RoutingModel_AddVariableMinimizedByFinalizer(self, var)

    def AddVariableMaximizedByFinalizer(self, var):
        r"""
        Adds a variable to maximize in the solution finalizer (see above for
        information on the solution finalizer).
        """
        return _pywrapcp.RoutingModel_AddVariableMaximizedByFinalizer(self, var)

    def AddWeightedVariableMinimizedByFinalizer(self, var, cost):
        r"""
        Adds a variable to minimize in the solution finalizer, with a weighted
        priority: the higher the more priority it has.
        """
        return _pywrapcp.RoutingModel_AddWeightedVariableMinimizedByFinalizer(self, var, cost)

    def AddWeightedVariableMaximizedByFinalizer(self, var, cost):
        r"""
        Adds a variable to maximize in the solution finalizer, with a weighted
        priority: the higher the more priority it has.
        """
        return _pywrapcp.RoutingModel_AddWeightedVariableMaximizedByFinalizer(self, var, cost)

    def AddVariableTargetToFinalizer(self, var, target):
        r"""
        Add a variable to set the closest possible to the target value in the
        solution finalizer.
        """
        return _pywrapcp.RoutingModel_AddVariableTargetToFinalizer(self, var, target)

    def AddWeightedVariableTargetToFinalizer(self, var, target, cost):
        r"""
        Same as above with a weighted priority: the higher the cost, the more
        priority it has to be set close to the target value.
        """
        return _pywrapcp.RoutingModel_AddWeightedVariableTargetToFinalizer(self, var, target, cost)

    def CloseModel(self):
        r"""
        Closes the current routing model; after this method is called, no
        modification to the model can be done, but RoutesToAssignment becomes
        available. Note that CloseModel() is automatically called by Solve() and
        other methods that produce solution.
        This is equivalent to calling
        CloseModelWithParameters(DefaultRoutingSearchParameters()).
        """
        return _pywrapcp.RoutingModel_CloseModel(self)

    def CloseModelWithParameters(self, search_parameters):
        r"""
        Same as above taking search parameters (as of 10/2015 some the parameters
        have to be set when closing the model).
        """
        return _pywrapcp.RoutingModel_CloseModelWithParameters(self, search_parameters)

    def Solve(self, assignment=None):
        r"""
        Solves the current routing model; closes the current model.
        This is equivalent to calling
        SolveWithParameters(DefaultRoutingSearchParameters())
        or
        SolveFromAssignmentWithParameters(assignment,
                                          DefaultRoutingSearchParameters()).
        """
        return _pywrapcp.RoutingModel_Solve(self, assignment)

    def SolveWithParameters(self, search_parameters, solutions=None):
        r"""
        Solves the current routing model with the given parameters. If 'solutions'
        is specified, it will contain the k best solutions found during the search
        (from worst to best, including the one returned by this method), where k
        corresponds to the 'number_of_solutions_to_collect' in
        'search_parameters'. Note that the Assignment returned by the method and
        the ones in solutions are owned by the underlying solver and should not be
        deleted.
        """
        return _pywrapcp.RoutingModel_SolveWithParameters(self, search_parameters, solutions)

    def SolveFromAssignmentWithParameters(self, assignment, search_parameters, solutions=None):
        r"""
        Same as above, except that if assignment is not null, it will be used as
        the initial solution.
        """
        return _pywrapcp.RoutingModel_SolveFromAssignmentWithParameters(self, assignment, search_parameters, solutions)

    def FastSolveFromAssignmentWithParameters(self, assignment, search_parameters, check_solution_in_cp, touched=None):
        r"""
        Improves a given assignment using unchecked local search.
        If check_solution_in_cp is true the final solution will be checked with
        the CP solver.
        As of 11/2023, only works with greedy descent.
        """
        return _pywrapcp.RoutingModel_FastSolveFromAssignmentWithParameters(self, assignment, search_parameters, check_solution_in_cp, touched)

    def SolveFromAssignmentsWithParameters(self, assignments, search_parameters, solutions=None):
        r"""
        Same as above but will try all assignments in order as first solutions
        until one succeeds.
        """
        return _pywrapcp.RoutingModel_SolveFromAssignmentsWithParameters(self, assignments, search_parameters, solutions)

    def SolveWithIteratedLocalSearch(self, search_parameters):
        r"""
        Solves the current routing model by using an Iterated Local Search
        approach.
        """
        return _pywrapcp.RoutingModel_SolveWithIteratedLocalSearch(self, search_parameters)

    def SetAssignmentFromOtherModelAssignment(self, target_assignment, source_model, source_assignment):
        r"""
        Given a "source_model" and its "source_assignment", resets
        "target_assignment" with the IntVar variables (nexts_, and vehicle_vars_
        if costs aren't homogeneous across vehicles) of "this" model, with the
        values set according to those in "other_assignment".
        The objective_element of target_assignment is set to this->cost_.
        """
        return _pywrapcp.RoutingModel_SetAssignmentFromOtherModelAssignment(self, target_assignment, source_model, source_assignment)

    def ComputeLowerBound(self):
        r"""
        Computes a lower bound to the routing problem solving a linear assignment
        problem. The routing model must be closed before calling this method.
        Note that problems with node disjunction constraints (including optional
        nodes) and non-homogenous costs are not supported (the method returns 0 in
        these cases).
        """
        return _pywrapcp.RoutingModel_ComputeLowerBound(self)

    def objective_lower_bound(self):
        r"""
        Returns the current lower bound found by internal solvers during the
        search.
        """
        return _pywrapcp.RoutingModel_objective_lower_bound(self)

    def status(self):
        r"""Returns the current status of the routing model."""
        return _pywrapcp.RoutingModel_status(self)

    def enable_deep_serialization(self):
        r"""Returns the value of the internal enable_deep_serialization_ parameter."""
        return _pywrapcp.RoutingModel_enable_deep_serialization(self)

    def ApplyLocks(self, locks):
        r"""
        Applies a lock chain to the next search. 'locks' represents an ordered
        vector of nodes representing a partial route which will be fixed during
        the next search; it will constrain next variables such that:
        next[locks[i]] == locks[i+1].

        Returns the next variable at the end of the locked chain; this variable is
        not locked. An assignment containing the locks can be obtained by calling
        PreAssignment().
        """
        return _pywrapcp.RoutingModel_ApplyLocks(self, locks)

    def ApplyLocksToAllVehicles(self, locks, close_routes):
        r"""
        Applies lock chains to all vehicles to the next search, such that locks[p]
        is the lock chain for route p. Returns false if the locks do not contain
        valid routes; expects that the routes do not contain the depots,
        i.e. there are empty vectors in place of empty routes.
        If close_routes is set to true, adds the end nodes to the route of each
        vehicle and deactivates other nodes.
        An assignment containing the locks can be obtained by calling
        PreAssignment().
        """
        return _pywrapcp.RoutingModel_ApplyLocksToAllVehicles(self, locks, close_routes)

    def PreAssignment(self):
        r"""
        Returns an assignment used to fix some of the variables of the problem.
        In practice, this assignment locks partial routes of the problem. This
        can be used in the context of locking the parts of the routes which have
        already been driven in online routing problems.
        """
        return _pywrapcp.RoutingModel_PreAssignment(self)

    def MutablePreAssignment(self):
        return _pywrapcp.RoutingModel_MutablePreAssignment(self)

    def WriteAssignment(self, file_name):
        r"""
        Writes the current solution to a file containing an AssignmentProto.
        Returns false if the file cannot be opened or if there is no current
        solution.
        """
        return _pywrapcp.RoutingModel_WriteAssignment(self, file_name)

    def ReadAssignment(self, file_name):
        r"""
        Reads an assignment from a file and returns the current solution.
        Returns nullptr if the file cannot be opened or if the assignment is not
        valid.
        """
        return _pywrapcp.RoutingModel_ReadAssignment(self, file_name)

    def RestoreAssignment(self, solution):
        r"""
        Restores an assignment as a solution in the routing model and returns the
        new solution. Returns nullptr if the assignment is not valid.
        """
        return _pywrapcp.RoutingModel_RestoreAssignment(self, solution)

    def ReadAssignmentFromRoutes(self, routes, ignore_inactive_indices):
        r"""
        Restores the routes as the current solution. Returns nullptr if the
        solution cannot be restored (routes do not contain a valid solution). Note
        that calling this method will run the solver to assign values to the
        dimension variables; this may take considerable amount of time, especially
        when using dimensions with slack.
        """
        return _pywrapcp.RoutingModel_ReadAssignmentFromRoutes(self, routes, ignore_inactive_indices)

    def RoutesToAssignment(self, routes, ignore_inactive_indices, close_routes, assignment):
        r"""
        Fills an assignment from a specification of the routes of the
        vehicles. The routes are specified as lists of variable indices that
        appear on the routes of the vehicles. The indices of the outer vector in
        'routes' correspond to vehicles IDs, the inner vector contains the
        variable indices on the routes for the given vehicle. The inner vectors
        must not contain the start and end indices, as these are determined by the
        routing model.  Sets the value of NextVars in the assignment, adding the
        variables to the assignment if necessary. The method does not touch other
        variables in the assignment. The method can only be called after the model
        is closed.  With ignore_inactive_indices set to false, this method will
        fail (return nullptr) in case some of the route contain indices that are
        deactivated in the model; when set to true, these indices will be
        skipped.  Returns true if routes were successfully
        loaded. However, such assignment still might not be a valid
        solution to the routing problem due to more complex constraints;
        it is advisible to call solver()->CheckSolution() afterwards.
        """
        return _pywrapcp.RoutingModel_RoutesToAssignment(self, routes, ignore_inactive_indices, close_routes, assignment)

    def AssignmentToRoutes(self, assignment, routes):
        r"""
        Converts the solution in the given assignment to routes for all vehicles.
        Expects that assignment contains a valid solution (i.e. routes for all
        vehicles end with an end index for that vehicle).
        """
        return _pywrapcp.RoutingModel_AssignmentToRoutes(self, assignment, routes)

    def CompactAssignment(self, assignment):
        r"""
        Converts the solution in the given assignment to routes for all vehicles.
        If the returned vector is route_indices, route_indices[i][j] is the index
        for jth location visited on route i. Note that contrary to
        AssignmentToRoutes, the vectors do include start and end locations.
        Returns a compacted version of the given assignment, in which all vehicles
        with id lower or equal to some N have non-empty routes, and all vehicles
        with id greater than N have empty routes. Does not take ownership of the
        returned object.
        If found, the cost of the compact assignment is the same as in the
        original assignment and it preserves the values of 'active' variables.
        Returns nullptr if a compact assignment was not found.
        This method only works in homogenous mode, and it only swaps equivalent
        vehicles (vehicles with the same start and end nodes). When creating the
        compact assignment, the empty plan is replaced by the route assigned to
        the compatible vehicle with the highest id. Note that with more complex
        constraints on vehicle variables, this method might fail even if a compact
        solution exists.
        This method changes the vehicle and dimension variables as necessary.
        While compacting the solution, only basic checks on vehicle variables are
        performed; if one of these checks fails no attempts to repair it are made
        (instead, the method returns nullptr).
        """
        return _pywrapcp.RoutingModel_CompactAssignment(self, assignment)

    def CompactAndCheckAssignment(self, assignment):
        r"""
        Same as CompactAssignment() but also checks the validity of the final
        compact solution; if it is not valid, no attempts to repair it are made
        (instead, the method returns nullptr).
        """
        return _pywrapcp.RoutingModel_CompactAndCheckAssignment(self, assignment)

    def AddToAssignment(self, var):
        r"""Adds an extra variable to the vehicle routing assignment."""
        return _pywrapcp.RoutingModel_AddToAssignment(self, var)

    def AddIntervalToAssignment(self, interval):
        return _pywrapcp.RoutingModel_AddIntervalToAssignment(self, interval)

    def PackCumulsOfOptimizerDimensionsFromAssignment(self, original_assignment, duration_limit, time_limit_was_reached=None):
        r"""
        For every dimension in the model with an optimizer in
        local/global_dimension_optimizers_, this method tries to pack the cumul
        values of the dimension, such that:
        - The cumul costs (span costs, soft lower and upper bound costs, etc) are
          minimized.
        - The cumuls of the ends of the routes are minimized for this given
          minimal cumul cost.
        - Given these minimal end cumuls, the route start cumuls are maximized.
        Returns the assignment resulting from allocating these packed cumuls with
        the solver, and nullptr if these cumuls could not be set by the solver.
        """
        return _pywrapcp.RoutingModel_PackCumulsOfOptimizerDimensionsFromAssignment(self, original_assignment, duration_limit, time_limit_was_reached)

    def GetOrCreateNodeNeighborsByCostClass(self, *args):
        r"""
        *Overload 1:*
        Returns neighbors of all nodes for every cost class. The result is cached
        and is computed once. The number of neighbors considered is based on a
        ratio of non-vehicle nodes, specified by neighbors_ratio, with a minimum
        of min-neighbors node considered.

        |

        *Overload 2:*
        Returns parameters.num_neighbors neighbors of all nodes for every cost
        class. The result is cached and is computed once.
        """
        return _pywrapcp.RoutingModel_GetOrCreateNodeNeighborsByCostClass(self, *args)

    def AddLocalSearchFilter(self, filter):
        r"""
        Adds a custom local search filter to the list of filters used to speed up
        local search by pruning unfeasible variable assignments.
        Calling this method after the routing model has been closed (CloseModel()
        or Solve() has been called) has no effect.
        The routing model does not take ownership of the filter.
        """
        return _pywrapcp.RoutingModel_AddLocalSearchFilter(self, filter)

    def Start(self, vehicle):
        r"""
        Model inspection.
        Returns the variable index of the starting node of a vehicle route.
        """
        return _pywrapcp.RoutingModel_Start(self, vehicle)

    def End(self, vehicle):
        r"""Returns the variable index of the ending node of a vehicle route."""
        return _pywrapcp.RoutingModel_End(self, vehicle)

    def IsStart(self, index):
        r"""Returns true if 'index' represents the first node of a route."""
        return _pywrapcp.RoutingModel_IsStart(self, index)

    def IsEnd(self, index):
        r"""Returns true if 'index' represents the last node of a route."""
        return _pywrapcp.RoutingModel_IsEnd(self, index)

    def VehicleIndex(self, index):
        r"""
        Returns the vehicle of the given start/end index, and -1 if the given
        index is not a vehicle start/end.
        """
        return _pywrapcp.RoutingModel_VehicleIndex(self, index)

    def Next(self, assignment, index):
        r"""
        Assignment inspection
        Returns the variable index of the node directly after the node
        corresponding to 'index' in 'assignment'.
        """
        return _pywrapcp.RoutingModel_Next(self, assignment, index)

    def IsVehicleUsed(self, assignment, vehicle):
        r"""Returns true if the route of 'vehicle' is non empty in 'assignment'."""
        return _pywrapcp.RoutingModel_IsVehicleUsed(self, assignment, vehicle)

    def NextVar(self, index):
        r"""
        Returns the next variable of the node corresponding to index. Note that
        NextVar(index) == index is equivalent to ActiveVar(index) == 0.
        """
        return _pywrapcp.RoutingModel_NextVar(self, index)

    def ActiveVar(self, index):
        r"""Returns the active variable of the node corresponding to index."""
        return _pywrapcp.RoutingModel_ActiveVar(self, index)

    def ActiveVehicleVar(self, vehicle):
        r"""
        Returns the active variable of the vehicle. It will be equal to 1 iff the
        route of the vehicle is not empty, 0 otherwise.
        """
        return _pywrapcp.RoutingModel_ActiveVehicleVar(self, vehicle)

    def VehicleRouteConsideredVar(self, vehicle):
        r"""
        Returns the variable specifying whether or not the given vehicle route is
        considered for costs and constraints. It will be equal to 1 iff the route
        of the vehicle is not empty OR vehicle_used_when_empty_[vehicle] is true.
        """
        return _pywrapcp.RoutingModel_VehicleRouteConsideredVar(self, vehicle)

    def VehicleVar(self, index):
        r"""
        Returns the vehicle variable of the node corresponding to index. Note that
        VehicleVar(index) == -1 is equivalent to ActiveVar(index) == 0.
        """
        return _pywrapcp.RoutingModel_VehicleVar(self, index)

    def ResourceVar(self, vehicle, resource_group):
        r"""
        Returns the resource variable for the given vehicle index in the given
        resource group. If a vehicle doesn't require a resource from the
        corresponding resource group, then ResourceVar(v, r_g) == -1.
        """
        return _pywrapcp.RoutingModel_ResourceVar(self, vehicle, resource_group)

    def CostVar(self):
        r"""Returns the global cost variable which is being minimized."""
        return _pywrapcp.RoutingModel_CostVar(self)

    def GetArcCostForVehicle(self, from_index, to_index, vehicle):
        r"""
        Returns the cost of the transit arc between two nodes for a given vehicle.
        Input are variable indices of node. This returns 0 if vehicle < 0.
        """
        return _pywrapcp.RoutingModel_GetArcCostForVehicle(self, from_index, to_index, vehicle)

    def CostsAreHomogeneousAcrossVehicles(self):
        r"""Whether costs are homogeneous across all vehicles."""
        return _pywrapcp.RoutingModel_CostsAreHomogeneousAcrossVehicles(self)

    def GetHomogeneousCost(self, from_index, to_index):
        r"""
        Returns the cost of the segment between two nodes supposing all vehicle
        costs are the same (returns the cost for the first vehicle otherwise).
        """
        return _pywrapcp.RoutingModel_GetHomogeneousCost(self, from_index, to_index)

    def GetArcCostForFirstSolution(self, from_index, to_index):
        r"""
        Returns the cost of the arc in the context of the first solution strategy.
        This is typically a simplification of the actual cost; see the .cc.
        """
        return _pywrapcp.RoutingModel_GetArcCostForFirstSolution(self, from_index, to_index)

    def GetArcCostForClass(self, from_index, to_index, cost_class_index):
        r"""
        Returns the cost of the segment between two nodes for a given cost
        class. Input are variable indices of nodes and the cost class.
        Unlike GetArcCostForVehicle(), if cost_class is kNoCost, then the
        returned cost won't necessarily be zero: only some of the components
        of the cost that depend on the cost class will be omited. See the code
        for details.
        """
        return _pywrapcp.RoutingModel_GetArcCostForClass(self, from_index, to_index, cost_class_index)

    def GetCostClassIndexOfVehicle(self, vehicle):
        r"""Get the cost class index of the given vehicle."""
        return _pywrapcp.RoutingModel_GetCostClassIndexOfVehicle(self, vehicle)

    def HasVehicleWithCostClassIndex(self, cost_class_index):
        r"""
        Returns true iff the model contains a vehicle with the given
        cost_class_index.
        """
        return _pywrapcp.RoutingModel_HasVehicleWithCostClassIndex(self, cost_class_index)

    def GetCostClassesCount(self):
        r"""Returns the number of different cost classes in the model."""
        return _pywrapcp.RoutingModel_GetCostClassesCount(self)

    def GetNonZeroCostClassesCount(self):
        r"""Ditto, minus the 'always zero', built-in cost class."""
        return _pywrapcp.RoutingModel_GetNonZeroCostClassesCount(self)

    def GetVehicleClassIndexOfVehicle(self, vehicle):
        return _pywrapcp.RoutingModel_GetVehicleClassIndexOfVehicle(self, vehicle)

    def GetVehicleOfClass(self, vehicle_class):
        r"""
        Returns a vehicle of the given vehicle class, and -1 if there are no
        vehicles for this class.
        """
        return _pywrapcp.RoutingModel_GetVehicleOfClass(self, vehicle_class)

    def GetVehicleClassesCount(self):
        r"""Returns the number of different vehicle classes in the model."""
        return _pywrapcp.RoutingModel_GetVehicleClassesCount(self)

    def GetSameVehicleIndicesOfIndex(self, node):
        r"""Returns variable indices of nodes constrained to be on the same route."""
        return _pywrapcp.RoutingModel_GetSameVehicleIndicesOfIndex(self, node)

    def GetSameActivityIndicesOfIndex(self, node):
        r"""Returns variable indices of nodes constrained to have the same activity."""
        return _pywrapcp.RoutingModel_GetSameActivityIndicesOfIndex(self, node)

    def GetSameActivityGroupOfIndex(self, node):
        r"""Returns the same activity group of the node."""
        return _pywrapcp.RoutingModel_GetSameActivityGroupOfIndex(self, node)

    def GetSameActivityGroupsCount(self):
        r"""Returns the number of same activity groups."""
        return _pywrapcp.RoutingModel_GetSameActivityGroupsCount(self)

    def GetSameActivityIndicesOfGroup(self, group):
        r"""Returns variable indices of nodes in the same activity group."""
        return _pywrapcp.RoutingModel_GetSameActivityIndicesOfGroup(self, group)

    def GetVehicleTypeContainer(self):
        return _pywrapcp.RoutingModel_GetVehicleTypeContainer(self)

    def ArcIsMoreConstrainedThanArc(self, _from, to1, to2):
        r"""
        Returns whether the arc from->to1 is more constrained than from->to2,
        taking into account, in order:
        - whether the destination node isn't an end node
        - whether the destination node is mandatory
        - whether the destination node is bound to the same vehicle as the source
        - the "primary constrained" dimension (see SetPrimaryConstrainedDimension)
        It then breaks ties using, in order:
        - the arc cost (taking unperformed penalties into account)
        - the size of the vehicle vars of "to1" and "to2" (lowest size wins)
        - the value: the lowest value of the indices to1 and to2 wins.
        See the .cc for details.
        The more constrained arc is typically preferable when building a
        first solution. This method is intended to be used as a callback for the
        BestValueByComparisonSelector value selector.
        Args:
          from: the variable index of the source node
          to1: the variable index of the first candidate destination node.
          to2: the variable index of the second candidate destination node.
        """
        return _pywrapcp.RoutingModel_ArcIsMoreConstrainedThanArc(self, _from, to1, to2)

    def DebugOutputAssignment(self, solution_assignment, dimension_to_print):
        r"""
        Print some debugging information about an assignment, including the
        feasible intervals of the CumulVar for dimension "dimension_to_print"
        at each step of the routes.
        If "dimension_to_print" is omitted, all dimensions will be printed.
        """
        return _pywrapcp.RoutingModel_DebugOutputAssignment(self, solution_assignment, dimension_to_print)

    def CheckIfAssignmentIsFeasible(self, assignment, call_at_solution_monitors):
        r"""
        Returns a vector cumul_bounds, for which cumul_bounds[i][j] is a pair
        containing the minimum and maximum of the CumulVar of the jth node on
        route i.
        - cumul_bounds[i][j].first is the minimum.
        - cumul_bounds[i][j].second is the maximum.
        Checks if an assignment is feasible.
        """
        return _pywrapcp.RoutingModel_CheckIfAssignmentIsFeasible(self, assignment, call_at_solution_monitors)

    def solver(self):
        r"""
        Returns the underlying constraint solver. Can be used to add extra
        constraints and/or modify search algorithms.
        """
        return _pywrapcp.RoutingModel_solver(self)

    def CheckLimit(self, *args):
        r"""
        Returns true if the search limit has been crossed with the given time
        offset.
        """
        return _pywrapcp.RoutingModel_CheckLimit(self, *args)

    def RemainingTime(self):
        r"""Returns the time left in the search limit."""
        return _pywrapcp.RoutingModel_RemainingTime(self)

    def UpdateTimeLimit(self, time_limit):
        r"""Updates the time limit of the search limit."""
        return _pywrapcp.RoutingModel_UpdateTimeLimit(self, time_limit)

    def TimeBuffer(self):
        r"""Returns the time buffer to safely return a solution."""
        return _pywrapcp.RoutingModel_TimeBuffer(self)

    def GetMutableCPSatInterrupt(self):
        r"""Returns the atomic<bool> to stop the CP-SAT solver."""
        return _pywrapcp.RoutingModel_GetMutableCPSatInterrupt(self)

    def GetMutableCPInterrupt(self):
        r"""Returns the atomic<bool> to stop the CP solver."""
        return _pywrapcp.RoutingModel_GetMutableCPInterrupt(self)

    def CancelSearch(self):
        r"""Cancels the current search."""
        return _pywrapcp.RoutingModel_CancelSearch(self)

    def nodes(self):
        r"""
        Sizes and indices
        Returns the number of nodes in the model.
        """
        return _pywrapcp.RoutingModel_nodes(self)

    def vehicles(self):
        r"""Returns the number of vehicle routes in the model."""
        return _pywrapcp.RoutingModel_vehicles(self)

    def Size(self):
        r"""Returns the number of next variables in the model."""
        return _pywrapcp.RoutingModel_Size(self)

    def GetNumberOfDecisionsInFirstSolution(self, search_parameters):
        r"""
        Returns statistics on first solution search, number of decisions sent to
        filters, number of decisions rejected by filters.
        """
        return _pywrapcp.RoutingModel_GetNumberOfDecisionsInFirstSolution(self, search_parameters)

    def GetNumberOfRejectsInFirstSolution(self, search_parameters):
        return _pywrapcp.RoutingModel_GetNumberOfRejectsInFirstSolution(self, search_parameters)

    def GetAutomaticFirstSolutionStrategy(self):
        r"""Returns the automatic first solution strategy selected."""
        return _pywrapcp.RoutingModel_GetAutomaticFirstSolutionStrategy(self)

    def IsMatchingModel(self):
        r"""Returns true if a vehicle/node matching problem is detected."""
        return _pywrapcp.RoutingModel_IsMatchingModel(self)

    def AreRoutesInterdependent(self, parameters):
        r"""
        Returns true if routes are interdependent. This means that any
        modification to a route might impact another.
        """
        return _pywrapcp.RoutingModel_AreRoutesInterdependent(self, parameters)

    def MakeGuidedSlackFinalizer(self, dimension, initializer):
        r"""
        The next few members are in the public section only for testing purposes.

        MakeGuidedSlackFinalizer creates a DecisionBuilder for the slacks of a
        dimension using a callback to choose which values to start with.
        The finalizer works only when all next variables in the model have
        been fixed. It has the following two characteristics:
        1. It follows the routes defined by the nexts variables when choosing a
           variable to make a decision on.
        2. When it comes to choose a value for the slack of node i, the decision
           builder first calls the callback with argument i, and supposingly the
           returned value is x it creates decisions slack[i] = x, slack[i] = x +
           1, slack[i] = x - 1, slack[i] = x + 2, etc.
        """
        return _pywrapcp.RoutingModel_MakeGuidedSlackFinalizer(self, dimension, initializer)

    def MakeSelfDependentDimensionFinalizer(self, dimension):
        r"""
        MakeSelfDependentDimensionFinalizer is a finalizer for the slacks of a
        self-dependent dimension. It makes an extensive use of the caches of the
        state dependent transits.
        In detail, MakeSelfDependentDimensionFinalizer returns a composition of a
        local search decision builder with a greedy descent operator for the cumul
        of the start of each route and a guided slack finalizer. Provided there
        are no time windows and the maximum slacks are large enough, once the
        cumul of the start of route is fixed, the guided finalizer can find
        optimal values of the slacks for the rest of the route in time
        proportional to the length of the route. Therefore the composed finalizer
        generally works in time O(log(t)*n*m), where t is the latest possible
        departute time, n is the number of nodes in the network and m is the
        number of vehicles.
        """
        return _pywrapcp.RoutingModel_MakeSelfDependentDimensionFinalizer(self, dimension)

    def GetPathsMetadata(self):
        return _pywrapcp.RoutingModel_GetPathsMetadata(self)

    def GetVehiclesOfSameClass(self, start_end_index):
        r"""
        Returns indices of the vehicles which are in the same vehicle class as the
        vehicle starting or ending at start_end_index.
        """
        return _pywrapcp.RoutingModel_GetVehiclesOfSameClass(self, start_end_index)

    def GetSameVehicleClassArcs(self, from_index, to_index):
        r"""
        Returns all arcs which are equivalent to the {from_index, to_index} arc
        wrt vehicle classes. Arcs will be returned only if from_index is the
        start of a vehicle or if to_index is the end of a vehicle. The returned
        arcs will then be starting or ending at start or end nodes of vehicles in
        the same vehicle class. The input arc is included in the returned vector.
        """
        return _pywrapcp.RoutingModel_GetSameVehicleClassArcs(self, from_index, to_index)

# Register RoutingModel in _pywrapcp:
_pywrapcp.RoutingModel_swigregister(RoutingModel)
cvar = _pywrapcp.cvar
RoutingModel.kNoPenalty = _pywrapcp.cvar.RoutingModel_kNoPenalty
RoutingModel.kNoDisjunction = _pywrapcp.cvar.RoutingModel_kNoDisjunction
RoutingModel.kNoDimension = _pywrapcp.cvar.RoutingModel_kNoDimension

class RoutingModelVisitor(BaseObject):
    r"""Routing model visitor."""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self):
        _pywrapcp.RoutingModelVisitor_swiginit(self, _pywrapcp.new_RoutingModelVisitor())
    __swig_destroy__ = _pywrapcp.delete_RoutingModelVisitor

# Register RoutingModelVisitor in _pywrapcp:
_pywrapcp.RoutingModelVisitor_swigregister(RoutingModelVisitor)
RoutingModelVisitor.kLightElement = _pywrapcp.cvar.RoutingModelVisitor_kLightElement
RoutingModelVisitor.kLightElement2 = _pywrapcp.cvar.RoutingModelVisitor_kLightElement2
RoutingModelVisitor.kRemoveValues = _pywrapcp.cvar.RoutingModelVisitor_kRemoveValues

class GlobalVehicleBreaksConstraint(Constraint):
    r"""
    GlobalVehicleBreaksConstraint ensures breaks constraints are enforced on
    all vehicles in the dimension passed to its constructor.
    It is intended to be used for dimensions representing time.
    A break constraint ensures break intervals fit on the route of a vehicle.
    For a given vehicle, it forces break intervals to be disjoint from visit
    intervals, where visit intervals start at CumulVar(node) and last for
    node_visit_transit[node]. Moreover, it ensures that there is enough time
    between two consecutive nodes of a route to do transit and vehicle breaks,
    i.e. if Next(nodeA) = nodeB, CumulVar(nodeA) = tA and CumulVar(nodeB) = tB,
    then SlackVar(nodeA) >= sum_{breaks [tA, tB)} duration(break).
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, dimension):
        _pywrapcp.GlobalVehicleBreaksConstraint_swiginit(self, _pywrapcp.new_GlobalVehicleBreaksConstraint(dimension))

    def DebugString(self):
        return _pywrapcp.GlobalVehicleBreaksConstraint_DebugString(self)

    def Post(self):
        return _pywrapcp.GlobalVehicleBreaksConstraint_Post(self)

    def InitialPropagateWrapper(self):
        return _pywrapcp.GlobalVehicleBreaksConstraint_InitialPropagateWrapper(self)
    __swig_destroy__ = _pywrapcp.delete_GlobalVehicleBreaksConstraint

# Register GlobalVehicleBreaksConstraint in _pywrapcp:
_pywrapcp.GlobalVehicleBreaksConstraint_swigregister(GlobalVehicleBreaksConstraint)
class TypeRegulationsChecker(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined - class is abstract")
    __repr__ = _swig_repr
    __swig_destroy__ = _pywrapcp.delete_TypeRegulationsChecker

    def CheckVehicle(self, vehicle, next_accessor):
        return _pywrapcp.TypeRegulationsChecker_CheckVehicle(self, vehicle, next_accessor)

# Register TypeRegulationsChecker in _pywrapcp:
_pywrapcp.TypeRegulationsChecker_swigregister(TypeRegulationsChecker)
class TypeIncompatibilityChecker(TypeRegulationsChecker):
    r"""Checker for type incompatibilities."""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, model, check_hard_incompatibilities):
        _pywrapcp.TypeIncompatibilityChecker_swiginit(self, _pywrapcp.new_TypeIncompatibilityChecker(model, check_hard_incompatibilities))
    __swig_destroy__ = _pywrapcp.delete_TypeIncompatibilityChecker

# Register TypeIncompatibilityChecker in _pywrapcp:
_pywrapcp.TypeIncompatibilityChecker_swigregister(TypeIncompatibilityChecker)
class TypeRequirementChecker(TypeRegulationsChecker):
    r"""Checker for type requirements."""

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, model):
        _pywrapcp.TypeRequirementChecker_swiginit(self, _pywrapcp.new_TypeRequirementChecker(model))
    __swig_destroy__ = _pywrapcp.delete_TypeRequirementChecker

# Register TypeRequirementChecker in _pywrapcp:
_pywrapcp.TypeRequirementChecker_swigregister(TypeRequirementChecker)
class TypeRegulationsConstraint(Constraint):
    r"""
    The following constraint ensures that incompatibilities and requirements
    between types are respected.

    It verifies both "hard" and "temporal" incompatibilities.
    Two nodes with hard incompatible types cannot be served by the same vehicle
    at all, while with a temporal incompatibility they can't be on the same
    route at the same time.
    The VisitTypePolicy of a node determines how visiting it impacts the type
    count on the route.

    For example, for
    - three temporally incompatible types T1 T2 and T3
    - 2 pairs of nodes a1/r1 and a2/r2 of type T1 and T2 respectively, with
        - a1 and a2 of VisitTypePolicy TYPE_ADDED_TO_VEHICLE
        - r1 and r2 of policy ADDED_TYPE_REMOVED_FROM_VEHICLE
    - 3 nodes A, UV and AR of type T3, respectively with type policies
      TYPE_ADDED_TO_VEHICLE, TYPE_ON_VEHICLE_UP_TO_VISIT and
      TYPE_SIMULTANEOUSLY_ADDED_AND_REMOVED
    the configurations
    UV --> a1 --> r1 --> a2 --> r2,   a1 --> r1 --> a2 --> r2 --> A and
    a1 --> r1 --> AR --> a2 --> r2 are acceptable, whereas the configurations
    a1 --> a2 --> r1 --> ..., or A --> a1 --> r1 --> ..., or
    a1 --> r1 --> UV --> ... are not feasible.

    It also verifies same-vehicle and temporal type requirements.
    A node of type T_d with a same-vehicle requirement for type T_r needs to be
    served by the same vehicle as a node of type T_r.
    Temporal requirements, on the other hand, can take effect either when the
    dependent type is being added to the route or when it's removed from it,
    which is determined by the dependent node's VisitTypePolicy.
    In the above example:
    - If T3 is required on the same vehicle as T1, A, AR or UV must be on the
      same vehicle as a1.
    - If T2 is required when adding T1, a2 must be visited *before* a1, and if
      r2 is also visited on the route, it must be *after* a1, i.e. T2 must be on
      the vehicle when a1 is visited:
      ... --> a2 --> ... --> a1 --> ... --> r2 --> ...
    - If T3 is required when removing T1, T3 needs to be on the vehicle when
      r1 is visited:
      ... --> A --> ... --> r1 --> ...   OR   ... --> r1 --> ... --> UV --> ...
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, model):
        _pywrapcp.TypeRegulationsConstraint_swiginit(self, _pywrapcp.new_TypeRegulationsConstraint(model))

    def Post(self):
        return _pywrapcp.TypeRegulationsConstraint_Post(self)

    def InitialPropagateWrapper(self):
        return _pywrapcp.TypeRegulationsConstraint_InitialPropagateWrapper(self)
    __swig_destroy__ = _pywrapcp.delete_TypeRegulationsConstraint

# Register TypeRegulationsConstraint in _pywrapcp:
_pywrapcp.TypeRegulationsConstraint_swigregister(TypeRegulationsConstraint)
class BoundCost(object):
    r"""
    A structure meant to store soft bounds and associated violation constants.
    It is 'Simple' because it has one BoundCost per element,
    in contrast to 'Multiple'. Design notes:
    - it is meant to store model information to be shared through pointers,
      so it disallows copy and assign to avoid accidental duplication.
    - it keeps soft bounds as an array of structs to help cache,
      because code that uses such bounds typically use both bound and cost.
    - soft bounds are named pairs, prevents some mistakes.
    - using operator[] to access elements is not interesting,
      because the structure will be accessed through pointers, moreover having
      to type bound_cost reminds the user of the order if they do a copy
      assignment of the element.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr
    bound = property(_pywrapcp.BoundCost_bound_get, _pywrapcp.BoundCost_bound_set)
    cost = property(_pywrapcp.BoundCost_cost_get, _pywrapcp.BoundCost_cost_set)

    def __init__(self, *args):
        _pywrapcp.BoundCost_swiginit(self, _pywrapcp.new_BoundCost(*args))
    __swig_destroy__ = _pywrapcp.delete_BoundCost

# Register BoundCost in _pywrapcp:
_pywrapcp.BoundCost_swigregister(BoundCost)
class SimpleBoundCosts(object):
    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")
    __repr__ = _swig_repr

    def __init__(self, num_bounds, default_bound_cost):
        _pywrapcp.SimpleBoundCosts_swiginit(self, _pywrapcp.new_SimpleBoundCosts(num_bounds, default_bound_cost))

    def bound_cost(self, element):
        return _pywrapcp.SimpleBoundCosts_bound_cost(self, element)

    def size(self):
        return _pywrapcp.SimpleBoundCosts_size(self)
    __swig_destroy__ = _pywrapcp.delete_SimpleBoundCosts

# Register SimpleBoundCosts in _pywrapcp:
_pywrapcp.SimpleBoundCosts_swigregister(SimpleBoundCosts)
class RoutingDimension(object):
    r"""
    Dimensions represent quantities accumulated at nodes along the routes. They
    represent quantities such as weights or volumes carried along the route, or
    distance or times.

    Quantities at a node are represented by "cumul" variables and the increase
    or decrease of quantities between nodes are represented by "transit"
    variables. These variables are linked as follows:

    if j == next(i),
    cumuls(j) = cumuls(i) + transits(i) + slacks(i) +
                state_dependent_transits(i)

    where slack is a positive slack variable (can represent waiting times for
    a time dimension), and state_dependent_transits is a non-purely functional
    version of transits_. Favour transits over state_dependent_transits when
    possible, because purely functional callbacks allow more optimisations and
    make the model faster and easier to solve.
    for a given vehicle, it is passed as an external vector, it would be better
    to have this information here.
    """

    thisown = property(lambda x: x.this.own(), lambda x, v: x.this.own(v), doc="The membership flag")

    def __init__(self, *args, **kwargs):
        raise AttributeError("No constructor defined")
    __repr__ = _swig_repr
    __swig_destroy__ = _pywrapcp.delete_RoutingDimension

    def model(self):
        r"""Returns the model on which the dimension was created."""
        return _pywrapcp.RoutingDimension_model(self)

    def GetTransitValue(self, from_index, to_index, vehicle):
        r"""
        Returns the transition value for a given pair of nodes (as var index);
        this value is the one taken by the corresponding transit variable when
        the 'next' variable for 'from_index' is bound to 'to_index'.
        """
        return _pywrapcp.RoutingDimension_GetTransitValue(self, from_index, to_index, vehicle)

    def GetTransitValueFromClass(self, from_index, to_index, vehicle_class):
        r"""
        Same as above but taking a vehicle class of the dimension instead of a
        vehicle (the class of a vehicle can be obtained with vehicle_to_class()).
        """
        return _pywrapcp.RoutingDimension_GetTransitValueFromClass(self, from_index, to_index, vehicle_class)

    def CumulVar(self, index):
        r"""
        Get the cumul, transit and slack variables for the given node (given as
        int64_t var index).
        """
        return _pywrapcp.RoutingDimension_CumulVar(self, index)

    def TransitVar(self, index):
        return _pywrapcp.RoutingDimension_TransitVar(self, index)

    def FixedTransitVar(self, index):
        return _pywrapcp.RoutingDimension_FixedTransitVar(self, index)

    def SlackVar(self, index):
        return _pywrapcp.RoutingDimension_SlackVar(self, index)

    def SetCumulVarRange(self, index, min, max):
        r"""
        Some functions to allow users to use the interface without knowing about
        the underlying CP model.
        Restricts the range of the cumul variable associated to index.
        """
        return _pywrapcp.RoutingDimension_SetCumulVarRange(self, index, min, max)

    def GetCumulVarMin(self, index):
        r"""Gets the current minimum of the cumul variable associated to index."""
        return _pywrapcp.RoutingDimension_GetCumulVarMin(self, index)

    def GetCumulVarMax(self, index):
        r"""Gets the current maximum of the cumul variable associated to index."""
        return _pywrapcp.RoutingDimension_GetCumulVarMax(self, index)

    def SetSpanUpperBoundForVehicle(self, upper_bound, vehicle):
        r"""
        Sets an upper bound on the dimension span on a given vehicle. This is the
        preferred way to limit the "length" of the route of a vehicle according to
        a dimension.
        """
        return _pywrapcp.RoutingDimension_SetSpanUpperBoundForVehicle(self, upper_bound, vehicle)

    def SetSpanCostCoefficientForVehicle(self, coefficient, vehicle):
        r"""
        Sets a cost proportional to the dimension span on a given vehicle,
        or on all vehicles at once. "coefficient" must be nonnegative.
        This is handy to model costs proportional to idle time when the dimension
        represents time.
        The cost for a vehicle is
          span_cost = coefficient * (dimension end value - dimension start value).
        """
        return _pywrapcp.RoutingDimension_SetSpanCostCoefficientForVehicle(self, coefficient, vehicle)

    def SetSpanCostCoefficientForAllVehicles(self, coefficient):
        return _pywrapcp.RoutingDimension_SetSpanCostCoefficientForAllVehicles(self, coefficient)

    def SetSlackCostCoefficientForVehicle(self, coefficient, vehicle):
        r"""
        Sets a cost proportional to the dimension total slack on a given vehicle,
        or on all vehicles at once. "coefficient" must be nonnegative.
        This is handy to model costs only proportional to idle time when the
        dimension represents time.
        The cost for a vehicle is
          slack_cost = coefficient *
                (dimension end value - dimension start value - total_transit).
        """
        return _pywrapcp.RoutingDimension_SetSlackCostCoefficientForVehicle(self, coefficient, vehicle)

    def SetSlackCostCoefficientForAllVehicles(self, coefficient):
        return _pywrapcp.RoutingDimension_SetSlackCostCoefficientForAllVehicles(self, coefficient)

    def SetGlobalSpanCostCoefficient(self, coefficient):
        r"""
        Sets a cost proportional to the *global* dimension span, that is the
        difference between the largest value of route end cumul variables and
        the smallest value of route start cumul variables.
        In other words:
        global_span_cost =
          coefficient * (Max(dimension end value) - Min(dimension start value)).
        """
        return _pywrapcp.RoutingDimension_SetGlobalSpanCostCoefficient(self, coefficient)

    def SetCumulVarSoftUpperBound(self, index, upper_bound, coefficient):
        r"""
        Sets a soft upper bound to the cumul variable of a given variable index.
        If the value of the cumul variable is greater than the bound, a cost
        proportional to the difference between this value and the bound is added
        to the cost function of the model:
          cumulVar <= upper_bound -> cost = 0
           cumulVar > upper_bound -> cost = coefficient * (cumulVar - upper_bound)
        This is also handy to model tardiness costs when the dimension represents
        time.
        """
        return _pywrapcp.RoutingDimension_SetCumulVarSoftUpperBound(self, index, upper_bound, coefficient)

    def HasCumulVarSoftUpperBound(self, index):
        r"""
        Returns true if a soft upper bound has been set for a given variable
        index.
        """
        return _pywrapcp.RoutingDimension_HasCumulVarSoftUpperBound(self, index)

    def GetCumulVarSoftUpperBound(self, index):
        r"""
        Returns the soft upper bound of a cumul variable for a given variable
        index. The "hard" upper bound of the variable is returned if no soft upper
        bound has been set.
        """
        return _pywrapcp.RoutingDimension_GetCumulVarSoftUpperBound(self, index)

    def GetCumulVarSoftUpperBoundCoefficient(self, index):
        r"""
        Returns the cost coefficient of the soft upper bound of a cumul variable
        for a given variable index. If no soft upper bound has been set, 0 is
        returned.
        """
        return _pywrapcp.RoutingDimension_GetCumulVarSoftUpperBoundCoefficient(self, index)

    def SetCumulVarSoftLowerBound(self, index, lower_bound, coefficient):
        r"""
        Sets a soft lower bound to the cumul variable of a given variable index.
        If the value of the cumul variable is less than the bound, a cost
        proportional to the difference between this value and the bound is added
        to the cost function of the model:
          cumulVar > lower_bound -> cost = 0
          cumulVar <= lower_bound -> cost = coefficient * (lower_bound -
                      cumulVar).
        This is also handy to model earliness costs when the dimension represents
        time.
        """
        return _pywrapcp.RoutingDimension_SetCumulVarSoftLowerBound(self, index, lower_bound, coefficient)

    def HasCumulVarSoftLowerBound(self, index):
        r"""
        Returns true if a soft lower bound has been set for a given variable
        index.
        """
        return _pywrapcp.RoutingDimension_HasCumulVarSoftLowerBound(self, index)

    def GetCumulVarSoftLowerBound(self, index):
        r"""
        Returns the soft lower bound of a cumul variable for a given variable
        index. The "hard" lower bound of the variable is returned if no soft lower
        bound has been set.
        """
        return _pywrapcp.RoutingDimension_GetCumulVarSoftLowerBound(self, index)

    def GetCumulVarSoftLowerBoundCoefficient(self, index):
        r"""
        Returns the cost coefficient of the soft lower bound of a cumul variable
        for a given variable index. If no soft lower bound has been set, 0 is
        returned.
        """
        return _pywrapcp.RoutingDimension_GetCumulVarSoftLowerBoundCoefficient(self, index)

    def SetBreakIntervalsOfVehicle(self, breaks, vehicle, node_visit_transits):
        r"""
        Sets the breaks for a given vehicle. Breaks are represented by
        IntervalVars. They may interrupt transits between nodes and increase
        the value of corresponding slack variables.
        A break may take place before the start of a vehicle, after the end of
        a vehicle, or during a travel i -> j.

        In that case, the interval [break.Start(), break.End()) must be a subset
        of [CumulVar(i) + pre_travel(i, j), CumulVar(j) - post_travel(i, j)). In
        other words, a break may not overlap any node n's visit, given by
        [CumulVar(n) - post_travel(_, n), CumulVar(n) + pre_travel(n, _)).
        This formula considers post_travel(_, start) and pre_travel(end, _) to be
        0; pre_travel will never be called on any (_, start) and post_travel will
        never we called on any (end, _). If pre_travel_evaluator or
        post_travel_evaluator is -1, it will be taken as a function that always
        returns 0.
        Deprecated, sets pre_travel(i, j) = node_visit_transit[i].
        """
        return _pywrapcp.RoutingDimension_SetBreakIntervalsOfVehicle(self, breaks, vehicle, node_visit_transits)

    def SetBreakDistanceDurationOfVehicle(self, distance, duration, vehicle):
        r"""
        With breaks supposed to be consecutive, this forces the distance between
        breaks of size at least minimum_break_duration to be at most distance.
        This supposes that the time until route start and after route end are
        infinite breaks.
        """
        return _pywrapcp.RoutingDimension_SetBreakDistanceDurationOfVehicle(self, distance, duration, vehicle)

    def InitializeBreaks(self):
        r"""
        Sets up vehicle_break_intervals_, vehicle_break_distance_duration_,
        pre_travel_evaluators and post_travel_evaluators.
        """
        return _pywrapcp.RoutingDimension_InitializeBreaks(self)

    def HasBreakConstraints(self):
        r"""Returns true if any break interval or break distance was defined."""
        return _pywrapcp.RoutingDimension_HasBreakConstraints(self)

    def GetPreTravelEvaluatorOfVehicle(self, vehicle):
        return _pywrapcp.RoutingDimension_GetPreTravelEvaluatorOfVehicle(self, vehicle)

    def GetPostTravelEvaluatorOfVehicle(self, vehicle):
        return _pywrapcp.RoutingDimension_GetPostTravelEvaluatorOfVehicle(self, vehicle)

    def base_dimension(self):
        r"""Returns the parent in the dependency tree if any or nullptr otherwise."""
        return _pywrapcp.RoutingDimension_base_dimension(self)

    def ShortestTransitionSlack(self, node):
        r"""
        It makes sense to use the function only for self-dependent dimension.
        For such dimensions the value of the slack of a node determines the
        transition cost of the next transit. Provided that
          1. cumul[node] is fixed,
          2. next[node] and next[next[node]] (if exists) are fixed,
        the value of slack[node] for which cumul[next[node]] + transit[next[node]]
        is minimized can be found in O(1) using this function.
        """
        return _pywrapcp.RoutingDimension_ShortestTransitionSlack(self, node)

    def name(self):
        r"""Returns the name of the dimension."""
        return _pywrapcp.RoutingDimension_name(self)

    def SetPickupToDeliveryLimitFunctionForPair(self, limit_function, pair_index):
        return _pywrapcp.RoutingDimension_SetPickupToDeliveryLimitFunctionForPair(self, limit_function, pair_index)

    def HasPickupToDeliveryLimits(self):
        return _pywrapcp.RoutingDimension_HasPickupToDeliveryLimits(self)

    def AddNodePrecedence(self, first_node, second_node, offset):
        return _pywrapcp.RoutingDimension_AddNodePrecedence(self, first_node, second_node, offset)

    def GetSpanUpperBoundForVehicle(self, vehicle):
        return _pywrapcp.RoutingDimension_GetSpanUpperBoundForVehicle(self, vehicle)

    def GetSpanCostCoefficientForVehicle(self, vehicle):
        return _pywrapcp.RoutingDimension_GetSpanCostCoefficientForVehicle(self, vehicle)

    def GetSlackCostCoefficientForVehicle(self, vehicle):
        return _pywrapcp.RoutingDimension_GetSlackCostCoefficientForVehicle(self, vehicle)

    def global_span_cost_coefficient(self):
        return _pywrapcp.RoutingDimension_global_span_cost_coefficient(self)

    def GetGlobalOptimizerOffset(self):
        return _pywrapcp.RoutingDimension_GetGlobalOptimizerOffset(self)

    def GetLocalOptimizerOffsetForVehicle(self, vehicle):
        return _pywrapcp.RoutingDimension_GetLocalOptimizerOffsetForVehicle(self, vehicle)

    def SetSoftSpanUpperBoundForVehicle(self, bound_cost, vehicle):
        r"""
        If the span of vehicle on this dimension is larger than bound,
        the cost will be increased by cost * (span - bound).
        """
        return _pywrapcp.RoutingDimension_SetSoftSpanUpperBoundForVehicle(self, bound_cost, vehicle)

    def HasSoftSpanUpperBounds(self):
        return _pywrapcp.RoutingDimension_HasSoftSpanUpperBounds(self)

    def GetSoftSpanUpperBoundForVehicle(self, vehicle):
        return _pywrapcp.RoutingDimension_GetSoftSpanUpperBoundForVehicle(self, vehicle)

    def SetQuadraticCostSoftSpanUpperBoundForVehicle(self, bound_cost, vehicle):
        r"""
        If the span of vehicle on this dimension is larger than bound,
        the cost will be increased by cost * (span - bound)^2.
        """
        return _pywrapcp.RoutingDimension_SetQuadraticCostSoftSpanUpperBoundForVehicle(self, bound_cost, vehicle)

    def HasQuadraticCostSoftSpanUpperBounds(self):
        return _pywrapcp.RoutingDimension_HasQuadraticCostSoftSpanUpperBounds(self)

    def GetQuadraticCostSoftSpanUpperBoundForVehicle(self, vehicle):
        return _pywrapcp.RoutingDimension_GetQuadraticCostSoftSpanUpperBoundForVehicle(self, vehicle)

# Register RoutingDimension in _pywrapcp:
_pywrapcp.RoutingDimension_swigregister(RoutingDimension)

def SolveModelWithSat(model, search_parameters, initial_solution, solution):
    r"""
    Attempts to solve the model using the cp-sat solver. As of 5/2019, will
    solve the TSP corresponding to the model if it has a single vehicle.
    Therefore the resulting solution might not actually be feasible. Will return
    false if a solution could not be found.
    """
    return _pywrapcp.SolveModelWithSat(model, search_parameters, initial_solution, solution)

